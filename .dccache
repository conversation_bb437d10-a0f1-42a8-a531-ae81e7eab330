{"/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/linux/main.cc": [180, 1724295464430.9146, "ea23f7cdf74091d63bba29ef60486370b766157b848f0cfe62bd8b1c1695a0c1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/linux/my_application.cc": [4420, 1724295519949.5486, "28785b58e09991ba224922fa75a39ca3991274142a8a4ce7c1da63d582768c55"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/linux/my_application.h": [388, 1724295464431.249, "a044da4b485c93d0e1a79c62356b22c8856dd2bab537ffb6a9446fc905131316"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/web/index.html": [3147, 1736407919077.9229, "81185b5361eef9f3c3999b1b78d331b62344681dc0036a5fe4b2721c3e7e6959"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/ios/Runner/AppDelegate.swift": [2156, 1733973973284.67, "6d67034e5a214d77e76465f0cadf474b9292c04c197693b568d22a6bbee7a040"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/ios/Runner/Runner-Bridging-Header.h": [38, 1724295464365.4192, "1a8d11d01d1cc92c650c9c9ed35299240052d4683a15a719a2315ef44185e074"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/ios/RunnerTests/RunnerTests.swift": [285, 1724295464365.633, "5e8f4908dacea6f29b2a790e4d38bf5fded69096063f9a27a2f21584d62b30c0"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/linux/flutter/generated_plugin_registrant.cc": [1614, 1738815276683.1682, "b5c845f6729e180a9a3dfbe37cf64ee3a6728c84951e0c1134949f48d050796f"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/linux/flutter/generated_plugin_registrant.h": [303, 1738815276679.5474, "00f93bb5b1faefdede8f6065df327522900fb0b8efee86bacf424fd947ed14bb"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/macos/Flutter/GeneratedPluginRegistrant.swift": [2611, 1738815276688.564, "8c6e10640ee2d0e4ccbf0256ff72be32aac1085d295fd6b61d32bc81342d8aca"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/macos/Runner/AppDelegate.swift": [214, 1724295464436.361, "41f9f536f420b84027a13fc061f99e95951504f67dae394aec4e512b979267c7"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/macos/Runner/MainFlutterWindow.swift": [388, 1724295464453.1162, "65c9613c11bcedfa51416b16c975d8ba6ff12b405fc19d60db8755d92e86d9fe"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/macos/RunnerTests/RunnerTests.swift": [290, 1724295519952.3313, "6bc180bc77c7def7a21d197233e25dff42cc493ab9d294632edafc18fdf0ac44"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/flutter/generated_plugin_registrant.cc": [2419, 1738815276696.646, "27c90584508bc90ccde4342a0517091ac41e088db56839323e6af7b3b23b354e"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/flutter/generated_plugin_registrant.h": [302, 1738815276692.8833, "dbc11635d7b1ba1887adf40df5b83d634e9c3ab0bee34510c71528bb4d73602a"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/flutter_window.cpp": [2122, 1724295464472.8203, "dd185884c0b5301d400b58f12ebb36b1c8b326531597e531b40e44a6eeb0e943"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/flutter_window.h": [928, 1724295464473.0344, "38e0fcd3a0b83895852ec295cb70d90179c0dcd40d58f065eb5232a4ecd84898"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/main.cpp": [1259, 1724295519966.0166, "a574ecb69724f4fe24ca2b97282ea4db85b5ca90dd990f08267b9e4e4d57c19f"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/resource.h": [432, 1724295464476.557, "1fa98588b04561590ba1cf0521ab86a9db4a878312f21c9d5861d965ddd8d1ae"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/utils.cpp": [1797, 1724295519966.2117, "dc2ee94d43116f959a4abd42557f71d07acc631abbffe03b11489dbeed35bedb"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/utils.h": [672, 1724295464479.5132, "95a7f7dddb832997cefa937a96f89472332277e75734a5de553610760b744b03"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/win32_window.cpp": [8534, 1724295464479.8916, "9065f31ccf746cf8b390121f958f9e959033af864aed779597066625ed56bc36"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/windows/runner/win32_window.h": [3522, 1724295464480.0579, "7dd03746b19dcf3542829b2f6e8ed40a2714ce5f2cb3b0e23f0118a2fcf62d62"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/debug/AndroidManifest.xml": [416, 1732682613856.317, "c6b717f6d5ca4fd06c3ffefb68a15195a1b57fa1f497cbbd98bb0339eadaac82"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/profile/AndroidManifest.xml": [378, 1724295464158.4644, "30b0832ed8b92dfbd6ae2abfa939ccf26333d37600056b1572bd4a397a18a74b"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/AndroidManifest.xml": [8284, 1736407919018.3403, "02fc4b7ea2511826fab1d602b12da4d68714d1e7a3ddf02b66e34d5a3e7bae0f"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/drawable/launch_background.xml": [224, 1737190131780.2205, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/drawable-v21/launch_background.xml": [224, 1737190131781.8826, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/values/strings.xml": [327, 1733973810641.999, "2ee9bd88f40bea23a4faa3b02bebad8db87332eff37e471fe9a2b9838238b8c2"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/values/styles.xml": [1265, 1737190131800.6548, "92577b5b03b0092dcf299293386819ae5a63ba605f8dbc2c70954cda9dc12f2e"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/values-night/styles.xml": [1264, 1737190131807.4797, "359b76f252fe3d3a32950c2ba219d91e5fe07c10bed8365830add0648956d738"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/values-v31/styles.xml": [1057, 1737190131793.465, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_dev/res/values-night-v31/styles.xml": [1056, 1737190131797.5725, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/drawable/launch_background.xml": [224, 1737190132121.477, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/drawable-v21/launch_background.xml": [224, 1737190132122.8447, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/values-night/styles.xml": [1264, 1737190132133.1443, "359b76f252fe3d3a32950c2ba219d91e5fe07c10bed8365830add0648956d738"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/values/strings.xml": [321, 1733973810643.3027, "0ed8c2ed4b18c449414b2c1bbfd80bb68a14f92a6d89110fa27a06bd88ac9541"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/values/styles.xml": [1265, 1737190132130.9885, "92577b5b03b0092dcf299293386819ae5a63ba605f8dbc2c70954cda9dc12f2e"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/values-night-v31/styles.xml": [1056, 1737190132127.833, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/aam_prod/res/values-v31/styles.xml": [1057, 1737190132125.525, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/drawable-v21/launch_background.xml": [224, 1736906935649.8206, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/drawable/launch_background.xml": [224, 1736906935651.4683, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/values/styles.xml": [1267, 1736867139984.0032, "445e293aee32abfc4d93ca8f7c1979492b26d2de7b38314a9f08776c5813d98d"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/values-night/styles.xml": [1266, 1736867139987.0806, "11ed372ab62bc04bb47e678f9ec10a55849a9e1378ba4443b4d65baed96ad0af"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/values-night-v31/styles.xml": [1131, 1736906935652.5823, "924d70d349799fe092f65a60d5af5f393784dc440f3e5348be388053d99f92e2"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/res/values-v31/styles.xml": [1132, 1736906935653.7893, "2df6e2bad95eac9724782b0f7ea97245197ee376a65c1ec9a06cb7d03322239e"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/drawable/launch_background.xml": [224, 1737190131994.0337, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/drawable-v21/launch_background.xml": [224, 1737190131998.0894, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/values/strings.xml": [329, 1733973810644.8293, "a3d78d00c83feb501bdaf534ef4c1c4a8f34697485ac9bd1d16e27b5a1ad801e"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/values/styles.xml": [1254, 1737190132013.6196, "14159d34d94728c675428ba41441180c0a3ada614e1d5d9c2c7770cb352d8e46"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/values-night/styles.xml": [1253, 1737190132018.3547, "a4714b502d520917f5e1ebfa131fac8c3a7cc8a68080da37f7cb4be00e94a6e4"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/values-night-v31/styles.xml": [1056, 1737190132009.0671, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_dev/res/values-v31/styles.xml": [1057, 1737190132002.1614, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/drawable-v21/launch_background.xml": [224, 1737190132166.5815, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/drawable/launch_background.xml": [224, 1737190132165.0786, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/values/strings.xml": [323, 1734947071542.7498, "cc0f2c23d0324d9da06ede8d976dc0ac8799fb957b876157b9a9050e1cc71684"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/values/styles.xml": [1254, 1737190132173.1667, "14159d34d94728c675428ba41441180c0a3ada614e1d5d9c2c7770cb352d8e46"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/values-night/styles.xml": [1253, 1737190132175.1958, "a4714b502d520917f5e1ebfa131fac8c3a7cc8a68080da37f7cb4be00e94a6e4"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/values-night-v31/styles.xml": [1056, 1737190132170.5283, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rafco_prod/res/values-v31/styles.xml": [1057, 1737190132168.0232, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/drawable/launch_background.xml": [224, 1737190132061.6636, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/drawable-v21/launch_background.xml": [224, 1737190132065.6213, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/values/strings.xml": [325, 1733973810646.066, "a697beb3e1b9c3ff8c0dac4c1a32a50f13111d4cd438772d47852ec10c00cb6f"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/values/styles.xml": [1263, 1737190132073.8916, "de83c58cf6c4603155bf64cce172d5d969dfcdba9c32cef35188577c33121757"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/values-night/styles.xml": [1262, 1737190132076.2112, "07fd01ccc6c1b064c71b817e5b550bc0ba5c0d65ee487f6bfd1bb5eb30ccd7e2"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/values-night-v31/styles.xml": [1056, 1737190132071.684, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_dev/res/values-v31/styles.xml": [1057, 1737190132068.623, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/drawable/launch_background.xml": [224, 1737190132209.1506, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/drawable-v21/launch_background.xml": [224, 1737190132210.5464, "ef1a461a1df0cce2a5e848a482f7d664bb93802f732df84109c56343608978f1"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/values/strings.xml": [319, 1734947146484.7068, "581c12af884b8b27563d3052bee71a1a4473fb3dd92cfd727922b4265dadf339"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/values/styles.xml": [1263, 1737190132216.858, "de83c58cf6c4603155bf64cce172d5d969dfcdba9c32cef35188577c33121757"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/values-night/styles.xml": [1262, 1737190132218.9548, "07fd01ccc6c1b064c71b817e5b550bc0ba5c0d65ee487f6bfd1bb5eb30ccd7e2"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/values-night-v31/styles.xml": [1056, 1737190132215.0188, "3468b94d860b055cf55a37d745d7cee14debe63dba0899a02409c83ea51963d3"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/rplc_prod/res/values-v31/styles.xml": [1057, 1737190132212.0232, "bfeec2a9c51d505a576e1c5a0e277b8b842b8eebc9c25029331f400a9656c422"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/kotlin/com/aamfinancegroup/aamapp/MainActivity.kt": [127, 1724295519733.6914, "efcf1c4fc67d9a4071353ea217a882c1285c0c3c371fd9873a511e8152337d40"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/kotlin/com/aamfinancegroup/mapp_aamg_ui/MainActivity.kt": [148, 1730188239661.473, "6861e1b65cb7dfa86ecfeb7b7d28720810d465a47fda867963d22b2f79574fa9"], "/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/android/app/src/main/kotlin/com/example/aamg/MainActivity.kt": [144, 1724295519734.0884, "19231950de0b2774d01bb1f3b81cf5133558886965dbbbcb364573843e0444c1"]}