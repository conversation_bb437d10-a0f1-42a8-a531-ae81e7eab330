import 'dart:io';

import 'package:AAMG/service/endpoint.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get_storage/get_storage.dart';
import 'package:global_configuration/global_configuration.dart';

import 'app_config.dart';
import 'firebase_options.dart';
import 'main.dart';
import 'service/http_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:path/path.dart' as path;

void main() async {
  try {
    // final configPath =
    //     path.join(Directory.current.path, '/Users/<USER>/githubproject/v3/AAMG/beta_test/beta_test_lastest/Mapp_AAMG_UI/flutter_native_splash-aam_dev.yaml');
    //
    // // print(File(configPath).existsSync());
    // if (File(configPath).existsSync()) {
    //   Process.runSync('flutter',
    //       ['pub', 'run', 'flutter_native_splash:create', '--path=$configPath']);
    //   print('Splash screen generated for aam.');
    // } else {
    //   print('Configuration file $configPath not found.');
    // }

    // print("rereerere");
    // print('test call data = > ${GlobalConfiguration().loadFromUrlIntoKey(url, key)}' );
    // await GlobalConfiguration().loadFromUrl("https://agilesoftgroup.com/AAMGp3-UAT/getEndpoint",queryParameters:{"bu" : "aam"}, headers: HttpService.KeyRequestHeadersEndPoint);
    // await GlobalConfiguration().loadFromUrl("https://ukxppovr06.execute-api.ap-southeast-1.amazonaws.com/latest/RPLC/GetGlobalConfigV3");
    await HttpService.getAuthEndPoint('aam', 'AAMGp3');
  } catch (e) {
    // something went wrong while fetching the config from the url ... do something
  }
  await Firebase.initializeApp(
      options: const FirebaseOptions(
        appId: "1:465736603910:web:5c258a24c8ae77125e6687",
        apiKey: "AIzaSyC0Oq8PjS5TvN2Ud338uJ9T34-vxOaIkOY",
        messagingSenderId: "465736603910",
        projectId: "mappaam-44857",));
  // define dev environment here
  var configuredApp = AppConfig(
    environment: Environment.aam_prod,
    appTitle: 'This is PROD env with AAM endpoints and code',
    countryConfigCollection: 'aam',
    pahtConfigCloudflareAPI: 'AAMGp3',
    child: MyApp(),
  );
  // run init here

  // await dotenv.load(fileName: ".env");
  await GetStorage.init();
  GetStorage box = GetStorage();
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  initScreen = box.read('initScreen') ?? 0;
  await box.write('initScreen', 1); //if already shown -> 1 else 0
  runApp(configuredApp);
}
