import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/models/contract/listContract_model.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sentry/sentry.dart';

import '../../models/contract/digitalContract_model.dart';
import '../../service/AppService.dart';
import '../../view/componance/utils/AppSvgImage.dart';
import '../AppConfigService.dart';
import '../config/appConfig.controller.dart';
import '../profile/profile.controller.dart';

class ContractListController extends GetxController {
  final ProfileController profileCtl = Get.find<ProfileController>();
  // final AppConfigController appConfigCtl = Get.find<AppConfigController>();
  final AppConfigController appConfigCtl = Get.put(AppConfigController());
  final HomeController homeCtl = Get.put(HomeController());
  Rx<ResponseContracDigi> contractDigi = ResponseContracDigi().obs;

  RxString? custCode = RxString('');
  RxString? selectedLoan = RxString('');
  RxBool? isLoading_AAMPay = false.obs;
  RxBool? isLoading = false.obs;
  RxBool? chk_aampay = false.obs;
  RxBool? chkCountAAMPay = false.obs;
  RxBool? chk_contract = false.obs;
  RxList? contactPercent = [].obs;

  RxList<ContractList> contractList = <ContractList>[].obs;
  RxList<Period> contractPeriod = <Period>[].obs;
  RxList<ContractPending> contractStatus = <ContractPending>[].obs;

  RxList<String> guaranteeList = [
    ''
    // menuGetLoanTypeCar.tr,
    // menuGetLoanTypeMoto.tr,
    // menuGetLoanTypeLand.tr,
    // 'AAM PAY'
  ].obs;
  RxList<String> guaranteeDescList = [
    menuGetLoanTypeCarDes.tr,
    menuGetLoanTypeMotoDes.tr,
    menuGetLoanTypeLandDes.tr,
    'AAM PAY'
  ].obs;

  RxList? guaranteeImgList = [].obs;
  RxList? guaranteeImgPercentList = [].obs;

  // RxInt loanStatusStep = 1.obs;
  RxList? loanStatusStepList = [].obs;
  RxInt? selectedStatusContractIndex = 0.obs;
  RxBool isNewRequest = false.obs;
  RxInt? indexMyBill = 0.obs;


  @override
  void onInit() {
    super.onInit();
    update();
    setGuaranteeImg();
  }

  void setGuaranteeImg() {
    AppConfigService appConfigService = Get.find<AppConfigService>();
    if (appConfigService.countryConfigCollection.toString() == 'aam') {
      guaranteeImgList = [
        AppSvgImage.myloan_car_aam,
        AppSvgImage.myloan_motocycle_aam,
        AppSvgImage.myloan_land_aam,
        AppSvgImage.myloan_truck_aam
      ].obs;

      guaranteeImgPercentList = [
        AppSvgImage.aam_myloan_car,
        AppSvgImage.aam_myloan_motocycle,
        AppSvgImage.aam_myloan_land,
        AppSvgImage.aam_myloan_truck
      ].obs;
    } else if (appConfigService.countryConfigCollection.toString() == 'rplc') {
      guaranteeImgList = [
        AppSvgImage.myloan_car_rplc,
        AppSvgImage.myloan_motocycle_rplc,
        AppSvgImage.myloan_land_rplc,
        '<svg ></svg>'
      ].obs;

      guaranteeImgPercentList = [
        AppSvgImage.rplc_myloan_car,
        AppSvgImage.rplc_myloan_motocycle,
        AppSvgImage.rplc_myloan_land,
        // AppSvgImage.rplc_myloan_moto3
      ].obs;
    } else {
      guaranteeImgList = [
        AppSvgImage.myloan_car_rafco,
        AppSvgImage.myloan_motocycle_rafco,
        AppSvgImage.myloan_land_rafco,
        AppSvgImage.myloan_moto3_rafco
      ].obs;
      guaranteeImgPercentList = [
        AppSvgImage.rafco_myloan_car,
        AppSvgImage.rafco_myloan_motocycle,
        AppSvgImage.rafco_myloan_land,
        AppSvgImage.rafco_myloan_moto3
      ].obs;
    }
    update();
  }

  Future<void> resetContractList()async{
    contractList.clear();
    contractPeriod.clear();
    contactPercent!.clear();
    update();
  }

  Future<dynamic> customerContactList() async {
    try {

      print("###### customerContactList");
      if (homeCtl.isGuest!.value) {
        return;
      }
      await resetContractList();

      final profileCtl = Get.find<ProfileController>();

      Map data = {
        "phone": profileCtl.profile.value.phoneFirebase.obs.string,
        "idcard": profileCtl.profile.value.idcard.obs.string ?? "-"
      };
      debugPrint(data.toString());

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getContractList, data);
      debugPrint("###### customerContactList : response");
      debugPrint(response.toString());
      if (response['status'] == 200) {
        for (var i = 0; i < response['result'].length; i++) {
          ContractList contract = ContractList(
            ctt_code: response['result'][i]['ctt_code'],
            guarantee_type:
                response['result'][i]['guarantee_type'].toString().isNotEmpty
                    ? response['result'][i]['guarantee_type'].toString()
                    : "1",
            guarantee_type_name: response['result'][i]['guarantee_type_name'],
            loan_amount: response['result'][i]['loan_amount'].toString(),
            interest: response['result'][i]['interest'].toString(),
            remain: response['result'][i]['remain'].toString() == '0' ||
                    response['result'][i]['remain'].toString().isEmpty &&
                        response['result'][i]['paid_periods'].toString() == '0'
                ? response['result'][i]['loan_amount'].toString()
                : response['result'][i]['remain']
                    .toString(), //TODO ถ้าข้อมูลยอดคงเหลือไม่มี และ จำนวนงวดที่ชำระเท่ากับ 0 ให้แสดงยอดเงินที่กู้มา
            nextpay: response['result'][i]['nextpay'].toString(),
            due_date: response['result'][i]['due_date'].toString(),
            periods: response['result'][i]['periods'].toString(),
            paid_periods: response['result'][i]['paid_periods'].toString(),
          );

          Period period = Period(
            ctt_code: response['result'][i]['ctt_code'],
            nextpay: response['result'][i]['nextpay'].toString(),
            due_date: response['result'][i]['due_date'].toString(),
          );
          update();

          contractPeriod.add(period); //TODO รายการ List ค่างวดสัญญา
          contractList.add(contract); //TODO รายการ List รายละเอียดสัญญา
          double percent = (response['result'][i]['payedInt'] /
              response['result'][i][
                  'mn_totalInt']); //TODO คำนวณ % จำนวนเงินที่ชำระแล้ว แสดงหน้า UI
          if (percent.isNaN) {
            percent = 0;
          }
          contactPercent!.add(percent);
          update();
        }
        chk_contract!.value = true;
        homeCtl.showLoanData!.value = true;
        update();
        await customerCustCode();
      } else {
        chk_contract!.value = false;
        isLoading!.value = false;
        isLoading_AAMPay!.value = false;
        update();
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getContactStatus() async {
    try {
      contractStatus.clear();
      update();

      final ProfileController profileCtl = Get.find<ProfileController>();

      Map data = {
        "phone": profileCtl.profile.value.phone.obs.string.isEmpty
            ? "-"
            : profileCtl.profile.value.phone.obs.string,
        "idcard": profileCtl.profile.value.idcard.obs.string.isEmpty
            ? "-"
            : profileCtl.profile.value.idcard.obs.string
        // "phone": "0632698961",
        // "idcard": "1229900619761"
      };

      // print("###### getContactStatus");
      // print(data);

      //TODO รอ API  ตัวใหม่
      // loanStatusStep.value = 5;
      // update();

      // print(Endpoints.getContractStatus);

      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.getContractStatus, data);

      // print(Endpoints.getContractStatus);
      //
      // print("###### getContactStatus : response");
      // print(response);
      // print(response['result'][0]['loan_amount']);

      if (response['status'] == 200) {
        // for(var i = 0; i < 2; i++){
        for (var i = 0; i < response['result'].length; i++) {
          // print(response['result']['guarantee_type']);
          // print(response['result'][i]['guarantee_type'].toString());

          ContractPending contract = ContractPending(
              grant_id: response['result'][i]['grant_id'].toString().isEmpty
                  ? "null"
                  : response['result'][i]['grant_id'].toString(),
              guarantee_type:
                  response['result'][i]['guarantee_type'].toString(),
              ctt_code: response['result'][i]['ctt_code'].toString(),
              loan_amount: response['result'][i]['loan_amount'].toString(),
              loan_periods: response['result'][i]['loan_periods'].toString(),
              loan_status:
                  response['result'][i]['loan_status_headtext'].toString(),
              grant_status: response['result'][i]['loan_status'].toString());

          contractStatus.add(contract);

          update();

          // print("###### getContactStatus");
          // print(contractStatus[i].loan_status);

          if (contractStatus[i].loan_status == "ส่งคำสมัครเรียบร้อย") {
            loanStatusStepList!.add(1);
          } else if (contractStatus[i].loan_status == "ตรวจสอบเอกสาร") {
            loanStatusStepList!.add(2);
          } else if (contractStatus[i].loan_status ==
              "อยู่ระหว่างพิจารณาสินเชื่อ") {
            loanStatusStepList!.add(3);
          } else if (contractStatus[i].loan_status ==
              "ผ่านการพิจารณาเรียบร้อย") {
            loanStatusStepList!.add(4);
          } else if (contractStatus[i].loan_status ==
                  "หน้างานปิดการขายไม่ได้" ||
              contractStatus[i].loan_status ==
                  "ขอบคุณสำหรับความสนใจในสินเชื่อ ของเรา" ||
              contractStatus[i].loan_status == "มีการแจ้งยกเลิก grant นี้" ||
              contractStatus[i].loan_status == "grant นี้ ถูกยกเลิก") {
            loanStatusStepList!.add(0);
          } else if (contractStatus[i].loan_status == "สถานะ grant นี้ถูกปิด") {
            loanStatusStepList!.add(5);
          }
          update();

          // ContractPending contract = ContractPending(
          //     grant_id: response['result'][0]['grant_id'].toString().isEmpty
          //         ? "null"
          //         : response['result'][0]['grant_id'].toString(),
          //     guarantee_type: response['result'][0]['guarantee_type'].toString(),
          //     ctt_code: response['result'][0]['ctt_code'].toString(),
          //     loan_amount: response['result'][0]['loan_amount'].toString(),
          //     loan_periods: response['result'][0]['loan_periods'].toString(),
          //     loan_status: response['result'][0]['loan_status_headtext'].toString());
          // contractStatus.add(contract);
          //
          // update();
          //
          // print("###### getContactStatus");
          // print(contractStatus[0].loan_status);
          //
          // if (contractStatus[0].loan_status == "ส่งคำสมัครเรียบร้อย") {
          //   loanStatusStepList!.add(1);
          // } else if (contractStatus[0].loan_status == "ตรวจสอบเอกสาร") {
          //   loanStatusStepList!.add(2);
          // } else if (contractStatus[0].loan_status ==
          //     "อยู่ระหว่างพิจารณาสินเชื่อ") {
          //   loanStatusStepList!.add(3);
          // } else if (contractStatus[0].loan_status == "ผ่านการพิจารณาเรียบร้อย") {
          //   loanStatusStepList!.add(4);
          // } else if (contractStatus[0].loan_status == "หน้างานปิดการขายไม่ได้" ||
          //     contractStatus[0].loan_status ==
          //         "ขอบคุณสำหรับความสนใจในสินเชื่อ ของเรา" ||
          //     contractStatus[0].loan_status == "มีการแจ้งยกเลิก grant นี้" ||
          //     contractStatus[0].loan_status == "grant นี้ ถูกยกเลิก") {
          //   loanStatusStepList!.add(0);
          // } else if (contractStatus[0].loan_status == "สถานะ grant นี้ถูกปิด") {
          //   loanStatusStepList!.add(5);
          // }
          // update();
        }

        // print("###### getContactStatus");
        // print(contractStatus.length);
        // print(loanStatusStepList![0]);

        return true;
      } else {
        debugPrint("ไม่พบข้อมูลสถานะสินเชื่อในระบบ");
        return false;
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> selectedStatusContract(int index) async {
    selectedStatusContractIndex!.value = index;
    update();
  }

  Future<dynamic> customerCustCode() async {
    try {

      // print("###### customerCustCode");
      Map data = {"idCard": profileCtl.profile.value.idcard.obs.string};
      // Map data = {"idCard": "1229900728676"};

      // print(data);

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getCustCode, data);

      // print(response);

      if (response['status'] == 200) {
        custCode!.value = response['result'][0]['cust_code'];
        update();
        // TODO: check สัญญา และ วงเงิน AAMPAY
        if (appConfigService.countryConfigCollection.toString() == 'aam') {
          checkGuaranteeLoanLimit();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkGuaranteeLoanLimit() async {
    try {
      //todo for test aam pay
      // contractDigi = ResponseContracDigi(
      //   remain_loan: 30000,
      //   current_contract: 1,
      // ).obs;
      // chk_aampay!.value = true;
      // update();
      //todo end test aam pay
      // Map data = {"cust_code": "CS202016085"};
      Map data = {"cust_code": custCode!.value};

      // print("###### checkGuaranteeLoanLimit");
      // print(data);

      final jsonResponse = await HttpService.post(
          Endpoints.checkAAMPAY, data, HttpService.noKeyRequestHeaders);

      // print("###### checkGuaranteeLoanLimit");
      // print(jsonResponse);
      print(appConfigCtl.appConfig.value.status_aampay.obs.string);

      if (jsonResponse["status"] == 200) {
        if (jsonResponse["remain_loan"] != 0 &&
            jsonResponse["current_contract"] < 5 &&
            appConfigCtl.appConfig.value.status_aampay.obs.string == 'Y') {

          print("ขอ AAM Pay ได้");
          contractDigi = ResponseContracDigi.fromJson(jsonResponse).obs;
          print("###");
          chk_aampay!.value = true;
        }else if(jsonResponse["current_contract"] >= 5 &&
            appConfigCtl.appConfig.value.status_aampay.obs.string == 'Y'){
          debugPrint("เกินจำนวนสัญญา");
          //เหลือวงเงิน แต่เกินจำนวน 5 สัญญา
          contractDigi = ResponseContracDigi.fromJson(jsonResponse).obs;
          chk_aampay!.value = true;
          chkCountAAMPay!.value = true;
        }else {
          // chk_aampay!.value = true; // test
          chk_aampay!.value = false;
        }
        isLoading_AAMPay!.value = false;
        update();

        //TODO ส่งแจ้งเตือนไปที่ Telegram aampay log ลูกค้า AAMPAY เข้าสู่ระบบ
        Map datasendTelegram = {
          "phone": profileCtl.profile.value.phone.obs.string,
          "name":
              "${profileCtl.profile.value.firstname.obs.string} ${profileCtl.profile.value.lastname.obs.string}",
          "cust_code": custCode!.value,
          "guarantee_id": jsonResponse["guarantee_id"].toString(),
          "money_approve": jsonResponse["money_approve"].toString(),
          "remain_loan": jsonResponse["remain_loan"].toString(),
          "debt_loan": jsonResponse["debt_loan"].toString(),
          "statusmenu":
              appConfigCtl.appConfig.value.status_aampay.obs.string != 'Y'
                  ? "ปิดบริการ"
                  : "เปิดให้บริการ",
          "time": DateTime.now().toString().split('.')[0]
        };

        //TODO แจ้งเตือนลูกค้า aam pay เข้าใช้งาน
        final sendTelegram = await HttpService.callAPICloudflare(
            "POST", "sendTelegrammessage", datasendTelegram);
        if (sendTelegram['status'] == 200) {
          print('sendTelegram success');
        } else {
          print('sendTelegram error');
        }
      } else {
        isLoading_AAMPay!.value = false;
        update();
        print('ไม่พบ contractDigi (AAM Pay)');
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void checkBillContract() {
    if (chk_contract!.value == true) {
      chk_contract!.value = false;
      homeCtl.showLoanData!.value = false;
    } else {
      chk_contract!.value = true;
      homeCtl.showLoanData!.value = true;
    }
    update();
  }

  void setCurrentLoan(int index) {
    selectedLoan!.value = contractList[index].ctt_code.toString();
    update();
  }

  String setDescByGuaType(String guarantee_type) {
    AppConfigService appConfig = Get.find<AppConfigService>();
    if (guarantee_type == '1') {
      //TODO car
      return guaranteeDescList[0].toString();
    } else if (guarantee_type == '2') {
      //TODO motocycle
      return guaranteeDescList[1].toString();
    } else if (guarantee_type == '3') {
      //TODO land
      return guaranteeDescList[2].toString();
    } else {
      //TODO other
      return guaranteeDescList[3].toString();
    }
  }

  void checkBu(bu) {
    switch (bu) {
      case 'aam':
        guaranteeList = [
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeMoto.tr,
          // menuGetLoanTypeTruck.tr,
          menuGetLoanTypeLand.tr,
        ].obs;
        break;
      case 'rafco':
        guaranteeList = [
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeMoto.tr,
          menuGetLoanTypeLand.tr,
          menuGetLoanTypeThree.tr,
        ].obs;
        break;
      case 'rplc':
        guaranteeList = [
          menuGetLoanTypeCar.tr,
          menuGetLoanTypeMoto.tr,
          menuGetLoanTypeLand.tr
        ].obs;
        break;
    }
  }

  Future<dynamic> setInitStatusData(
      amount, period, guarantee_type, isNewRequestData) async {
    try {
      selectedStatusContractIndex!.value = 0;
      isNewRequest!.value = isNewRequestData;
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  String setDueDateLocale(String dueDate, String lang) {
    //TODO ฟังก์ชันปรับ format วันที่ ตามภาษาในเมนู setting ex. th, en ,la , km
    try {
      print(dueDate);
      print(lang);
      var date_localeString = "";
      DateTime date = DateTime.parse(dueDate.toString());

      date_localeString = AppService.dateLocalized(date, lang);

      if (date_localeString.toString().isEmpty) {
        debugPrint("Error : date locale String is empty");
        date_localeString = dueDate;
      }
      return date_localeString;
    } catch (e) {
      if (kDebugMode) {
        debugPrint("Error : function convert date locale String");
        debugPrint(e.toString());
      }
      return dueDate;
    }
  }

  String formatCurrency(String input) {
    // กำหนดรูปแบบจำนวนเงินที่ต้องการ
    RegExp regExp = RegExp(r'^\d{1,3}(,\d{3})*$');

    // เช็คว่าจำนวนเงินมีรูปแบบที่ถูกต้องแล้วหรือไม่
    if (regExp.hasMatch(input)) {
      return input; // ถ้าถูกต้องแล้ว return ข้อมูลเดิมกลับมา
    }

    // ถ้ายังไม่ได้อยู่ในรูปแบบที่ถูกต้องให้ปรับรูปแบบ
    try {
      int value = int.parse(
          input.replaceAll(',', '')); // ลบทุก comma ก่อน parse เป็น int
      final formatter = NumberFormat('##,###,###');
      String formatted = formatter.format(value);
      return formatted;
    } catch (e) {
      // throw FormatException('Input is not a valid number');
      return input;
    }
  }

  Future<bool> acceptRejectedLoanStatus(
      String grantId, String grantStatus, String reason) async {
    try {
      final Map<String, dynamic> data = {
        "grant_id": grantId,
        "user_id": profileCtl.profile.value.running.toString(),
        "grant_status": grantStatus,
        "reason": reason,
      };

      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.acceptRejectedLoanStatus, data);

      if (response['status'] == 200) {
        debugPrint("acceptRejectedLoanStatus success");

        // todo delete contractStatus by grant_id ที่ถูกปฏิเสธ
        contractStatus.removeWhere((element) => element.grant_id == grantId);
        loanStatusStepList?.clear();
        //todo เซ็ท loanStatusStepList ใหม่
        for (final contract in contractStatus) {
          loanStatusStepList
              ?.add(_mapLoanStatusToStep(contract.loan_status.toString()));
        }

        update();
        return true;
      } else {
        debugPrint("acceptRejectedLoanStatus error");
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint("acceptRejectedLoanStatus catch error: $e");
      }
      GetSnackBar(
        title: error_msg.tr,
        message: error_msg.tr,
        duration: const Duration(seconds: 3),
      );
      rethrow;
    }
  }

  int _mapLoanStatusToStep(String loanStatus) {
    switch (loanStatus) {
      case "ส่งคำสมัครเรียบร้อย":
        return 1;
      case "ตรวจสอบเอกสาร":
        return 2;
      case "อยู่ระหว่างพิจารณาสินเชื่อ":
        return 3;
      case "ผ่านการพิจารณาเรียบร้อย":
        return 4;
      case "สถานะ grant นี้ถูกปิด":
        return 5;
      case "หน้างานปิดการขายไม่ได้":
      case "ขอบคุณสำหรับความสนใจในสินเชื่อ ของเรา":
      case "มีการแจ้งยกเลิก grant นี้":
      case "grant นี้ ถูกยกเลิก":
        return 0;
      default:
        return -1; // กำหนดค่าเริ่มต้นหากไม่มีสถานะที่ตรงกัน
    }
  }

  void setIndexMyBill(int index) {
    indexMyBill!.value = index;
    update();
    debugPrint("#### indexMyloan : ${indexMyBill!.value}");
  }
}
