import 'dart:async';

import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/pincode/pincode.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/view/screen/pincode/pincode.dart';
import 'package:sentry/sentry.dart';
import 'package:uuid/uuid.dart';

import '../../models/otp_model.dart';
import '../../service/http_service.dart';
import '../../view/componance/AppLoading.dart';
import '../../service/endpoint.dart';
import '../../view/componance/widgets/app_pop_up/alert_popup.dart';
import '../../view/componance/widgets/app_pop_up/register_popups.dart';
import '../../view/screen/login/verifyOTP.dart';
import '../profile/delete.account.controller.dart';

class LoginController extends GetxController {
  RxBool? isVerify = false.obs;
  RxBool? isAlert = false.obs;
  Rx<TextEditingController> otpController = TextEditingController().obs;
  Rx<TextEditingController> phone_login = TextEditingController().obs;
  final HomeController homeController = Get.put(HomeController());
  FocusNode textFieldFocus = FocusNode();
  RxString? refCode = "".obs;
  RxInt counter_OTP = 60.obs;
  late Timer timer;
  RxBool? isPhoneValid = false.obs;

  RxString? phone_code = "".obs;

  GetStorage storage = GetStorage();

  void setInitPhoneCode() {
    if (appConfigService.countryConfigCollection == 'aam') {
      phone_code!.value = "+66";
    } else if (appConfigService.countryConfigCollection == 'rplc') {
      phone_code!.value = "+856";
    } else if (appConfigService.countryConfigCollection == 'rafco') {
      phone_code!.value = "+855";
    }
    update();
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  //TODO check otp format for Login
  void checkOtpFormat(context, value) async {
    if (otpController.value.text.length == 6) {
      setVerify(true);
      // TODO verify otp then if true
      var chkOtp = await checkVerifyOTPConfig(context, phone_login.value.text,
          otpController.value.text, refCode!.value, '');
      print('chkOtp : ${chkOtp.toString()}');
      if (chkOtp.toString() == '200') {
        loginWithPhone(context);
      } else {
        isAlert!.value = true;
        setVerify(false);
        update();
      }
    } else {
      // isAlert!.value = true;
      setVerify(false);
      update();
    }
  }

  // TODO send otp Login
  Future<dynamic> checkOTPConfig(context, String phone) async {
    print('checkOTPConfig');
    try {
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        if(phone == "0819455508"){
          sendOTPAdmin(context, phone);
        }else{
          sendOTP(context, phone, '');
        }
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        checkFormatOTPRPLC(context, phone, '');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        checkFormatOTPRafco(context, phone, '');
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  Future<dynamic>sendOTPAdmin(context,phone) async{
    try{

      print('sendOTPAdmin');
      print('phone : $phone');
      AppLoading.loadingVerify(context);
      refCode!.value = 'xxxxx';
      update();
      AppLoading.Loaderhide(context);

      Get.to(() => VerifyOTPScreen());

    }catch(e){
      print("error sendOTPAdmin : $e");
    }
  }

  // TODO resend otp
  Future<dynamic> checkResendOTPConfig(context) async {
    try {
      if (counter_OTP.value == 0) {
        resetData();
        setVerify(false);

        var phone = phone_login.value.text;

        if (appConfigService.countryConfigCollection.toString() == "aam") {
          if(phone == "0819455508"){
            sendOTPAdmin(context, phone);
          }else {
            sendOTP(context, phone, 'resend');
          }
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rplc") {
          checkFormatOTPRPLC(context, phone, 'resend');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rafco") {
          checkFormatOTPRafco(context, phone, 'resend');
        }
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  Future<dynamic> sendOTP(context, String phone, String typeResend) async {
    try {
      if (phone.length == 10) {
        AppLoading.loadingVerify(context);
        phone = "+66${phone.substring(1)}";

        //TODO check Delete Account
        var chkAcc =
            await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
        //TODO ถ้ามีสถานะ Delete Account
        if (chkAcc == true) {
          //TODO call api send otp

          SendCode valueSendCode =
              SendCode.fromJson({"phone": phone, "from": "AAM", "typeSMS": ""});
          final resSendCode = await HttpService.callAPICloudflare(
              "POST", "requertOTP_CF_V2", valueSendCode.toJson());

          AppLoading.Loaderhide(context);
          // var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
          var status = resSendCode["result"]["statusCode"];

          if (status.toString() == "202") {
            Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
          } else if (status.toString() == "200") {
            /// verify otp =>
            refCode!.value = resSendCode["result"]["refCode"];
            update();
            if (typeResend != "resend") {
              Get.to(() => VerifyOTPScreen());
            }
          } else {
            Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
          }
          return status;
        } else if (chkAcc["status"].toString() == 'false') {
          AppLoading.Loaderhide(context);
          //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
          AlertPopup.AlertDeleteAccount(context);
        } else {
          AppLoading.Loaderhide(context);
          debugPrint("Something went wrong try again");
        }
      } else {
        Get.snackbar("แจ้งเตือน", "กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง");
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {}
  }

  Future<dynamic> checkFormatOTPRPLC(
      context, String phone, String typeResend) async {
    try {
      if (phone.isNotEmpty) {
        if (phone_code!.value == '+856' || phone_code!.value == '+855') {
          // if (phone.substring(0, 1) == '2' || phone.substring(0, 1) == '3') {
          //   if (phone.length == 10) {
          var phoneParam = "";
          var checkPhone = phone.substring(0, 1);

          if (checkPhone == "0") {
            phoneParam = phone_code!.value + phone.substring(1, phone.length);
          } else {
            phoneParam = '${phone_code!.value}$phone';
          }
          sendOTPRPLC(context, phoneParam, typeResend); //TODO ส่ง OTP RPLC
          // } else {
          //   // wrongPhone();
          // }
          // }
        } else {
          debugPrint('ส่ง otp ผ่าน bot TG : ${phone_code!.value}');
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RPLC", typeResend); // เส้น bot TG
        }
      } else {
        // print('here else');
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkFormatOTPRafco(
      context, String phone, String typeResend) async {
    try {
      print('checkFormatOTPRafco');
      print('phone : $phone');
      if (phone.isNotEmpty) {
        if (phone_code!.value == '+855' || phone_code!.value == '+856') {
          var phoneParam = "";
          var checkPhone = phone.substring(0, 1);

          if (checkPhone == "0") {
            phoneParam = phone_code!.value + phone.substring(1, phone.length);
          } else {
            phoneParam = '${phone_code!.value}$phone';
          }
          sendOTPRafco(context, phoneParam, typeResend); //TODO ส่ง OTP RAFCO
        } else {
          debugPrint('ส่ง otp ผ่าน bot TG : ${phone_code!.value}');
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RAFCO", typeResend); // เส้น bot TG
        }
      } else {}
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRPLC(context, String phone, String typeResend) async {
    try {
      // ex. body
      /// *********** เบอร์สาขา RPLC
      Map data = {
        "from": "RPLC",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);
      //TODO check Delete Account
      var chkAcc =
          await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp

        final response = await HttpService.callAPIsmsService(
            "POST", Endpoints.requestLocal_OTP, data);

        int status = response["statusCode"];
        var result = response["result"];

        if (status == 200 && result == true) {
          final phoneCode = phone.substring(1, phone.length);
          var uniqueID = Uuid().v4();
          // ex. body
          /// *********** เบอร์พี่นกน้อย
          var bodyData = {
            "transaction_id": uniqueID.toString(),
            "header": "RPLC",
            "phoneNumber": phoneCode,
            "message":
                "RPLC OTP ${response["refCode"]} (Ref: ${response["otp"].toString()}) ລະຫັດຈະໝົດອາຍຸໃນ 1 ນາທີ"
          };

          final responseSendSMS = await HttpService.callAPIsmsService(
              "POST", Endpoints.sendOTPLaos, bodyData);

          AppLoading.Loaderhide(context);

          if (responseSendSMS["resultCode"] == 20000 ||
              responseSendSMS["resultDesc"]
                  .toString()
                  .toLowerCase()
                  .contains("success")) {
            refCode!.value = response["refCode"];
            update();
            if (typeResend != "resend") {
              Get.to(const VerifyOTPScreen());
            }
          } else {
            Get.snackbar("Error", 'Failed to send OTP');
            //TODO sms error
            Map dataSendTG = {
              "message":
                  '⚠️  แจ้งเตือน MappRPLC(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n🔐 OTP : ${response["otp"].toString()} \n📲 เบอร์โทรศัพท์ : $phoneCode \n👤 transaction ID : $uniqueID \n⏰ เวลา : ${DateTime.now()}'
            };
            // print('dataSendTG : $dataSendTG');
            final response2 = await HttpService.post(
                Endpoints.sendNotiTelegramAI,
                dataSendTG,
                HttpService.noKeyRequestHeaders);
          }
        } else if (status == 404 && result == false) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(verify_failed.tr);
        } else if (status == 202) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(please_1_min.tr);
        } else if (status == 500) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(server_error.tr);
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(register_failed.tr);
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (e) {
      AppLoading.Loaderhide(context);
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRafco(context, String phone, String typeResend) async {
    try {
      Map data = {
        "from": "RAFCO",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);
      //TODO check Delete Account
      var chkAcc =
          await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp
        final response = await HttpService.callAPIsmsService(
            "POST", Endpoints.requestLocal_OTP, data);

        int status = response["statusCode"];
        var result = response["result"];

        if (status == 200 && result == true) {
          final phoneCode = phone.substring(1, phone.length);
          var uniqueID = Uuid().v4();

          var bodyData = {
            "phone": phoneCode,
            "mess":
                "RAFCO OTP ${response["otp"].toString()} (Ref: ${response["refCode"]}) លេខកូដនឹងផុតកំណត់ក្នុងរយៈពេល 1 នាទី។",
            "sender": "RAFCO"
          };

          final responseSendSMS = await HttpService.callAPIsmsService(
              "POST", Endpoints.sendOTPRafco, bodyData);

          AppLoading.Loaderhide(context);

          if (responseSendSMS["resultCode"] == 20000 ||
              responseSendSMS["resultDesc"]
                  .toString()
                  .toLowerCase()
                  .contains("success")) {
            refCode!.value = response["refCode"];
            update();
            if (typeResend != "resend") {
              Get.to(const VerifyOTPScreen());
            }
          } else {
            Get.snackbar("Error", 'Failed to send OTP');

            //TODO sms error
            Map dataSendTG = {
              "message":
                  '⚠️  แจ้งเตือน MappRAFCO(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n📲 เบอร์โทรศัพท์ : $phoneCode \n⏰ เวลา : ${DateTime.now()}'
            };
            final response2 = await HttpService.post(
                Endpoints.lambdaLinkSendLineMappRAFCO,
                dataSendTG,
                HttpService.noKeyRequestHeaders);
          }
        } else if (status == 404 && result == false) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(verify_failed.tr);
        } else if (status == 202) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(please_1_min.tr);
        } else if (status == 500) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(server_error.tr);
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(register_failed.tr);
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (e) {
      AppLoading.Loaderhide(context);

      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPbot_TH(
      context, String phone, String BU, String typeResend) async {
    try {
      print("try");
      print(phone);
      if (phone == "**********") {
        // loginWithUsername(context, "demoAcount", "1234");
        return null;
      }
      var phoneParam = "";
      var checkPhone = phone.substring(0, 1);
      print(checkPhone);
      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = '${phone_code!.value}$phone';
      }

      final data = {"phone_firebase": phoneParam};
      print(data);

      AppLoading.loadingVerify(context);

      print("loading");
      //TODO check Delete Account
      var chkAcc = await Get.put(DeleteAccountController())
          .checkDeleteAccount(phoneParam);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp

        print(Endpoints.getDataMember);
        final dataMember = await HttpService.callAPIjwtRPLC(
            "POST", Endpoints.getDataMember, data); //ข้อมูลพนักงาน เบอร์ไทย
        print('dadsdsdfsf ${dataMember}');

        if (dataMember["status"] == 200) {
          var margeInto = phoneParam + dataMember["result"][0]["memberID"];
          var bodyData = {"from": BU, "phone": margeInto, "typeSMS": "Qsms"};
          print(bodyData);
          print("dsddsdsdsd");
          print(Endpoints.requestOTPbot);
          final response = await HttpService.callAPIsmsService(
              "POST", Endpoints.requestOTPbot, bodyData);
          print('status : $response');
          AppLoading.Loaderhide(context);
          int status = response["statusCode"];
          var result = response["result"];

          if (status == 200 && result == true) {
            refCode!.value = response["refCode"];
            update();
            if (typeResend != "resend") {
              Get.to(const VerifyOTPScreen());
            }
          } else if (status == 404 && result == false) {
            // ToastAlert.redCenter(verify_failed.tr);
          } else if (status == 202) {
            // ToastAlert.redCenter(please_1_min.tr);
          } else if (status == 500) {
            // ToastAlert.redCenter(server_error.tr);
          } else {
            // ToastAlert.redCenter(register_failed.tr);
          }
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter("ไม่ได้เป็นสมาชิกนิ");
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (e) {
      AppLoading.Loaderhide(context);

      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> resendOtp(context) async {
    try {
      if (counter_OTP.value == 0) {
        resetData();
        var phone = "+66${phone_login.value.text.substring(1)}";

        print("phone : $phone");
        SendCode valueSendCode =
            SendCode.fromJson({"phone": phone, "from": "AAM", "typeSMS": ""});
        final resSendCode = await HttpService.callAPICloudflare(
            "POST", "requertOTP_CF_V2", valueSendCode.toJson());

        var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
        var status = resSendCode["result"]["statusCode"];

        if (status.toString() == "202") {
          Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
        } else if (status.toString() == "200") {
          refCode!.value = resSendCode["result"]["refCode"];
          update();
        } else {
          Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void resetData() {
    otpController.value.text = "";
    refCode!.value = "";
    isVerify!.value = false;
    isAlert!.value = false;
    counter_OTP.value = 60;
    update();
    startOTPTimer();
  }

  Future<dynamic> checkVerifyOTPConfig(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      var response;
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        if(phone == "0819455508") {
          response =
          await checkOTPVerifyAdmin(context, phone, otpCode);
        }else{
          response =
          await checkOTPVerify(context, phone, otpCode, refCode, 'AAM');
        }
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        response =
            await verifyOTP_RPLC(context, phone, otpCode, refCode, 'RPLC');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        response =
            await verifyOTP_Rafco(context, phone, otpCode, refCode, 'RAFCO');
      }
      return response;
    } catch (e) {
      print('$e => error checkVerifyOTPConfig');
    }
  }

  Future<dynamic> checkOTPVerify(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": otpCode,
          "refCode": refCode,
          "fromBU": fromBU,
        });

        final resVerify = await HttpService.callAPICloudflare(
            "POST", "verifyOTP_CF", valueVerify.toJson());
        var responseVerify = ResponseVerifyCode.fromJson(resVerify["result"]);

        var status = resVerify["result"]['statusCode'];
        update();
        print("status : $status");
        return status;
      }
    } catch (e) {
      print('$e => error checkVerify');
    }
  }

  Future<dynamic> checkOTPVerifyAdmin(context, String phone, String otpCode) async {
    try {
      if (phone.length == 10 && otpCode == "111111") {
        return 200;
      }
    } catch (e) {
      print('$e => error checkVerify');
    }
  }

  Future<dynamic> verifyOTP_RPLC(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> verifyOTP_Rafco(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> switchVerify(data) async {
    var response;
    if (Endpoints.verifyOTPCF == "next") {
      response = await HttpService.post(
          Endpoints.verifyOTP_RPLC, data, HttpService.noKeyRequestHeaders);
    } else {
      response = await HttpService.callAPIsmsService(
          'POST', Endpoints.verifyOTPCF, data);
    }
    return response;
  }

  Future<void> testFunc() async {
    try {
      Map data = {
        "phone_firebase": '+66927251261',
        // "phone": phoneFirebase,
        // "idcard":"-"
      };

      final response = await HttpService.callAPIjwt(
          "POST", "https://agilesoftgroup.com/AAMGp3-UAT/getDataCust", data);

      print("response : $response");
    } catch (e) {
      print(e);
    }
  }

  Future<void> loginWithPhone(context) async {
    try {
      if (phone_login.value.text.isEmpty) {
        print("phone_login : ${phone_login.value.text}");
        return;
      } else {
        var phone = phone_login.value.text;
        var phoneFirebase = "$phone_code${phone.toString().substring(1)}";
        Map data = {
          "phone_firebase": phoneFirebase,
        };

        print(data);

        final response = await HttpService.callAPIjwt(
            "POST", Endpoints.loginWithPhone, data);

        // print("response : $response");

        if (response['status'].toString() == '200') {
          print("#####if");
          storage.write("isGuest", false);
          storage.write("token", response['result']['accessToken']);
          storage.write("phone_firebase", phoneFirebase);
          homeController.isShowTutorial!.value = true;
          homeController.update();
          print(
              "isShowTutorialLogin : ${homeController.isShowTutorial!.value}");
          // storage.write('isShowTutorial', true);
          // storage.write('isShowTutorialMR', true);
          // print('isShowTutorialLogin : ${storage.read('isShowTutorial')}');
          getDataCust();
        } else {
          debugPrint("Error : ${response['message']}");
          // AlertPopup.AlertNotFoundAccount(context);
        }
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
    }
  }

  Future<void> getDataCust() async {
    try {
      if (phone_login.value.text.isEmpty) {
        print("phone_login : ${phone_login.value.text}");
        return;
      } else {
        var phone_firebase =
            '$phone_code${phone_login.value.text.substring(1)}';

        print("phone_firebase : $phone_firebase");
        Map data = {
          "phone_firebase": phone_firebase,
        };
        print("data : $data");
        final response =
            await HttpService.callAPIjwt("POST", Endpoints.getDataCust, data);

        print(Endpoints.getDataCust);
        print(response);
        print("response  getDataCust : $response");
        print(response['result']['running'].toString());
        if (response['status'].toString() == '200') {
          storage.write("session", true);
          storage.write("user_id", response['result']['running'].toString());
          if (response['result']['phone_firebase'].toString().isEmpty ||
              response['result']['phone_firebase'].toString() == 'null') {
            var phone_firebase =
                '$phone_code${phone_login.value.text.substring(1)}';
            storage.write("phone_firebase", phone_firebase);
            homeController.isShowTutorial!.value = true;
            homeController.update();
            // storage.write('isShowTutorial', true);
          } else {
            storage.write("phone_firebase",
                response['result']['phone_firebase'].toString());
          }
          Get.lazyPut(() => PincodeController().setBackIn(false), fenix: true);
          Get.to(PinCodePage());
        } else {
          print("Error : ${response['message']}");
        }
      }
    } catch (e) {
      print(e);
    }
  }

  void startOTPTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (counter_OTP.value > 0) {
        counter_OTP.value--;
        update();
      } else {
        timer.cancel();
      }
    });
    print('start timer');
    print(counter_OTP.value);
  }

  void setPhoneLogin(String phone) {
    phone_login.value.text = phone;
    update();

    if (phone_login.value.text.length == 10) {
      isPhoneValid!.value = true;
      update();
    } else {
      isPhoneValid!.value = false;
      update();
    }
  }

  void setPhoneCode(String phoneCode) {
    phone_code!.value = phoneCode;
    update();
  }

  void setPhone(String phoneCode, String phone) {
    phone_code!.value = phoneCode;
    phone_login.value.text = phone;
    update();
  }
}
