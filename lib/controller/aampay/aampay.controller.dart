import 'dart:async';
import 'dart:convert';

import 'package:AAMG/controller/register/register.controller.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/models/address_model.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:AAMG/view/componance/widgets/aam_pay/aampay_componance.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:sentry/sentry.dart';

import '../../models/otp_model.dart';
import '../../view/screen/aampay/aampay_comfirm.dart';
import '../../view/screen/aampay/aampay_updateInfo.dart';
import '../../view/screen/home/<USER>';
import '../../view/screen/kyc/bookbank.dart';
import '../contract/contractlist.controller.dart';
import '../kyc/kyc.controller.dart';
import '../profile/profile.controller.dart';
import '../transalation/translation_key.dart';
import 'package:http/http.dart' as http;


class AAMPayController extends GetxController {
  final ProfileController profileCtl = Get.find<ProfileController>();
  final ContractListController contractlistCtl =
      Get.find<ContractListController>();

  RxBool? acceptAgreement = false.obs;
  RxString? kycstate = RxString('');

  RxBool? showAddress = false.obs;
  RxBool? showJobAddress = false.obs;

  TextEditingController JobText = TextEditingController();
  TextEditingController salary = TextEditingController();
  TextEditingController homeAddressText = TextEditingController();
  TextEditingController zipCodeText = TextEditingController();
  TextEditingController provinceText = TextEditingController();
  TextEditingController ampherText = TextEditingController();
  TextEditingController tombolText = TextEditingController();
  TextEditingController JobAddressText = TextEditingController();
  TextEditingController JobzipCodeText = TextEditingController();
  TextEditingController JobprovinceText = TextEditingController();
  TextEditingController JobampherText = TextEditingController();
  TextEditingController JobtombolText = TextEditingController();

  FocusNode focusNodeHomeAddressText = FocusNode();
  FocusNode focusNodeTypeCar = FocusNode();
  FocusNode focusNodeProvinceText = FocusNode();
  FocusNode focusNodeAmpherText = FocusNode();
  FocusNode focusNodeTombolText = FocusNode();
  FocusNode focusNodeJobAddressText = FocusNode();
  FocusNode focusNodeTypeJob = FocusNode();
  FocusNode focusNodeJobProvinceText = FocusNode();
  FocusNode focusNodeJobAmpherText = FocusNode();
  FocusNode focusNodeJobTombolText = FocusNode();

  RxString? address = RxString('');
  RxString? Jobaddress = RxString('');

  RxString? installment = RxString('0');
  RxString? stayPeriod = RxString('');
  RxString? itemsSelect = RxString('');
  RxString? itemsSelectSave = RxString('');
  RxString? itemsSelectSave2 = RxString('');
  RxString? itemsSelectSaveJob = RxString('');

  RxList? typeJobList = [].obs;
  RxList? typeJobforCheck = [].obs;
  RxList? JobprovinceList = [].obs;
  RxList? JobampherList = [].obs;
  RxList? JobtombolList = [].obs;
  RxList? provinceList = [].obs;
  RxList? ampherList = [].obs;
  RxList? tombolList = [].obs;
  RxList? typeJobList2 = [].obs;

  /// Step 2
  TextEditingController bankText = TextEditingController();
  final TextEditingController requestLoan = TextEditingController();

  RxString? provinceTextSave = RxString('');
  RxString? districtTextSave = RxString('');
  RxString? sub_districtTextSave = RxString('');
  RxString? mooTextSave = RxString('');
  RxString? addressTextSave = RxString('');
  RxString? province_codeTextSave = RxString('');
  RxString? district_codeTextSave = RxString('');
  RxString? sub_district_codeTextSave = RxString('');
  RxString? zip_codeTextSave = RxString('');
  RxString? selectedProvince = RxString('');
  RxString? selectedDistrict = RxString('');
  RxString? selectedSubDistrict = RxString('');

  RxInt? requestloanCal = 0.obs;

  RxString? bank_save = RxString('');
  RxString? banktextshow = RxString('');
  RxString? dropdownValue = RxString('');
  RxString? dropdownValue2 = RxString('');
  RxList? paypermonthall = [].obs;
  RxList? dropdownchoice = [].obs;
  RxString? installmentDigi = RxString('0');
  RxString? interest_remain = RxString('0');
  RxDouble? net_total = 0.0.obs;
  RxString? Fee = RxString('0');
  RxBool? openDropdown = true.obs;
  RxList<String>? items1 = ["1", "2", "3"].obs;
  RxList<String>? items2 = ["1", "2", "3", "4", "5", "6"].obs;
  RxList<String>? items3 =
      ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"].obs;
  RxList<String>? items4 = [
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18"
  ].obs;

  RxBool? popuppayall = false.obs;
  RxString? paypermonth = RxString('');

  RxString? mngrand = RxString('');
  RxString? periods = RxString('');
  RxString? selectPayPerMonth = RxString('');
  RxInt? start = 60.obs;

  RxBool? updateAgreement = false.obs;
  RxBool? createContractFailed = false.obs;
  RxString? cttdigi = RxString('');
  Rx<TextEditingController> loan_amount = TextEditingController(text: '0').obs;
  FocusNode loanAmountFocus = FocusNode();
  RxList loanAmountList = [].obs;
  RxString selectedPeriod = ''.obs;
  RxInt selectedPeriodIndex = 0.obs;
  var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
  RxBool isActivePeriod = false.obs;
  RxBool? isAcceptedTermPolicy = false.obs;
  RxInt counter_OTP = 60.obs;
  RxBool? isVerify = false.obs;
  RxBool? isAlert = false.obs;
  RxString? refCode = "".obs;
  Rx<TextEditingController> otpController = TextEditingController().obs;
  RxString? aampay_contract_status = "".obs;
  RxBool? chkUpdateAddress = false.obs;
  RxString? addressAms4 = "".obs;
  RxString? mooAms4 = "".obs;
  RxString? sub_districtAms4 = "".obs;
  RxString? sub_districtCode = "".obs;
  RxString? districtAms4 = "".obs;
  RxString? districtCode = "".obs;
  RxString? provinceAms4 = "".obs;
  RxString? provinceCode = "".obs;
  RxString? zip_codeAms4 = "".obs;
  GetStorage storage = GetStorage();
  Rx<TextEditingController> controllerProvince = TextEditingController().obs;
  Rx<TextEditingController> controllerDistrict = TextEditingController().obs;
  Rx<TextEditingController> controllerSubDistrict = TextEditingController().obs;
  Rx<TextEditingController> controllerMoo = TextEditingController().obs;
  Rx<TextEditingController> controllerAddress = TextEditingController().obs;
  RxBool isUpdateAddress = false.obs;
  RxBool isCalculating = false.obs;
  RxString aampay_ctt_code = "".obs;
  RxString tax_contract = "0".obs;

  RxString setSelectedCityCode = "".obs;
  RxString setSelectedDistrictCode = "".obs;
  RxString setSelectedSubDistrictCode = "".obs;

  final ContractListController contractListCtl =
      Get.find<ContractListController>();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    Future.delayed(Duration.zero, () async {
      // setLoanAmountChoice();
      print("sdddss");
      await storage.write('aampay_contract_status', '');
    });
  }

  void setLoanAmountChoice() {
    if (contractListCtl.contractDigi.value.remain_loan.toString().isEmpty) {
      throw 'remain_loan is empty';
    }

    if (int.parse(contractListCtl.contractDigi.value.remain_loan.toString()) >
        3000) {
      loanAmountList.value = [1000, 2000, 3000, 5000];
    } else {
      loanAmountList.value = [1000, 2000, 3000];
    }
    update();
  }

  void calculateLoan(int loanAmount, String calculateType) {
    print('loanAmount: $loanAmount');
    print('calculateType: $calculateType');
    int totalLoan = 0;
    int maxLoan = int.parse(contractListCtl.contractDigi.value.remain_loan.toString()); //todo วงเงินสูงสุดที่ขอได้
    // maxLoan = 30000; // todo เงื่อนไขตัวใหม่

    if (loan_amount.value.text.isNotEmpty) {
      if (loan_amount.value.text.contains(",")) {
        // Parse the cleaned text to an integer
        totalLoan =
            int.parse(loan_amount.value.text.toString().replaceAll(",", ""));
      } else {
        // Parse the cleaned text to an integer
        totalLoan = int.parse(loan_amount.value.text.toString());
      }
    }

    if (calculateType == 'plus') {
      totalLoan += loanAmount;

      // Ensure the total loan does not exceed the maximum allowed loan
      if (totalLoan > maxLoan) {
        totalLoan = maxLoan;
      }

      // Update the text field and requestloanCal with the formatted value
      loan_amount.value.text = f.format(totalLoan);
      requestloanCal!.value = totalLoan;
    } else {
      loan_amount.value.text = '0';
      selectedPeriod.value = '';
      selectedPeriodIndex.value = 0;
      requestloanCal!.value = 0;
      isActivePeriod.value = false;
    }

    // Ensure UI update after modifying values
    update();
  }

  void calculateLoan2(int loanAmount, String calculateType) {
    int total_loan;

    if (loan_amount.value.text.isNotEmpty) {
      if (loan_amount.value.text.contains(',')) {
        loan_amount.value.text = loan_amount.value.text.replaceAll(",", "");
      } else {
        loan_amount.value.text = loan_amount.value.text;
      }
      total_loan = int.parse(loan_amount.value.text);
    } else {
      total_loan = 0;
    }

    if (calculateType == 'plus') {
      total_loan += loanAmount;
      loan_amount.value.text = total_loan.toString();
      loan_amount.value.text = f.format(loan_amount.value.text);
      update();
      //todo ถ้าเงินที่ขอกู้มากกว่าเงินที่เหลือในสัญญา
      if (total_loan >=
          int.parse(
              contractListCtl.contractDigi.value.remain_loan.toString())) {
        loan_amount.value.text =
            f.format(contractListCtl.contractDigi.value.remain_loan);
        update();
        requestloanCal!.value =
            int.parse(loan_amount.value.text.replaceAll(",", ""));
      }
    } else {
      if (loan_amount.value.text.isNotEmpty) {
        loan_amount.value.text = '0';
      }
      selectedPeriod.value = '';
      selectedPeriodIndex.value = 0;
    }
    update();
  }

  void setLoanAmount2(amount) {
    // print('amount: $amount');
    if (amount.toString().isEmpty) {
      loan_amount.value.text = '0';
    } else {
      loan_amount.value.text = amount.toString();
    }
    update();
  }

  void setLoanAmount1(amount) {
    print(amount.toString());
    if (amount == null ||
        amount.toString().isEmpty ||
        amount.toString() == '0') {
      loan_amount.value.text = '0';
    } else {
      // เพิ่มค่า amount กับค่าปัจจุบันของ loan_amount
      var currentAmount = int.parse(
          loan_amount.value.text.isEmpty || loan_amount.value.text == '0'
              ? '0'
              : loan_amount.value.text.toString().replaceAll(",", ""));

      var value = currentAmount + int.parse(amount.toString());

      // Define the rounding thresholds
      var thresholds = loanAmountList; // [1000, 2000, 3000, 5000]

      // Find the nearest threshold
      var roundedValue = thresholds.firstWhere(
        (threshold) => value <= threshold,
        orElse: () => thresholds.last,
      );

      // Check if the roundedValue exceeds the remaining loan
      var remainingLoan =
          int.parse(contractListCtl.contractDigi.value.remain_loan.toString());
      if (roundedValue >= remainingLoan) {
        roundedValue = remainingLoan;

        loan_amount.value.text = roundedValue.toString();
        loan_amount.value.text =
            f.format(contractListCtl.contractDigi.value.remain_loan);
        requestloanCal!.value =
            int.parse(loan_amount.value.text.replaceAll(",", ""));
      } else {
        // Update the loan amount
        loan_amount.value.text = roundedValue.toString();
        loan_amount.value.text = f.format(loan_amount.value.text);
        requestloanCal!.value =
            int.parse(loan_amount.value.text.replaceAll(",", ""));
      }
      update();
    }
  }

  Future<void> setLoanAmount(dynamic amount) async {
    print(amount.toString());
    // ตรวจสอบกรณี amount ว่างหรือไม่มีค่า
    if (amount == null || amount.toString().isEmpty || amount.toString() == '0') {
      loan_amount.value.text = '0';
      requestloanCal!.value = 0;
      isActivePeriod.value = false;
      update();
      return;
    }

    try {
      // แปลง amount โดยลบ comma และเปลี่ยนเป็น integer
      // int parsedAmount = int.parse(amount.toString().replaceAll(",", ""));

      // ลบ comma, space และตรวจสอบว่าข้อมูลเป็นตัวเลขจริง ๆ
      String cleanedAmount = amount.toString().replaceAll(",", "").trim();
      if (!RegExp(r'^\d+$').hasMatch(cleanedAmount)) {
        throw FormatException("Invalid number format: $cleanedAmount");
      }
      int parsedAmount = int.parse(cleanedAmount);

      // ดึง remain_loan และแปลงเป็น integer
      int remainingLoan = int.parse(contractListCtl.contractDigi.value.remain_loan.toString());

      // จำกัดยอดตาม remain_loan
      if (parsedAmount > remainingLoan) {
        parsedAmount = remainingLoan;
      }

      // ตรวจสอบว่ากำลังพิมพ์อยู่หรือไม่ (ถ้ามากกว่า 100 ปัดหลักสิบและหลักหน่วย)
      int roundedValue;
      if (parsedAmount >= 1000 ) {
        roundedValue = (parsedAmount ~/ 100) * 100;
      } else {
        roundedValue = parsedAmount;
      }

      // อัปเดตค่าใหม่
      loan_amount.value.text = f.format(roundedValue);
      requestloanCal!.value = roundedValue;

      // อัปเดต UI
      update();

    } catch (e) {
      print("Error in setLoanAmount: $e");
      // จัดการข้อผิดพลาด เช่น แสดงข้อความเตือนผู้ใช้
    }
  }

  void setLoanAmount_test(amount) {
    print(amount.toString());
    if (amount == null ||
        amount.toString().isEmpty ||
        amount.toString() == '0') {
      loan_amount.value.text = '0';
      requestloanCal!.value = 0;
      isActivePeriod.value = false;
      update();
    } else {
      // Current loan amount
      var currentAmount = 0;

      if (loan_amount.value.text.toString().contains(',')) {
        amount = amount.toString().replaceAll(",", "");
        currentAmount =
            int.parse(loan_amount.value.text.toString().replaceAll(",", ""));
      } else {
        currentAmount = int.parse(loan_amount.value.text.toString());
      }

      // Add the new amount to the current loan amount
      var value = currentAmount + int.parse(amount.toString());

      // Define the rounding thresholds
      var thresholds = loanAmountList;

      // Find the next threshold based on the value
      var roundedValue = thresholds.firstWhere(
        (threshold) => value <= threshold,
        orElse: () => thresholds.last,
      );

      // Ensure the roundedValue does not exceed the remaining loan
      var remainingLoan =
          int.parse(contractListCtl.contractDigi.value.remain_loan.toString());
      if (roundedValue >= remainingLoan) {
        roundedValue = remainingLoan;
      }

      // Update the loan amount
      loan_amount.value.text = f.format(roundedValue);
      requestloanCal!.value = roundedValue;
      update();
    }
  }

  void selectPeriod(context, String value, int index) {
    selectedPeriod.value = value;
    selectedPeriodIndex.value = index;
    update();
    setDropdownActive(false);
    arcardPreview(context);
  }

  void acceptTermPolicy() {
    isAcceptedTermPolicy!.value = !isAcceptedTermPolicy!.value;
    update();
  }

  Future<dynamic> updateDigitalAgreement(context) async {
    try {
      Map data = {
        "phone_firebase": profileCtl.profile.value.phoneFirebase.obs.string,
        "app_agree_status": "Y",
      };

      AppLoading.loadingVerify(context);
      final response =
          await HttpService.callAPIjwt("POST", Endpoints.updateDigitalAgreement, data);
      AppLoading.Loaderhide(context);
      print(response);
      print(response["status"]);
      if (response["status"] == 200) {
        return true;
      }else{
        Get.snackbar("เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง",
            duration: const Duration(seconds: 3));
        // return true;
        return false;

      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
      return false;
    }
  }

  // todo check status kyc
  // Future<dynamic> checkstatus(context) async {
  //   try {
  //     if (appConfigCtl.appConfig.value.status_kycsumsub.obs.value == 'Y' &&
  //         appConfigCtl.appConfig.value.status_kycbookbank.obs.value == 'Y') {
  //       if (kycCtl.statusSumSub!.value == "success") {
  //         print("ยืนยันตัวตนสำเร็จ");
  //         checkstatusBookbank(context);
  //       } else if (kycCtl.statusSumSub!.value == "duplicate") {
  //         print("พบข้อมูล Sumsub จากการทำช่องทางอื่น");
  //         checkstatusBookbank(context);
  //       } else if (kycCtl.statusSumSub!.value == "pending") {
  //         Get.snackbar("ไม่สามารถดำเนินการได้",
  //             "คุณอยู่ในระหว่างการตรวจสอบการยืนยันตัวตน \nโปรดรอ 10 นาทีและลองใหม่อีกครั้ง \nหรือจนกว่าจะได้รับการแจ้งเตือน",
  //             duration: const Duration(seconds: 3));
  //       } else if (kycCtl.statusSumSub!.value == "problem") {
  //         Get.snackbar("ไม่สามารถดำเนินการได้",
  //             "เกิดข้อผิดพลาดในการยืนยันตัวตน \nโปรดลองใหม่อีกครั้ง",
  //             duration: const Duration(seconds: 3));
  //       } else {
  //         print('1');
  //         bool chk = await alertDialogSumsub(context, "ขออภัยค่ะ!");
  //
  //         if (chk == true) {
  //           Get.to(const SumsubKyc(),
  //               transition: Transition.rightToLeft,
  //               duration: const Duration(microseconds: 100));
  //         }
  //       }
  //     } else {
  //       bool chk = await alertDialogV3(context, "ขออภัยค่ะ!",
  //           "ขณะนี้เมนูการยืนยันตัวตน\nและเมนูลงทะเบียนสมุดบัญชีปิดการทำงานอยู่\nโปรดรอการเปิดการใช้งานจาก\nAAM Financing");
  //       if (chk == true) {
  //         Get.off(const HomePage(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       }
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }
  //
  // Future<dynamic> checkstatusBookbank(context) async {
  //   try {
  //     // print(kycCtl.statusBookBank.value);
  //     if (kycCtl.statusBookBank!.value == "pending") {
  //       bool chk = await alertDialogV3(context, "ขออภัยค่ะ",
  //           "สถานะสมุดบัญชีของท่านยังอยู่ในระหว่างการตรวจสอบ\n โปรดรอการอนุมัติจาก AAM Financing\n (ภายในวันทำการ ไม่เกิน 48 ชั่วโมง)");
  //       if (chk == true) {
  //         Get.off(const StatusKYC(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       }
  //     } else if (kycCtl.statusBookBank!.value == "success") {
  //       print("ยืนยันสมุดบัญชีสำเร็จ");
  //       checkUpdateAddress(context);
  //     } else if (kycCtl.statusBookBank!.value == "reject") {
  //       bool chk = await alertDialogV3(context, "ขออภัยค่ะ",
  //           "ท่านถูกปฏิเสธการลงทะเบียนสมุดบัญชี\nกรุณาลงทะเบียนสมุดบัญชีอีกครั้งค่ะ");
  //       if (chk == true) {
  //         Get.off(const StatusKYC(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       }
  //     } else {
  //       Get.off(const StatusKYC(),
  //           transition: Transition.rightToLeft,
  //           duration: const Duration(microseconds: 100));
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }

  Future<dynamic> checkUpdateAddress(context) async {
    try {
      print("dsddsdssdddsd######");
      Map data = {
        "cust_code": Get.find<ContractListController>().custCode!.value,
        // "cust_code": "CS202200020",
      };

      final dataResponse = await HttpService.callAPIjwt(
          "POST", Endpoints.checkUpdateAddress, data);

      print("ddsdsddsdsdsdsdssdds");
      print(dataResponse);

      if (dataResponse['status'] == 200) {
        addressAms4!.value = dataResponse['result']['data']['address'];
        addressTextSave!.value = dataResponse['result']['data']['address'];
        controllerAddress.value.text =
            dataResponse['result']['data']['address'];
        mooAms4!.value = dataResponse['result']['data']['moo'];
        mooTextSave!.value = dataResponse['result']['data']['moo'];
        controllerMoo.value.text = dataResponse['result']['data']['moo'];
        sub_districtAms4!.value =
            dataResponse['result']['data']['sub_district'];
        controllerSubDistrict.value.text =
            dataResponse['result']['data']['sub_district'];
        sub_districtCode!.value =
            dataResponse['result']['data']['sub_district_code'];
        districtAms4!.value = dataResponse['result']['data']['district'];
        controllerDistrict.value.text =
            dataResponse['result']['data']['district'];
        districtCode!.value = dataResponse['result']['data']['district_code'];
        provinceAms4!.value = dataResponse['result']['data']['province'];
        controllerProvince.value.text =
            dataResponse['result']['data']['province'];
        provinceCode!.value = dataResponse['result']['data']['province_code'];
        zip_codeAms4!.value = dataResponse['result']['data']['zipcode'];
        update();

        if (dataResponse['result']['statusCode'] == 200) {
          chkUpdateAddress!.value == false;
        } else {
          chkUpdateAddress!.value == true;
          var chk = await AAMPAY_Componance.buildUpdateInfo(context);
          if (chk == false) {
            Get.to(const AAMPayUpdateInfo(),
                transition: Transition.rightToLeft,
                duration: const Duration(microseconds: 100));
          }
        }
        update();
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> getBookBankData() async {
    try {
      Map data = {
        "phone": profileCtl.profile.value.phone.obs.value,
      };
      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getKycBookbank, data);
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาดในการดึงข้อมูลสมุดบัญชี กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  // Future<dynamic> checkstatuskyc(context) async {
  //   try {
  //     if (appConfigCtl.appConfig.value.status_kycsumsub.obs.value == 'Y' &&
  //         appConfigCtl.appConfig.value.status_kycbookbank.obs.value == 'Y') {
  //       if (kycCtl.statusSumSub!.value == "pending") {
  //         kycstate!.value = "กำลังตรวจสอบข้อมูล KYC";
  //         update();
  //       }
  //     } else {
  //       bool chk = await alertDialogV3(context, "ขออภัยค่ะ!",
  //           "ขณะนี้เมนูการยืนยันตัวตน\nและเมนูลงทะเบียนสมุดบัญชีปิดการทำงานอยู่\nโปรดรอการเปิดการใช้งานจาก\nAAM Financing");
  //       if (chk == true) {
  //         Get.off(const HomePage(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       }
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }

  Future<dynamic> getjoblist() async {
    try {
      var data = {};

      final dataResponse =
          await HttpService.callAPIjwt("POST", "getCareer", data);

      if (dataResponse["status"] == 200) {
        typeJobList2!.value = dataResponse["result"];

        for (var i = 0; i < typeJobList2!.length; i++) {
          typeJobList!.add(typeJobList2![i]["job_name"]);
          typeJobforCheck!.add(
              "${typeJobList2![i]["job_id"]}|" + typeJobList2![i]["job_name"]);
        }
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> updateAddressValue(type, value) async {
    if (type == "address") {
      addressTextSave!.value = value;
    } else if (type == "moo") {
      mooTextSave!.value = value;
    }
    checkActiveChangeAddress();
    Get.back();
  }

  //TODO update new address data
  Future<dynamic> updateAddress(context) async {
    try {
      final RegisterAddressController addressController =
          Get.find<RegisterAddressController>();

      if (addressTextSave!.value.isEmpty ||
          mooTextSave!.value.isEmpty ||
          controllerProvince.value.text.isEmpty ||
          controllerDistrict.value.text.isEmpty ||
          controllerSubDistrict.value.text.isEmpty) {
        Get.snackbar(
            "ข้อผิดพลาด", "กรุณากรอกข้อมูลให้ครบถ้วน\nเพื่อไปยังขั้นตอนต่อไป");
      } else {
        Map data = {
          "cust_code": Get.find<ContractListController>().custCode!.value,
          "idcard": profileCtl.profile.value.idcard.obs.value,
          "address_number": addressTextSave!.value,
          "village": mooTextSave!.value,
          "sub_district":
              '${setSelectedSubDistrictCode.value}|${controllerSubDistrict.value.text}',
          "district":
              '${setSelectedDistrictCode.value}|${controllerDistrict.value.text}',
          "province":
              '${setSelectedCityCode.value}|${controllerProvince.value.text}',
          "zipcode": zip_codeTextSave!.value
        };

        // print("dsdsdsdsdsd  updateAddress");
        // print(data);

        final dataResponse = await HttpService.callAPIjwt(
            "POST", Endpoints.updateAddressDigital, data);
        print(dataResponse);
        if (dataResponse["status"] == 200) {
          Get.back();
        } else {
          Get.snackbar("เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง");
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  //todo เซ็ทจำนวน period ที่เลือก จากวงเงิน
  Future<dynamic> checkdrop(context) async {
    try {
      print('เข้า checkdrop');
      openDropdown!.value = false;
      paypermonthall!.clear();

      print(requestloanCal!.value);
      //
      // requestloanCal!.value = int.parse(requestLoan.text.replaceAll(",", ""));
      // update();

      if (requestloanCal!.value == 0) {
        openDropdown!.value = false;
      } else if (requestloanCal!.value == null) {
        openDropdown!.value = false;
      // } else if (requestloanCal!.value >= 1000 &&
      //     requestloanCal!.value <= 3000) {
      //   dropdownchoice!.value = items1!.value;
      //   openDropdown!.value = true;
      // } else if (requestloanCal!.value > 3000 &&
      //     requestloanCal!.value <= 5000) {
      //   dropdownchoice!.value = items2!.value;
      //   openDropdown!.value = true;
      // } else if (requestloanCal!.value > 5000 &&
      //     requestloanCal!.value <= 10000) {
      //   dropdownchoice!.value = items3!.value;
      //   openDropdown!.value = true;
      // } else if (requestloanCal!.value > 10000 &&
      //     requestloanCal!.value <= 20000) {
      //   dropdownchoice!.value = items4!.value;
      //   openDropdown!.value = true;
      } else {
        // openDropdown!.value = false;
        dropdownchoice!.value = items1!.value; //todo 3 เดือน
        openDropdown!.value = true;
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<bool> checkActiveChangeAddress() async {
    if (addressAms4!.value != addressTextSave!.value ||
        mooAms4!.value != mooTextSave!.value ||
        sub_districtAms4!.value != controllerSubDistrict.value.text ||
        districtAms4!.value != controllerDistrict.value.text ||
        provinceAms4!.value != controllerProvince.value.text ||
        zip_codeAms4!.value != zip_codeTextSave!.value &&
            addressTextSave!.value.isNotEmpty &&
            mooTextSave!.value.isNotEmpty &&
            controllerProvince.value.text.isNotEmpty &&
            controllerDistrict.value.text.isNotEmpty &&
            controllerSubDistrict.value.text.isNotEmpty) {
      isUpdateAddress.value = true;
      update();
      return true;
    } else {
      isUpdateAddress.value = false;
      update();
      return false;
    }
  }

  void setDropdownActive(value) {
    isActivePeriod!.value = value;
    update();
  }

  void resetArCardData() {
    paypermonthall!.clear();
    Fee!.value = "";
    interest_remain!.value = "";
    popuppayall!.value = false;
    paypermonth!.value = "";
    update();
  }

  Future<dynamic> arcardPreview(context) async {
    try {
      print("arcardPreview");
      if (selectedPeriod.value == "" || selectedPeriod!.value.isEmpty) {
      } else {
        resetArCardData();
        // var requestloansend = requestLoan.text.replaceAll(",", "");
        Map data = {
          "mn_grand": "${requestloanCal!.value.toString()}",
          "periods": "${selectedPeriod.value.toString()}",
          "ctt_date": "31-08-2564"
        };

        isCalculating.value = true;
        update();

        var response = await HttpService.post_v2(
            Endpoints.arcardPreview, data, HttpService.noKeyRequestHeaders);

        if (response["statusCode"] == 200) {
          isCalculating.value = false;
          update();
          print(response["result"][0]);

          interest_remain!.value = (response["result"][0]["net_total"] -
                  int.parse(loan_amount.value.text.replaceAll(",", "")))
              .toString();

          if (int.parse(selectedPeriod.value) <= 6) {
            for (var i = 0; i < int.parse(selectedPeriod.value); i++) {
              print(response["result"][i]["installment"].toString());
              paypermonthall!
                  .add(response["result"][i]["installment"].toString());
            }
            Fee!.value = '24';
             tax_contract.value = response["result"][int.parse(selectedPeriod.value) - 1]
                    ["expense"]
                .toString();
            popuppayall!.value = true;
            paypermonth!.value = paypermonthall!.value[0];
            update();
          } else if (int.parse(selectedPeriod.value) > 6) {
            for (var i = 0; i < int.parse(selectedPeriod.value); i++) {
              print(response["result"][i]["installment"].toString());

              paypermonthall!
                  .add(response["result"][i]["installment"].toString());
            }
            Fee!.value = '24';
            tax_contract.value = response["result"][int.parse(selectedPeriod.value) - 1]
            ["expense"]
                .toString();
            popuppayall!.value = true;
            paypermonth!.value = paypermonthall![0];
            update();
          }
          net_total!.value =
              response["result"][0]["net_total"] + double.parse(Fee!.value);
          update();
        } else {
          isCalculating.value = false;
          update();
          print("ไม่พบข้อมูล");
          Get.snackbar("แจ้งเตือน", "กรุณาลองใหม่อีกครั้ง");
        }
      }
    } catch (e) {
      isCalculating.value = false;
      update();
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "กรุณาลองใหม่อีกครั้ง",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> confirmContractAndVerify(context) async {
    try {
      Navigator.pop(context, true);

      // todo ยืนยัน otp และสร้างสัญญา aam pay
      sendOTP(context,
          Get.find<ProfileController>().profile.value.phone.obs.string, "");

      startOTPTimer();
      AAMPAY_Componance.buildVerifyOTP(context);
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTP(context, String phone, String typeResend) async {
    try {
      if (phone.length == 10) {
        phone = "+66${phone.substring(1)}";

        SendCode valueSendCode =
            SendCode.fromJson({"phone": phone, "from": "AAMDIGI", "typeSMS": ""});

        print(valueSendCode.toJson());

        // AppLoading.loadingVerify(context);

        final resSendCode = await HttpService.callAPICloudflare(
            "POST", "requertOTP_CF_V2", valueSendCode.toJson());

        // AppLoading.Loaderhide(context);

        print("#########");
        print(resSendCode);
        print(resSendCode["result"]["statusCode"]);

        print("#########");

        // var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
        // var status = resSendCode["result"]["statusCode"];

        if (resSendCode["result"]["statusCode"].toString() == "202") {
          Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
        } else if (resSendCode["result"]["statusCode"].toString() == "200") {
          /// verify otp =>
          refCode!.value = resSendCode["result"]["refCode"];
          update();
          // if (typeResend != "resend") {
          //   Get.to(const VerifyOTPScreen());
          // }
        } else {
          Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
        }
        return resSendCode["result"]["statusCode"];
      } else {
        Get.snackbar("แจ้งเตือน", "กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง");
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {}
  }

  Future<dynamic> checkOTPVerify(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": otpCode,
          "refCode": refCode,
          "fromBU": fromBU,
        });

        final resVerify = await HttpService.callAPICloudflare(
            "POST", "verifyOTP_CF", valueVerify.toJson());
        var responseVerify = ResponseVerifyCode.fromJson(resVerify["result"]);

        var status = resVerify["result"]['statusCode'];
        update();
        print("status : $status");
        return status;
      }
    } catch (e) {
      print('$e => error checkVerify');
    }
  }

  Future<dynamic> setBankvalue() async {
    try {
      print("setBankvalue");
      Map data = {
        "bankName": Get.find<KYCController>()
            .bookbankData
            .value
            .bookbank_name
            .toString(),
      };

      print(data);

      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.assumeBookBankData, data);

      print(response);
      if (response["status"] == 200) {
        bank_save!.value = response["result"];
      } else {
        print("ไม่พบข้อมูล");
        bank_save!.value = "";
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  // Future<dynamic> checkOtp(context) async {
  //   try {
  //     final otp = Get.put(OtpController());
  //     await otp.chkOTP(
  //         context,
  //         profileCtl.profile.value.phone.obs.string,
  //         profileCtl.profile.value.firstname.obs.string,
  //         profileCtl.profile.value.lastname.obs.string);
  //
  //     if (otp.otpStatus.value == true) {
  //       await otp.resetOtpData();
  //
  //       Get.to(const AAMPaysuccess(),
  //           transition: Transition.rightToLeft,
  //           duration: const Duration(microseconds: 100));
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }

  /// ##### สร้างสัญญา AAM PAY  #####
  Timer timer = Timer.periodic(const Duration(seconds: 1), (timer) {});

  void startTimer(context) {
    print("startTimer");
    const oneSec = Duration(seconds: 1);
    timer = Timer.periodic(
      oneSec,
      (Timer timer) {
        if (start!.value == 0) {
          timer.cancel();
        } else {
          start!.value--;
          update();
        }

        /// ส่งแจ้งเตือนเข้ากระบวนการไปห้อง TG ตอนเหลือ 59 วิ
        if (start!.value == 59) {
          sendCheckLogAAMPAY(
              context, "ระบบกำลังบันทึกข้อมูลสินเชื่อ AAM PAY", "");
        }
      },
    );
  }

  Future<dynamic> sendCheckLogAAMPAY(
      context, String msg, String cttCode) async {
    try {
      Map data = {
        "msg": msg,
        "name":
            "${profileCtl.profile.value.firstname.obs.string} ${profileCtl.profile.value.lastname.obs.string}",
        "phone": profileCtl.profile.value.phone.obs.string,
        "ctt_code": cttCode,
        "cust_code": contractlistCtl.custCode!.value,
        "time": DateTime.now().toString().split('.')[0]
      };

      final sendTelegram =
          await HttpService.callAPICloudflare("POST", "sendTelegramLogAAMPay", data);

      if (sendTelegram["status"] == 200) {
        print("ส่งแจ้งเตือนสำเร็จ");
      } else {
        print("ส่งแจ้งเตือนไม่สำเร็จ");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  // todo สร้างสัญญา AAM PAY
  Future<void> requestloan_digital(context) async {
    try {
      AppLoading.loadingVerify(Get.context!);
      await setBankvalue();
      await updateDigitalAgreementEnd(context);
      AppLoading.Loaderhide(Get.context!);

      if (updateAgreement!.value == true && bank_save!.value != "") {
        sendCheckLogAAMPAY(context, "ระบบกำลังบันทึกข้อมูลสินเชื่อ AAM PAY", "");

        var time = DateTime.now();
        // var mn_grand = (int.tryParse(loan_amount.value.text.replaceAll(',', '')) ?? 0).toString();
        // print("mn_grand : $mn_grand");

        Map data = {
          "mn_grand": (int.tryParse(loan_amount.value.text.replaceAll(',', '')) ?? 0).toString(),
          "periods": selectedPeriod.value,
          "guarantee_id": contractlistCtl.contractDigi.value.guarantee_id.toString(),
          "cust_code": contractlistCtl.custCode!.value,
          "ctt_date": time.toString().split(" ")[0],
          "bank": bank_save!.value,
          "bank_branch": Get.find<KYCController>().bookbankData.value.bookbank_branch,
          "bank_account": Get.find<KYCController>().bookbankData.value.bookbank_number
        };

        print("data : $data");

        // ตั้งค่าเริ่มต้น
        await storage.write("aampay_contract_status", "pending");
        aampay_contract_status!.value = "pending";
        update();

        var uriParse = Uri.parse(Endpoints.createAAMPAYContract.toString());
        final responseFuture = http.post(uriParse, body: data);

        print("## ยิง API แล้วไม่รอ (ทำงาน background)");
        responseFuture.then((response) {
          // แปลง response เป็น JSON หรือรูปแบบที่ต้องการ
          var responseData = jsonDecode(response.body); // สมมติว่า API คืนค่า JSON

          if (responseData["status"] == false) {
            sendCheckLogAAMPAY(Get.context!, "บันทึกข้อมูลไม่สำเร็จ", "");
            Get.snackbar("ข้อผิดพลาด", "สร้างสัญญาไม่สำเร็จ กรุณาลองใหม่");
            createContractFailed!.value = true;
          } else {
            sendCheckLogAAMPAY(Get.context!, "บันทึกข้อมูลสำเร็จ", responseData["ctt_code"]);
            aampay_ctt_code.value = responseData["ctt_code"];
            storage.write("aampay_contract_status", "completed");
            aampay_contract_status!.value = "completed";
            contractListCtl.customerContactList();
          }
          update();
        }).catchError((error) {
          print("เกิดข้อผิดพลาด: $error");
          Get.snackbar("ข้อผิดพลาด", "มีปัญหาการเชื่อมต่อ กรุณาลองใหม่");
          createContractFailed!.value = true;
          update();
        });
        print("## เปลี่ยนหน้าไปก่อน");
        Get.off(() => AAMPayConfirmScreen(), transition: Transition.rightToLeft, duration: const Duration(milliseconds: 100));
      } else {
        Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
      }
    } catch (exception, stackTrace) {
      createContractFailed!.value = true;
      update();
      await Sentry.captureException(exception, stackTrace: stackTrace);
      if (kDebugMode) print(exception);
    }
  }

  Future<void> requestloan_digital_v4AI(context) async {
    try {
      AppLoading.loadingVerify(Get.context!);
      await setBankvalue();
      await updateDigitalAgreementEnd(context);
      AppLoading.Loaderhide(Get.context!);

      if (updateAgreement!.value == true && bank_save!.value != "") {
        sendCheckLogAAMPAY(context, "ระบบกำลังบันทึกข้อมูลสินเชื่อ AAM PAY", "");

        var time = DateTime.now();
        Map data = {
          "mn_grand": loan_amount.value.text,
          "periods": selectedPeriod.value,
          "guarantee_id": contractlistCtl.contractDigi.value.guarantee_id.toString(),
          "cust_code": contractlistCtl.custCode!.value,
          "ctt_date": time.toString().split(" ")[0],
          "bank": bank_save!.value,
          "bank_branch": Get.find<KYCController>().bookbankData.value.bookbank_branch.obs,
          "bank_account": Get.find<KYCController>().bookbankData.value.bookbank_number.obs
        };

        print("data : $data");

        // ตั้งค่าเริ่มต้น
        storage.write("aampay_contract_status", "pending");
        aampay_contract_status!.value = "pending";
        update();

        print("## ยิง API แล้วไม่รอ (ทำงาน background)");
        Future.microtask(() async {
          try {
            final response = await HttpService.apiPostRequest2(Uri.parse(Endpoints.createAAMPAYContract), data);
            print("## API Response: $response");

            if (response["status"] == false) {
              sendCheckLogAAMPAY(Get.context!, "บันทึกข้อมูลไม่สำเร็จ", "");
              Get.snackbar("ข้อผิดพลาด", "สร้างสัญญาไม่สำเร็จ กรุณาลองใหม่");
              createContractFailed!.value = true;
            } else {
              sendCheckLogAAMPAY(Get.context!, "บันทึกข้อมูลสำเร็จ", response["ctt_code"]);
              aampay_ctt_code.value = response["ctt_code"];
              storage.write("aampay_contract_status", "completed");
              aampay_contract_status!.value = "completed";
              contractListCtl.customerContactList();
            }
            update();
          } catch (error, stackTrace) {
            createContractFailed!.value = true;
            update();
            Sentry.captureException(error, stackTrace: stackTrace);
            if (kDebugMode) print(error);
          }
        });

        print("## เปลี่ยนหน้าไปก่อน");
        Get.off(() => AAMPayConfirmScreen(), transition: Transition.rightToLeft, duration: const Duration(milliseconds: 100));
      } else {
        Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
      }
    } catch (exception, stackTrace) {
      createContractFailed!.value = true;
      update();
      await Sentry.captureException(exception, stackTrace: stackTrace);
      if (kDebugMode) print(exception);
    }
  }

  Future<void> requestloan_digital_v3(context) async {
    try {
      AppLoading.loadingVerify(Get.context!);
      await setBankvalue();
      await updateDigitalAgreementEnd(context);
      AppLoading.Loaderhide(Get.context!);

      if (updateAgreement!.value == true && bank_save!.value != "") {
        sendCheckLogAAMPAY(
            context, "ระบบกำลังบันทึกข้อมูลสินเชื่อ AAM PAY", "");

        var time = DateTime.now();

        Map data = {
          "mn_grand": loan_amount.value.text,
          "periods": selectedPeriod.value,
          "guarantee_id": contractlistCtl.contractDigi.value.guarantee_id.toString(),
          "cust_code": contractlistCtl.custCode!.value,
          "ctt_date": time.toString().split(" ")[0],
          "bank": bank_save!.value,
          "bank_branch": Get.find<KYCController>().bookbankData.value.bookbank_branch.obs,
          "bank_account": Get.find<KYCController>().bookbankData.value.bookbank_number.obs
        };

        print("data : $data");

        // อัพเดทสถานะเริ่มต้น
        storage.write("aampay_contract_status", "pending");
        aampay_contract_status!.value = "pending";
        update();

        print("## สร้างสัญญา AAM PAY ทันทีโดยไม่รอ response");
        print(Endpoints.createAAMPAYContract);

        // ยิง API แบบไม่รอ และเปลี่ยนหน้าไปทันที
        HttpService.apiPostRequest2(Uri.parse(Endpoints.createAAMPAYContract), data)
            .then((response) {
          // ประมวลผล response ใน background
          print("## ผลลัพธ์จาก API ใน background");
          print(response);

          if (response["status"] == false) {
            sendCheckLogAAMPAY(
                Get.context!,
                "บันทึกข้อมูลสินเชื่อ AAM PAY ไม่สำเร็จ\nมีสัญญาดิจิทัลครบ 2 สัญญาแล้ว",
                "");
            Get.snackbar("ข้อผิดพลาด", "สร้างสัญญาไม่สำเร็จ กรุณาทำรายการใหม่อีกครั้ง");
            createContractFailed!.value = true;
            update();
          } else {
            sendCheckLogAAMPAY(Get.context!,
                "บันทึกข้อมูลสินเชื่อ AAM PAY สำเร็จ", response["ctt_code"]);
            aampay_ctt_code.value = response["ctt_code"];
            update();

            if (response["ar_card"] == false || response["money"] == false) {
              Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
            } else if (response["ar_card"] == true && response["money"] == true) {
              Future.delayed(const Duration(seconds: 3), () {
                storage.write("aampay_contract_status", "completed");
                aampay_contract_status!.value = "completed";
                update();
                contractListCtl.customerContactList();
              });
            }
          }
        }).catchError((error, stackTrace) {
          createContractFailed!.value = true;
          update();
          Sentry.captureException(error, stackTrace: stackTrace);
          if (kDebugMode) {
            print(error);
          }
        });

        print("## เปลี่ยนหน้า");
        Future.delayed(const Duration(seconds: 3), () {
          // เปลี่ยนหน้าไปทันทีโดยไม่รอ response
          Get.off(() => AAMPayConfirmScreen(),
              transition: Transition.rightToLeft,
              duration: const Duration(milliseconds: 100));
        });

      } else {
        Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
      }
    } catch (exception, stackTrace) {
      createContractFailed!.value = true;
      update();
      await Sentry.captureException(exception, stackTrace: stackTrace);
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> requestloan_digital_v2(context) async {
    try {
      AppLoading.loadingVerify(Get.context!);
      await setBankvalue();
      await updateDigitalAgreementEnd(context);
      AppLoading.Loaderhide(Get.context!);
      if (updateAgreement!.value == true && bank_save!.value != "") {
        sendCheckLogAAMPAY(
            context, "ระบบกำลังบันทึกข้อมูลสินเชื่อ AAM PAY", "");

        var time = DateTime.now();

        Map data = {
          "mn_grand": loan_amount.value.text,
          "periods": selectedPeriod.value,
          "guarantee_id": contractlistCtl.contractDigi.value.guarantee_id.toString(),
          "cust_code": contractlistCtl.custCode!.value,
          "ctt_date": time.toString().split(" ")[0],
          "bank": bank_save!.value,
          "bank_branch": Get.find<KYCController>().bookbankData.value.bookbank_branch.obs,
          "bank_account": Get.find<KYCController>().bookbankData.value.bookbank_number.obs
        };

        print("data : $data");

        print("## process on backend");
        // Future.delayed(const Duration(seconds: 1), () {
          storage.write("aampay_contract_status", "pending");
          aampay_contract_status!.value = "pending";
          update();
        // });



        print("## สร้างสัญญา AAM PAY ทันทีโดยไม่รอ response");
        print(Endpoints.createAAMPAYContract);
        // เริ่มส่งคำขอ API และไปหน้าอื่นทันที
        // final futureResponse = HttpService.post_v2(Endpoints.createAAMPAYContract, data, HttpService.noKeyRequestHeaders);
        final futureResponse = HttpService.apiPostRequest2(Uri.parse(Endpoints.createAAMPAYContract), data);


        // ไปหน้า AAMPayConfirmScreen ทันทีโดยไม่รอ response
        Get.to(() => AAMPayConfirmScreen(),
            transition: Transition.rightToLeft,
            duration: const Duration(milliseconds: 100));

        print("## รอผลลัพธ์จาก API ใน background และประมวลผลต่อ");
        // รอผลลัพธ์จาก API ใน background และประมวลผลต่อ
        final response = await futureResponse;
        print("## รอผลลัพธ์จาก API ใน background และประมวลผลต่อ");
        print(response);

        if (response["status"] == false) {
          await sendCheckLogAAMPAY(
              Get.context!,
              "บันทึกข้อมูลสินเชื่อ AAM PAY ไม่สำเร็จ\nมีสัญญาดิจิทัลครบ 2 สัญญาแล้ว",
              "");
          // ไม่ต้องใช้ Get.off ที่นี่ เพราะหน้าถูกเปลี่ยนไปแล้ว
          Get.snackbar("ข้อผิดพลาด", "สร้างสัญญาไม่สำเร็จ กรุณาทำรายการใหม่อีกครั้ง");
          createContractFailed!.value = true;
          update();
        } else {
          await sendCheckLogAAMPAY(Get.context!,
              "บันทึกข้อมูลสินเชื่อ AAM PAY สำเร็จ", response["ctt_code"]);
          aampay_ctt_code.value = response["ctt_code"];
          update();

          if (response["ar_card"] == false || response["money"] == false) {
            Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
          } else if (response["ar_card"] == true && response["money"] == true) {

            Future.delayed(const Duration(seconds: 3), () {
              storage.write("aampay_contract_status", "completed");
              aampay_contract_status!.value = "completed";
              update();
              contractListCtl.customerContactList(); // ดึงข้อมูลสัญญาใหม่
            });
          }
        }
      } else {
        // AppLoading.Loaderhide(context);
        Get.snackbar("ข้อผิดพลาด", "กรุณาทำรายการใหม่อีกครั้ง");
      }
    } catch (exception, stackTrace) {
      createContractFailed!.value = true;
      update();
      // AppLoading.Loaderhide(context);
      await Sentry.captureException(exception, stackTrace: stackTrace);
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> requestloan_digital_(context) async {
    try {
      // startTimer(context);
      await setBankvalue();
      await updateDigitalAgreementEnd(context);
      // updateAgreement!.value = true; //ปิด
      // update();

      if (updateAgreement!.value == true && bank_save!.value != "") {
        var time = DateTime.now();
        Map data = {
          "mn_grand": loan_amount.value.text,
          "periods": selectedPeriod.value,
          // "guarantee_id": contractlistCtl.contractList[0].guarantee_id,
          "cust_code": contractlistCtl.custCode!.value,
          "ctt_date": time.toString().split(" ")[0],
          "bank": bank_save!.value, // todo ชื่อธนาคาร
          "bank_branch": Get.find<KYCController>()
              .bookbankData
              .value
              .bookbank_branch
              .obs, // todo สาขาธนาคาร
          "bank_account": Get.find<KYCController>()
              .bookbankData
              .value
              .bookbank_number
              .obs // todo เลขที่บัญชี
        };

        // print("data : $data");


        //TODO test process
        // print("## process on backend");
        // storage.write("aampay_contract_status", "pending");
        // aampay_contract_status!.value = "pending";
        // update();
        //
        // Future.delayed(const Duration(seconds: 10), () {
        //   storage.write("aampay_contract_status", "tranfer");
        //   aampay_contract_status!.value = "tranfer";
        //   update();
        // });
        //
        // Future.delayed(const Duration(seconds: 10), () {
        //   storage.write("aampay_contract_status", "completed");
        //   aampay_contract_status!.value = "completed";
        //   update();
        //   //TODO ดึงข้อมูลสัญญาใหม่
        //   contractListCtl.customerContactList();
        // });
        //TODO test process

        //TODO set status on process
        print("## process on backend");
        await storage.write("aampay_contract_status", "pending");
        aampay_contract_status!.value = "pending";
        update();


        //todo production process
        final response = await HttpService.post(Endpoints.createAAMPAYContract, data, HttpService.noKeyRequestHeaders);

        Get.off(() => AAMPayConfirmScreen(),
            transition: Transition.rightToLeft,
            duration: const Duration(milliseconds: 100));


        if (response["status"] == false) {

          //TODO ถ้าบันทึกข่้อมูล และสร้างการ์ดไม่สำเร็จ!! ส่งแจ้งเตือน TG
          await sendCheckLogAAMPAY(
              context,
              "บันทึกข้อมูลสินเชื่อ AAM PAY ไม่สำเร็จ\nมีสัญญาดิจิทัลครบ 2 สัญญาแล้ว",
              "");
          createContractFailed!.value = true;
          update();
            Get.off(const HomePage(),
                transition: Transition.rightToLeft,
                duration: const Duration(microseconds: 100));

        } else {
          contractListCtl.checkGuaranteeLoanLimit();
          // TODO บันทึกข่้อมูล และสร้างการ์ดสำเร็จ ส่งแจ้งเตือน TG
          await sendCheckLogAAMPAY(context,
              "บันทึกข้อมูลสินเชื่อ AAM PAY สำเร็จ", response["ctt_code"]);
          //todo สร้างสัญญาสำเร็จ
          aampay_ctt_code.value = response["ctt_code"];
          update();



          if (response["ar_card"] == false || response["money"] == false) {
            Get.snackbar("ข้อผิดพลาด", "กรุณารอรับ OTP ใหม่อีกครั้ง");
          } else if (response["ar_card"] == true && response["money"] == true) {
            // aampay_ctt_code.value = response["ctt_code"];

            //TODO ถ้าเช็คสถานะโอนเงินด้วยโค้ดข้างล่างอาจต้องปรับ
            Future.delayed(const Duration(seconds: 3), () {
              storage.write("aampay_contract_status", "completed");
              aampay_contract_status!.value = "completed";
              update();
           //TODO ดึงข้อมูลสัญญาใหม่
             contractListCtl.customerContactList();
            });
          }
        }
        //todo production process
      } else {
        Get.snackbar("ข้อผิดพลาด", "กรุณารอรับ OTP ใหม่อีกครั้ง");
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void startOTPTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (counter_OTP.value > 0) {
        counter_OTP.value--;
        update();
      } else {
        timer.cancel();
      }
    });
    print('start timer');
    print(counter_OTP.value);
  }

  Future<dynamic> checkResendOTPConfig(context) async {
    try {
      if (counter_OTP.value == 0) {
        // resetData();

        // var phone =
        //     Get.find<ProfileController>().profile.value.phone.obs;

        sendOTP(context, Get.find<ProfileController>().profile.value.phone.obs.toString(), 'resend');
        otpController.value.text = "";
        refCode!.value = "";
        counter_OTP.value = 60;
        update();
        startOTPTimer();
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  void checkOtpFormat(context, value) async {
    if (otpController.value.text.length == 6) {
      setVerify(true);
      print("dfdfdfdffd");
      // TODO verify otp then if true
      var chkOtp = await checkOTPVerify(
          context,
          Get.find<ProfileController>().profile.value.phone.obs.string,
          otpController.value.text,
          refCode!.value,
          'AAMDIGI');
      print('chkOtp : ${chkOtp.toString()}');
      if (chkOtp.toString() == '200') {
        requestloan_digital(context);
      } else {
        isAlert!.value = true;
        setVerify(false);
        update();
      }
    } else {
      // isAlert!.value = true;
      setVerify(false);
      update();
    }
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  Future<dynamic> updateOldAddress(context) async {
    try {
      Map data = {
        "cust_code": Get.find<ContractListController>().custCode!.value,
        "idcard": Get.find<ProfileController>().profile.value.idcard,
        "address_number": addressAms4!.value,
        "village": mooAms4!.value,
        "sub_district": '${sub_districtCode!.value}|${sub_districtAms4!.value}',
        "district": '${districtCode!.value}|${districtAms4!.value}',
        "province": '${provinceCode!.value}|${provinceAms4!.value}',
        "zipcode": zip_codeAms4!.value
      };

      // print(data);
      // print(Endpoints.updateAddressDigital);
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.updateAddressDigital, data);

      // print(response);

      if (response['status'] == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> updateDigitalAgreementEnd(context) async {
    try {
      Map data = {
        "phone_firebase": profileCtl.profile.value.phoneFirebase.obs.string,
        "app_agree_status": "N",
      };

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.updateDigitalAgreement, data);

      // print(response);
      if (response["status"] == 200) {
        updateAgreement!.value = true;
        update();
      } else {
        updateAgreement!.value = false;
        update();
        Get.snackbar("เกิดข้อผิดพลาด", "กรุณาลองใหม่อีกครั้ง");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<void> checkPendingStatus() async {

    var value = await storage.read('aampay_contract_status');
    if (value.toString().isNotEmpty ||
        value.toString().toLowerCase() != 'null' ||
        value.toString() != '') {
      // print('checkPendingStatus : $value');
      aampay_contract_status!.value = value;
      update();
    }
  }

  void setDropdown(context, String type, int value) {
    final RegisterAddressController addressController =
        Get.find<RegisterAddressController>();
    if (type == accountAddressProvince.tr) {
      // selectedProvince!.value = addressController.citiesData[value].cityName.toString();
      controllerProvince.value.text =
          addressController.citiesData[value].cityNameLocal.toString();
      addressController
          .setSelectedCityCode(addressController.citiesData[value].cityId!);
      controllerDistrict.value.clear();
      controllerSubDistrict.value.clear();
      update();
    } else if (type == accountAddressDistrict.tr) {
      // selectedDistrict!.value = addressController.districtsData[value].districtName.toString();
      controllerDistrict.value.text =
          addressController.districtsData[value].districtNameLocal.toString();
      zip_codeTextSave!.value =
          addressController.districtsData[value].districtZipcode.toString();

      addressController.setSelectedDisCode(
          addressController.districtsData[value].districtId!);

      controllerSubDistrict.value.clear();
      update();
    } else if (type == accountAddressSubDistrict.tr) {
      // selectedSubDistrict!.value = addressController.subDistrictsData[value].subDistrictName.toString();
      controllerSubDistrict.value.text = addressController
          .subDistrictsData[value].subDistrictNameLocal
          .toString();
      addressController.setSelectedSubDisCode(
          addressController.subDistrictsData[value].subDistrictId!);
      update();
    }
    update();
    checkActiveChangeAddress();
    Navigator.pop(context);
  }

  Future<void> resetData() async {
    loan_amount.value.text = '0';
    otpController.value.text = "";
    refCode!.value = "";
    counter_OTP.value = 60;
    selectedPeriod.value = '';
    selectedPeriodIndex.value = 0;
    isActivePeriod.value = false;
    isAcceptedTermPolicy!.value = false;
    isVerify!.value = false;
    isAlert!.value = false;
    aampay_contract_status!.value = "";
    chkUpdateAddress!.value = false;
    update();
    await storage.write('aampay_contract_status', '');

  }

  void setDropdownAddress(context, String type, int value) {
    final RegisterAddressController addressController =
        Get.find<RegisterAddressController>();

    if (type == accountAddressProvince.tr) {
      selectedProvince!.value = addressController.citiesData[value].cityNameLocal.toString();
      setSelectedCityCode!.value = addressController.citiesData[value].cityId!.toString();
      controllerProvince.value.text = addressController.citiesData[value].cityNameLocal.toString();


      // reset
      selectedDistrict!.value = '';
      setSelectedDistrictCode!.value = '';
      zip_codeTextSave!.value = '';
      controllerDistrict.value.text = '';
      selectedSubDistrict!.value = "";
      setSelectedSubDistrictCode!.value = "";
      controllerSubDistrict.value.text = "";
      update();


      addressController.idCity = addressController.citiesData[value].cityId!;
      addressController.update();
      addressController.getDistrictData();


      // print("selectedProvince : ${selectedProvince!.value}");
      // print("setSelectedCityCode : ${setSelectedCityCode!.value}");
    } else if (type == accountAddressDistrict.tr) {
      selectedDistrict!.value = addressController.districtsData[value].districtNameLocal.toString();
      setSelectedDistrictCode!.value =  addressController.districtsData[value].districtId.toString();
      zip_codeTextSave!.value = addressController.districtsData.value[value].districtZipcode.toString();
      controllerDistrict.value.text = addressController.districtsData[value].districtNameLocal.toString();


      // reset
      selectedSubDistrict!.value = "";
      setSelectedSubDistrictCode!.value = "";
      controllerSubDistrict.value.text = "";
      update();
      // print(addressController.districtsData[value].districtId!);
      // print("selectedDistrict : ${selectedDistrict!.value}");
      // print("setSelectedDistrictCode : ${setSelectedDistrictCode!.value}");
      // print("zip_codeTextSave : ${zip_codeTextSave!.value}");


      addressController.idDistrict = addressController.districtsData[value].districtId!;
      addressController.update();
      addressController.getSubDistrictData();

    } else if (type == accountAddressSubDistrict.tr) {
      selectedSubDistrict!.value = addressController.subDistrictsData[value].subDistrictNameLocal.toString();
      setSelectedSubDistrictCode!.value = addressController.subDistrictsData[value].subDistrictId.toString();
      controllerSubDistrict.value.text = addressController.subDistrictsData[value].subDistrictNameLocal.toString();
      // addressController.setSelectedSubDisCode(addressController.subDistrictsData[value].subDistrictId!);
      update();
      // print("selectedSubDistrict : ${selectedSubDistrict!.value}");
      // print("setSelectedSubDistrictCode : ${setSelectedSubDistrictCode!.value}");
    }
    update();
    Navigator.pop(context);
  }
}
