import 'dart:io';

import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/sms/send.request.controller.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/screen/profile/deleteAccount_success.dart';

import '../../view/componance/themes/theme.dart';
import '../sms/verification.controller.dart';

class DeleteAccountController extends GetxController {
  RxBool? checkList1 = false.obs;
  RxBool? checkList2 = false.obs;
  RxBool? checkList3 = false.obs;

  RxBool? isAlert = false.obs;
  RxBool? isVerify = false.obs;
  Rx<TextEditingController> otpController = TextEditingController().obs;
  Rx<TextEditingController> controllerDeleteAcc = TextEditingController().obs;
  RxString? delete_reason = "".obs;

  void setCheckList(int index, String reason_delete) {
    if (index == 0) {
      checkList1!.value = !checkList1!.value;
      if (checkList1!.value) {
        checkList2!.value = false;
        checkList3!.value = false;
      }
    } else if (index == 1) {
      checkList2!.value = !checkList2!.value;
      if (checkList2!.value) {
        checkList1!.value = false;
        checkList3!.value = false;
      }
    } else {
      checkList3!.value = !checkList3!.value;
      if (checkList3!.value) {
        checkList1!.value = false;
        checkList2!.value = false;
      }
    }
    delete_reason!.value = reason_delete; // TODO ประเภทของ feedback
    update();
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  Future<void> setFormatPhoneCode() async {
    final SendRequestSMSController sendRequestSMSController =
        Get.put(SendRequestSMSController());
    var phone_code = "";
    if (Get.find<ProfileController>()
        .profile
        .value
        .phoneFirebase
        .toString()
        .startsWith("+66")) {
      phone_code = Get.find<ProfileController>()
          .profile
          .value
          .phoneFirebase
          .toString()
          .substring(0, 3); // ตัด +66
      print('phone_code : $phone_code');
    }else{
      phone_code = Get.find<ProfileController>()
          .profile
          .value
          .phoneFirebase
          .toString()
          .substring(0, 4); // เบอร์ต่างประเทศ
      print('phone_code : $phone_code');
    }
    sendRequestSMSController.setPhoneCode(phone_code);
  }

  void checkOtpFormat(context, value) async {
    if (otpController.value.text.length == 6) {
      setVerify(true);
      // TODO verify otp then if true
      var chkOtp = await Get.find<VerificationSMSController>()
          .checkVerifyOTPConfig(
              context,
              Get.find<ProfileController>().profile.value.phone.toString(),
              otpController.value.text);
      print('chkOtp : ${chkOtp.toString()}');
      if (chkOtp.toString() == '200') {
        // TODO verify otp success
        setStatusDeleteAccount();
      } else {
        isAlert!.value = true;
        setVerify(false);
        update();
      }
    } else {
      setVerify(false);
      isAlert!.value = true;
      update();
    }
  }

  Future<dynamic> setStatusDeleteAccount() async {
    try {
      var chk_feedback = await saveFeedbackDeleteAccount();

      print('chk_feedback : $chk_feedback');
      if (chk_feedback == true) {
        Map data = {
          "phone_firebase": Get.find<ProfileController>()
              .profile
              .value
              .phoneFirebase
              .toString()
        };

        // print('data : $data');

        // print(Endpoints.saveDisableAccount);

        final response = await HttpService.callAPIjwt(
            "POST", Endpoints.saveDisableAccount, data);

        if (response["status"] == 200) {
          debugPrint("delete account success");
          // debugPrint(response["result"]["msg"]);
          // TODO delete account success screen
          Get.off(const DeleteAccountSuccess());
        } else {
          debugPrint("delete account fail");
        }
      } else {
        Get.snackbar("error feedback", "pleas try again");
      }
    } catch (e) {
      print(e);
      Get.snackbar("error", "pleas try again");
    }
  }

  Future<dynamic> saveFeedbackDeleteAccount() async {
    try {
      Map data = {
        "point": "-",
        "message": controllerDeleteAcc.value.text, // TODO คำแนะนำเพิ่มเติม
        "type": delete_reason!.value, // TODO ประเภทของ feedback
        "image": "-",
        "os": Platform.operatingSystem,
        "fullname":
            "${Get.find<ProfileController>().profile.value.firstname} ${Get.find<ProfileController>().profile.value.lastname}",
        "phone": Get.find<ProfileController>().profile.value.phone.toString(),
        "phone_firebase":
            Get.find<ProfileController>().profile.value.phoneFirebase.toString()
      };

      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.saveDeleteFeedback, data);

      if (response["status"] == 200) {
        debugPrint("save feedback success");
        // debugPrint(response["result"]["msg"]);
        return true;
      } else {
        debugPrint("save feedback fail");
        return false;
      }
    } catch (e) {
      debugPrint("save feedback fail");
      return false;
    }
  }

  Future<dynamic> checkDeleteAccount(String phone_firebase) async {
    try {
      Map data = {"phone_firebase": phone_firebase};
      print('data : $data');

      debugPrint(Endpoints.checkDeleteAccount);
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.checkDeleteAccount, data);
      debugPrint('response : $response');

      if (response["status"] == 200) {
        // print(response["result"]["account_status"]);
        if (response["result"]["account_status"] == "disable") {
          debugPrint(
              "account is disable have time left : ${response["result"]["time_left"]}");
          // Get.snackbar("Alert", "account is disable");
          return {
            "status": false,
            "status_account": "disable",
          };
        } else if (response["result"]["account_status"] == "delete") {
          // Get.snackbar("Alert", "account is already deleted");
          return {
            "status": false,
            "status_account": "delete",
          };
        } else if (response["result"]["account_status"] == "normal") {
          debugPrint("normal account");
          return true;
        }
        return false;
      } else if (response["status"] == 404) {
        debugPrint("normal account");
        return true;
      } else {
        debugPrint("account not found");
        return true;
      }
    } catch (e) {
      Get.snackbar("error", "check status account fail");
      return true;
    }
  }

  void resetOTP() {
    otpController.value.clear();
    setVerify(false);
    isAlert!.value = false;
    update();
  }
}
