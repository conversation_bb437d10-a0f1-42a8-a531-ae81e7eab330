import 'dart:convert';
import 'dart:io';

import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/view/screen/profile/edit_profile.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../service/http_service.dart';
import '../../service/endpoint.dart';
import '../transalation/translation_key.dart';
import 'profile.controller.dart';

class EditProfileController extends GetxController {
  RxBool? isEmailValid = false.obs;
  RxBool? isIdCardValid = false.obs;
  RxString? selectedProvince = ''.obs;
  RxString? selectedDistrict = ''.obs;
  RxString? selectedSubDistrict = ''.obs;
  RxString? selectedAddress = ''.obs;
  RxString? selectedZipCode = ''.obs;
  RxString? selectedBranch = ''.obs;

  RxString? selectEmail = ''.obs;
  RxString? selectIdCard = ''.obs;
  File? imageFile;
  RxBool? isEdit = false.obs;





  Rx<TextEditingController> controllerAddress = TextEditingController().obs;
  Rx<TextEditingController> controllerMoo = TextEditingController().obs;
  Rx<TextEditingController> controllerSubDistrict = TextEditingController().obs;
  Rx<TextEditingController> controllerDistrict = TextEditingController().obs;
  Rx<TextEditingController> controllerProvince = TextEditingController().obs;

  final RegisterAddressController addressController =
  Get.put(RegisterAddressController());

  // AppConfigService appConfig = AppConfigService(context: Get.context!);
  AppConfigService appConfig = Get.find<AppConfigService>();
  void setIsEmailValid(bool status, String email) {
    isEmailValid!.value = status;
    selectEmail!.value = email;
    update();
  }

  void setIsIdCardValid(bool status, String idcard) {
    isIdCardValid!.value = status;
    selectIdCard!.value = idcard;
    update();
  }

  void setDropdown(context, String type, int value) {
    if (type == accountAddressProvince.tr) {
      selectedProvince!.value = addressController.citiesData[value].cityNameLocal.toString();
      addressController.setSelectedCityCode(addressController.citiesData[value].cityId!);
      update();
    } else if (type == accountAddressDistrict.tr) {
      selectedDistrict!.value = addressController.districtsData[value].districtNameLocal.toString();
      addressController.setSelectedDisCode(addressController.districtsData[value].districtId!);
      // print(addressController.districtsData[value].districtId!);
      update();
    } else if (type == accountAddressSubDistrict.tr) {
      selectedSubDistrict!.value = addressController.subDistrictsData[value].subDistrictNameLocal.toString();
      // addressController.setSelectedSubDisCode(addressController.subDistrictsData[value].subDistrictId!);
      update();
    }
    update();
    Navigator.pop(context);
  }

  Future<dynamic> updateProfile(context, String typeData) async {
    try {
      final ProfileController profileController = Get.find<ProfileController>();

      Map data = {};

      if (typeData == 'email') {
        data = {
          'phone_firebase':
          profileController.profile.value.phoneFirebase.toString(),
          'type': 'email',
          'data': {
            'email' :selectEmail!.value,
          }
        };
      } else if (typeData == 'idcard') {
        data = {
          'phone_firebase':
          profileController.profile.value.phoneFirebase.toString(),
          'type': 'idcard',
          'data': {
            'idcard' :selectIdCard!.value,
          }
        };
      } else if (typeData == 'address') {
        data = {
          'phone_firebase':
          profileController.profile.value.phoneFirebase.toString(),
          'type': 'address',
          'data': {
            'address': "${controllerAddress.value.text ??''} ${controllerMoo.value.text ??'' }" ?? "",
            'sub_district': selectedSubDistrict!.value??'' ,
            'district': selectedDistrict!.value,
            'province': selectedProvince!.value,
            'zipcode': "",
            'branch': "",
          },
        };
        update();
        print(data);
      }
      update();
      print("EditProfileController updateProfile data");
      print(data);
      final response =
      await HttpService.callAPIjwt("POST", Endpoints.updateProfile, data);
      print(response);
      if (response["status"] == 200 && response["result"]['status'].toString() == 'true' ) {
        print('success');
        Get.snackbar('Success', 'Update profile success');
        await profileController.getProfile();
        // Navigator.push(context, MaterialPageRoute(builder: (context) => EditProfilePage()));
        // Get.back();
        update();
        Navigator.pop(context);
      } else {
        Get.snackbar('Error', 'Update profile failed');
      }
    } catch (e) {
      print(e);
    }
  }

  void camera() async {
    final ImagePicker _picker = ImagePicker();

    final cameraImage = await _picker.pickImage(source: ImageSource.camera);
    imageFile = cameraImage != null ? File(cameraImage.path) : null;

    upLoadToS3();
  }

  void pickImage() async {
    final ImagePicker _picker = ImagePicker();

    final pickedImage = await _picker.pickImage(source: ImageSource.gallery);
    imageFile = pickedImage != null ? File(pickedImage.path) : null;
    print("imageFile:${imageFile}");
    upLoadToS3();
  }

  void upLoadToS3() async {
    print("upLoadToS3");
    String base64Image = base64Encode(imageFile!.readAsBytesSync());
    // print("base64Image:${base64Image}");
    // print("ตรงนี้${appConfig.countryConfigCollection}");
    var name = "";
    var folder = "";
    // print("appConfig.countryConfigCollection:${appConfig.countryConfigCollection}");
    // name = "MappAAM";
    // folder = "MappAAM/ProfileImages";
    if (appConfig.countryConfigCollection.toString() == 'aam') {
      name = "MappAAM";
      folder = "MappAAM/ProfileImages";
    } else if (appConfig.countryConfigCollection.toString() == 'rafco') {
      name = "MappRafco";
      folder = "MappRafco/ProfileImages";
    } else {
      name = "MappRPLC";
      folder = "MappRPLC/ProfileImages";
    }
    print(base64Image);
     print("ปริ้นตรงนี้");
    if (base64Image != "" || base64Image != null) {
      Map map = {"name": name, "folder": folder, "image": base64Image};
      // print("MAP:${map}");
      final response = await HttpService.post(Endpoints.uploadS3_Center, map,HttpService.noKeyRequestHeaders);
      print(response);

      // var jsonResponse = json.decode(response);

      if (response["statusCode"].toString() == "200") {
        Get.find<ProfileController>().changeImg(response["result"]["url"]["Location"]);
        Get.back();
      } else {
        // Fluttertoast.showToast(
        //     msg: "upload fail!",
        //     toastLength: Toast.LENGTH_SHORT,
        //     gravity: ToastGravity.TOP,
        //     timeInSecForIosWeb: 1,
        //     backgroundColor: Colors.redAccent.withOpacity(0.5),
        //     textColor: Colors.black,
        //     fontSize: 16.0);
      }
    } else {
      //   Fluttertoast.showToast(
      //       msg: "cancle upload",
      //       toastLength: Toast.LENGTH_SHORT,
      //       gravity: ToastGravity.TOP,
      //       timeInSecForIosWeb: 1,
      //       backgroundColor: Colors.redAccent.withOpacity(0.5),
      //       textColor: Colors.black,
      //       fontSize: 16.0);
      // }
    }
  }
}
