import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../app_config.dart';

class AppConfigService extends GetxService {
  BuildContext context;
  AppConfigService({
    required this.context,
  });
  late String countryConfigCollection;
  late Environment environment;
  late String appTitle;
  late String configCloudflareAPI;

  @override
  void onInit() {
    super.onInit();
    print('AppConfigService initializing...');
    countryConfigCollection = AppConfig.of(context).countryConfigCollection;
    environment = AppConfig.of(context).environment;
    appTitle = AppConfig.of(context).appTitle;
    configCloudflareAPI = AppConfig.of(context).pahtConfigCloudflareAPI;

    print('AppConfigService initialized');
    print('appTitle: $appTitle');
  }
}
