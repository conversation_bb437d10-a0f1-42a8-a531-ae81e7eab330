import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/screen/pincode/pincode.dart';
import 'package:uuid/uuid.dart';

import '../../models/otp_model.dart';
import '../../service/http_service.dart';
import '../../view/componance/AppLoading.dart';
import '../../view/componance/themes/theme.dart';
import '../../service/endpoint.dart';
import '../../view/screen/register/register_verify.dart';
import 'register.controller.dart';

class RegisterVerifyController extends GetxController {
  RxBool? isVerify = false.obs;
  RxBool? isAlert = false.obs;
  RxInt counter_OTP = 60.obs;
  late Timer timer;

  Rx<TextEditingController> otpController = TextEditingController().obs;
  final RegisterController registerController = Get.put(RegisterController());
  RxString? phone_register = "".obs;
  RxString? refCode = "".obs;
  RxString phone_code = "".obs;

  void setPhoneCode(String phoneCode) {
    print("RegisterVerifyController phoneCode : $phoneCode");
    phone_code.value = phoneCode;
    update();
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  void completeVerify() {
    if (isVerify!.value == true) {
      Get.toNamed('/home');
    } else {
      isAlert!.value = true;
      update();
    }
  }

  // TODO send otp register
  Future<dynamic> checkOTPConfig(context, String phone) async {
    try {
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        sendRegisterOTP(context, phone, '');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        checkFormatOTPRPLC(context, phone, '');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        checkFormatOTPRafco(context, phone, '');
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  // TODO resend otp Register
  Future<dynamic> checkResendOTPConfig(context) async {
    try {
      if (counter_OTP.value == 0) {
        resetData();

        final RegisterController registerController =
        Get.find<RegisterController>();

        var phone = registerController.phone_regis.value.text;

        if (appConfigService.countryConfigCollection.toString() == "aam") {
          sendRegisterOTP(context, phone, 'resend');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rplc") {
          checkFormatOTPRPLC(context, phone, 'resend');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rafco") {
          checkFormatOTPRafco(context, phone, 'resend');
        }
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  Future<dynamic> sendRegisterOTP(
      context, String phone, String typeResend) async {
    try {
      if (phone.length == 10) {
        // if (!kIsWeb) {
        //   Loader.show2(context);
        // }
        phone_register!.value = phone;
        print("อยู่ตรงนี้เปล่า");
        print(phone_register!.value);
        update();
        AppLoading.loadingVerify(context);

        phone = "+66${phone.substring(1)}";
        print("เบอร์โทรมาไหม");
        print(phone);

        SendCode valueSendCode =
        SendCode.fromJson({"phone": phone, "from": "AAM", "typeSMS": ""});
        final resSendCode = await HttpService.callAPICloudflare(
            "POST", "requertOTP_CF_V2", valueSendCode.toJson());

        AppLoading.Loaderhide(context);
        var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
        var status = resSendCode["result"]["statusCode"];

        if (status.toString() == "202") {
          Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
        } else if (status.toString() == "200") {
          /// verify otp =>
          refCode!.value = resSendCode["result"]["refCode"];
          update();
          if (typeResend != "resend") {
            Get.to(const RegisterVerify());
          }
        } else {
          Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
        }
        return status;
      } else {
        Get.snackbar("แจ้งเตือน", "กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง");
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    } finally {}
  }

  Future<dynamic> checkFormatOTPRPLC(
      context, String phone, String typeResend) async {
    try {
      if (phone.isNotEmpty) {
        print("phone_code : ${phone_code.value}");
        if (phone_code.value == '+856' || phone_code.value == '+855') {
          print("#########");
          // print(phone.substring(0, 1));
          // if (phone.substring(0, 1) == '2' || phone.substring(0, 1) == '3') {
              var phoneParam = "";
              var checkPhone = phone.substring(0, 1);

              if (checkPhone == "0") {
                phoneParam =
                    phone_code!.value + phone.substring(1, phone.length);
              } else {
                phoneParam = '${phone_code!.value}$phone';
              }

              // print('ส่ง otp เบอร์ : ${phone}');
              // print('ส่ง otp เบอร์ : ${phoneParam}');
              // print('ส่ง otp เบอร์ : ${phone_code!.value}');
              sendOTPRPLC(context, phoneParam, typeResend); //TODO ส่ง OTP RPLC
          // }
        } else {
          debugPrint('ส่ง otp ผ่าน bot TG : ${phone_code!.value}');
          print('ส่ง otp เบอร์ : ${phone}');
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RPLC", typeResend);
        }
      } else {
        // print('here else');
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkFormatOTPRafco(
      context, String phone, String typeResend) async {
    try {
      if (phone.isNotEmpty) {
        if (phone_code!.value == '+855' || phone_code!.value == '+856') {
          var phoneParam = "";
          var checkPhone = phone.substring(0, 1);

          if (checkPhone == "0") {
            phoneParam = phone_code!.value + phone.substring(1, phone.length);
          } else {
            phoneParam = '${phone_code!.value}$phone';
          }
          sendOTPRafco(context, phoneParam, typeResend); //TODO ส่ง OTP RAFCO
        } else {
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RAFCO", typeResend); // เส้น bot TG
        }
      } else {}
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRPLC(context, String phone, String typeResend) async {
    try {
      // ex. body
      /// 02029880022 เบอร์สาขา RPLC
      Map data = {
        "from": "RPLC",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);

      final response = await HttpService.callAPIsmsService(
          "POST", Endpoints.requestLocal_OTP, data);

      print("response requestLocal_OTP : $response");
      int status = response["statusCode"];
      var result = response["result"];

      if (status == 200 && result == true) {
        final phoneCode = phone.substring(1, phone.length);
        var uniqueID = Uuid().v4();
        // ex. body
        /// 02029880022 เบอร์พี่นกน้อย
        var bodyData = {
          "transaction_id": uniqueID.toString(),
          "header": "RPLC",
          "phoneNumber": phoneCode,
          "message":
          "RPLC OTP ${response["refCode"]} (Ref: ${response["otp"].toString()}) ລະຫັດຈະໝົດອາຍຸໃນ 1 ນາທີ"
        };
        print(bodyData);

        final responseSendSMS = await HttpService.callAPIsmsService(
            "POST", Endpoints.sendOTPLaos, bodyData);

        print("response sendOTPLaos : $responseSendSMS");

        AppLoading.Loaderhide(context);

        if (responseSendSMS["resultCode"] == 20000 ||
            responseSendSMS["resultDesc"]
                .toString()
                .toLowerCase()
                .contains("success")) {
          refCode!.value = response["refCode"];
          update();
          if (typeResend != "resend") {
            Get.to(const RegisterVerify());
          }
        } else {
          Get.snackbar("Error", 'Failed to send OTP');
          //TODO sms error
          Map dataSendTG = {
            "message":
            '⚠️  แจ้งเตือน MappRPLC(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n🔐 OTP : ${response["otp"].toString()} \n📲 เบอร์โทรศัพท์ : $phoneCode \n👤 transaction ID : $uniqueID \n⏰ เวลา : ${DateTime.now()}'
          };
          final response2 = await HttpService.post(Endpoints.sendNotiTelegramAI,
              dataSendTG, HttpService.noKeyRequestHeaders);
        }
      } else if (status == 404 && result == false) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(verify_failed.tr);
      } else if (status == 202) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(please_1_min.tr);
      } else if (status == 500) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(server_error.tr);
      } else {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(register_failed.tr);
      }
    } catch (e) {
      AppLoading.Loaderhide(context);
      // print("error sendOTPRPLC : $e");
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRafco(context, String phone, String typeResend) async {
    try {
      Map data = {
        "from": "RAFCO",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);

      final response = await HttpService.callAPIsmsService(
          "POST", Endpoints.requestLocal_OTP, data);

      int status = response["statusCode"];
      var result = response["result"];

      if (status == 200 && result == true) {
        final phoneCode = phone.substring(1, phone.length);
        var uniqueID = Uuid().v4();

        var bodyData = {
          "phone": phoneCode,
          "mess":
          "RAFCO OTP ${response["otp"].toString()} (Ref: ${response["refCode"]}) លេខកូដនឹងផុតកំណត់ក្នុងរយៈពេល 1 នាទី។",
          "sender": "RAFCO"
        };

        final responseSendSMS = await HttpService.callAPIsmsService(
            "POST", Endpoints.sendOTPRafco, bodyData);

        AppLoading.Loaderhide(context);

        if (responseSendSMS["resultCode"] == 20000 ||
            responseSendSMS["resultDesc"]
                .toString()
                .toLowerCase()
                .contains("success")) {
          refCode!.value = response["refCode"];
          update();
          if (typeResend != "resend") {
            Get.to(const RegisterVerify());
          }
        } else {
          Get.snackbar("Error", 'Failed to send OTP');
          //TODO sms error
          Map dataSendTG = {
            "message":
            '⚠️  แจ้งเตือน MappRAFCO(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n📲 เบอร์โทรศัพท์ : $phoneCode \n⏰ เวลา : ${DateTime.now()}'
          };
          // print("dataSendTG : $dataSendTG");
          final response2 = await HttpService.post(
              Endpoints.lambdaLinkSendLineMappRAFCO,
              dataSendTG,
              HttpService.noKeyRequestHeaders);
        }
      } else if (status == 404 && result == false) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(verify_failed.tr);
      } else if (status == 202) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(please_1_min.tr);
      } else if (status == 500) {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(server_error.tr);
      } else {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter(register_failed.tr);
      }
    } catch (e) {
      AppLoading.Loaderhide(context);
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPbot_TH(
      context, String phone, String BU, String typeResend) async {
    try {
      if (phone == "0123456789") {
        // loginWithUsername(context, "demoAcount", "1234");
        return null;
      }

      var phoneParam = "";
      var checkPhone = phone.substring(0, 1);

      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = '${phone_code!.value}$phone';
      }

      final data = {"phone_firebase": phoneParam};

      AppLoading.loadingVerify(context);

      final dataMember = await HttpService.callAPIjwtRPLC(
          "POST", Endpoints.getDataMember, data); //ข้อมูลพนักงาน เบอร์ไทย

      if (dataMember["status"] == 200) {
        var margeInto = phoneParam + dataMember["result"][0]["memberID"];

        var bodyData = {"from": BU, "phone": margeInto, "typeSMS": "Qsms"};

        final response = await HttpService.callAPIsmsService(
            "POST", Endpoints.requestOTPbot, bodyData);
        AppLoading.Loaderhide(context);
        int status = response["statusCode"];
        var result = response["result"];

        if (status == 200 && result == true) {
          refCode!.value = response["refCode"];
          update();
          if (typeResend != "resend") {
            Get.to(const RegisterVerify());
          }
        } else if (status == 404 && result == false) {
          // ToastAlert.redCenter(verify_failed.tr);
        } else if (status == 202) {
          // ToastAlert.redCenter(please_1_min.tr);
        } else if (status == 500) {
          // ToastAlert.redCenter(server_error.tr);
        } else {
          // ToastAlert.redCenter(register_failed.tr);
        }
      } else {
        AppLoading.Loaderhide(context);
        // ToastAlert.redCenter("ไม่ได้เป็นสมาชิกนิ");
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  //TODO function check verify otp
  // TODO check otp verify
  //TODO Step 1
  void checkOtpFormat(context, value) async {
    if (value.length == 6) {
      var chkOtp = await checkVerifyOTPConfig(context, registerController.phone_regis.value.text,
          otpController.value.text, refCode!.value, "");
      // print("ใครทำอะไรตรงนี้");
      // print(phone_register!.value);
      // print(refCode!.value);
      // print(otpController.value.text);
      print(chkOtp);
      if (chkOtp.toString() == "200") {
        setVerify(true);
        // TODO verify otp then if true save data regis to db
        final registerController = Get.find<RegisterController>();
        registerController.registerUserData(context);
      } else {
        isAlert!.value = true;
        setVerify(false);
        update();
      }
    }
    
  }
  //TODO Step 2
  Future<dynamic> checkVerifyOTPConfig(context, String phone, String otpCode, String refCode, String fromBU) async {
    try {
      var response;
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        response =
        await checkOTPVerify(context, phone, otpCode, refCode, 'AAM');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        response =
        await verifyOTP_RPLC(context, phone, otpCode, refCode, 'RPLC');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        response =
        await verifyOTP_Rafco(context, phone, otpCode, refCode, 'RAFCO');
      }
      print("responseVerify : $response");
      return response;

    } catch (e) {
      print('$e => error checkVerifyOTPConfig');
    }
  }

  //TODO Step 3 check otp verify By Country
  //TODO AAM THAI
  Future<dynamic> checkOTPVerify(context, String phone, String otpCode,
      String refCode, String fromBU) async {
    try {
      if (phone.length == 10) {
        var phoneCode = "+66${phone.substring(1)}";

        VerifyCode valueVerify = VerifyCode.fromJson({
          "phone": phoneCode,
          "otpCode": otpCode,
          "refCode": refCode,
          "fromBU": fromBU,
        });

        print("valueVerify : ${valueVerify.toJson().toString()}");

        final resVerify = await HttpService.callAPICloudflare(
            "POST", "verifyOTP_CF", valueVerify.toJson());
        print("resVerify : ${resVerify}");
        var responseVerify = ResponseVerifyCode.fromJson(resVerify["result"]);

        var status = resVerify["result"]['statusCode'];
        update();
        print("status : $status");
        return status;
      }
    } catch (e) {
      print('$e => error checkVerify');
    }
  }

  //TODO RPLC
  Future<dynamic> verifyOTP_RPLC(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);
      print('verifyOTP_RPLC  :   ${response}');

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  //TODO RAFCO
  Future<dynamic> verifyOTP_Rafco(
      context, phone, otpCode, refCode, fromBU) async {
    try {
      var checkPhone = phone.substring(0, 1);

      var phoneParam = "";
      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = phone_code!.value + phone;
      }

      final data = {
        "phone": phoneParam,
        "otpCode": otpCode,
        "refCode": refCode,
        "fromBU": fromBU,
        "firstname": "",
        "lastname": ""
      };

      final response = await switchVerify(data);

      int status = response["statusCode"];
      var msg = response["message"];

      debugPrint("msg : $msg");
      return status;
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> switchVerify(data) async {
    var response;
    if (Endpoints.verifyOTPCF == "next") {
      response = await HttpService.post(
          Endpoints.verifyOTP_RPLC, data, HttpService.noKeyRequestHeaders);
    } else {
      response = await HttpService.callAPIsmsService(
          'POST', Endpoints.verifyOTPCF, data);
    }
    return response;
  }

  //TODO end function check verify otp

  void setDefalttheme() {
    otpController.value.text;
    update();
  }

  // Future<dynamic> resendOtp(context) async {
  //   try {
  //     if (counter_OTP.value == 0) {
  //       resetData();
  //       var phone = "+66${phone_register!.value.substring(1)}";
  //
  //       // print("phone : $phone");
  //       SendCode valueSendCode =
  //       SendCode.fromJson({"phone": phone, "from": "AAM", "typeSMS": ""});
  //       final resSendCode = await HttpService.callAPICloudflare(
  //           "POST", "requertOTP_CF_V2", valueSendCode.toJson());
  //
  //       var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
  //       var status = resSendCode["result"]["statusCode"];
  //
  //       if (status.toString() == "202") {
  //         Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
  //       } else if (status.toString() == "200") {
  //         refCode!.value = resSendCode["result"]["refCode"];
  //         update();
  //       } else {
  //         Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
  //       }
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //       Get.snackbar("error", 'เกิดข้อผิดพลาด');
  //       Get.snackbar("error", e.toString());
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }

  void resetData() {
    otpController.value.text = "";
    refCode!.value = "";
    isVerify!.value = false;
    isAlert!.value = false;
    counter_OTP.value = 60;
    update();
    startOTPTimer();
  }

  void startOTPTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (counter_OTP.value > 0) {
        counter_OTP.value--;
        update();
      } else {
        timer.cancel();
      }
    });
    print('start timer');
    print(counter_OTP.value);
  }
}