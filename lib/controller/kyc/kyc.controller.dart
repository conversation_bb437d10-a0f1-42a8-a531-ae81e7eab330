import 'package:AAMG/models/kyc/bookbank_model.dart';
import 'package:AAMG/models/kyc/sumsub_model.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/service/AppUrl.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../contract/contractlist.controller.dart';
import '../profile/profile.controller.dart';

class KYCController extends GetxController {
  final ProfileController profileCtl = Get.find<ProfileController>();
  Rx<SumsubModel> sumsubData = SumsubModel().obs;
  Rx<BookbankModel> bookbankData = BookbankModel().obs;

  RxString? selectedBank = "".obs;
  RxString? selectedBankIcon = "".obs;
  RxInt? selectedBankIndex = 0.obs;
  RxBool showPopUpFirst = false.obs;
  RxList<String> listBankIcon = [
    AppSvgImage.kasikorn_icon,
    AppSvgImage.krungthai_icon,
    AppSvgImage.krungsri_icon,
    AppSvgImage.scb_icon,
    AppSvgImage.bangkok_icon,
    AppSvgImage.omsin_icon,
    AppSvgImage.ttb_icon,
    AppSvgImage.baac_icon,
  ].obs;

  RxList<String> listBankName = [
    "กสิกรไทย", //
    "กรุงไทย", //
    "กรุงศรีอยุธยา", //
    "ไทยพาณิชย์", //
    "กรุงเทพ", //
    "ออมสิน", //
    "ทหารไทยธนชาต",
    "เพื่อการเกษตรและสหกรณ์"
  ].obs;

  RxBool rejectVerifyBank = false.obs;

  RxString bank_custname_text = "".obs;
  RxString bank_name_text = "".obs;
  RxString bank_number_text = "".obs;
  RxString bank_branch_text = "".obs;
  RxBool isChangeBankData = false.obs;
  RxBool isVerifyBankData = false.obs;
  TextEditingController bank_custname = TextEditingController();
  TextEditingController bank_number = TextEditingController();

  @override
  void onInit() {
    super.onInit();
  }

  Future<dynamic> checkstatusSumSub() async {
    try {
      Map data = {
        "phone": profileCtl.profile.value.phone,
      };
      final response = await HttpService.post(
          Endpoints.getKycSumsub, data, HttpService.noKeyRequestHeaders);

      if (response["statusCode"] == 200) {
        if (response["result"]["Status"] == "completed" &&
            response["result"]["Result"]["clientComment"] == "Duplicate.") {
          sumsubData.value = SumsubModel(
            sumsub_status: "duplicate",
            sumsub_reviewAns: "Duplicate",
            sumsub_message: "Duplicate",
            sumsub_passed: true,
            sumsub_caution: false,
          );
        } else if (response["result"]["Status"] == "pending") {
          sumsubData.value = SumsubModel(
            sumsub_status: "pending",
            sumsub_reviewAns: "pending",
            sumsub_message: "pending",
            sumsub_passed: false,
            sumsub_caution: false,
          );
          showPopUpFirst!.value = true;
        } else if (response["result"]["Status"] == "completed" &&
            response["result"]["Result"]["reviewAnswer"] == "GREEN") {
          sumsubData.value = SumsubModel(
            sumsub_status: "success",
            sumsub_reviewAns: "GREEN",
            sumsub_message: "success",
            sumsub_passed: true,
            sumsub_caution: false,
          );
        } else if (response["result"]["Status"] == "completed" &&
            response["result"]["Result"]["reviewAnswer"] == "RED") {
          if (response["result"]["Result"]["buttonIds"][0] == "badPhoto") {
            sumsubData.value = SumsubModel(
              sumsub_status: "problem",
              sumsub_reviewAns: "badPhoto",
              sumsub_message: "problem",
              sumsub_passed: false,
              sumsub_caution: true,
            );
          } else {
            sumsubData.value = SumsubModel(
              sumsub_status: "problem",
              sumsub_reviewAns: "RED",
              sumsub_message: "problem",
              sumsub_passed: false,
              sumsub_caution: true,
            );
          }
        } else if (response["result"]["Status"] == "init") {
          sumsubData.value = SumsubModel(
            sumsub_status: "",
            sumsub_reviewAns: "init",
            sumsub_message: "init",
            sumsub_passed: false,
            sumsub_caution: false,
          );
          showPopUpFirst!.value = true;
        }
        update();
      } else {
        sumsubData.value = SumsubModel(
          sumsub_status: "",
          sumsub_reviewAns: "",
          sumsub_message: "",
          sumsub_passed: false,
          sumsub_caution: false,
        );
        showPopUpFirst!.value = true;
        update();
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkstatusbookbank_edit() async {
    try {
      Map data = {
        // "phone": "**********",
        "phone": profileCtl.profile.value.phone,
      };
      print("data => $data");

      final response = await HttpService.post(Endpoints.getKycBookbank, data,
          HttpService.noKeyRequestHeaders); //todo cloud function aam

      // print("dsddsdd => ${response["result"]}");
      if (response["statusCode"] == 200) {
        if (response["result"]["verifyStatus"] == "pending") {
          bookbankData.value = BookbankModel(
            bookbank_status: "pending",
            bookbank_reviewAns: "รอตรวจสอบ",
            bookbank_custName: response["result"]["bookName"],
            bookbank_name: response["result"]["bankName"],
            bookbank_number: response["result"]["bookNum"],
            bookbank_branch: response["result"]["bookBranch"],
            bookbank_image: response["result"]["bookImage"],
            bookbank_note: "",
            bookbank_showList: true,
          );
        } else if (response["result"]["verifyStatus"] == "reject") {
          bookbankData.value = BookbankModel(
            bookbank_status: "problem",
            bookbank_reviewAns: "ไม่ผ่าน",
            bookbank_custName: response["result"]["bookName"],
            bookbank_name: response["result"]["bankName"],
            bookbank_number: response["result"]["bookNum"],
            bookbank_branch: response["result"]["bookBranch"],
            bookbank_image: response["result"]["bookImage"],
            bookbank_note: response["result"]["note"],
            bookbank_showList: true,
          );
        } else if (response["result"]["verifyStatus"] == "verify") {
          bookbankData.value = BookbankModel(
            bookbank_status: "success",
            bookbank_reviewAns: "ตรวจสอบแล้ว",
            bookbank_custName: response["result"]["bookName"],
            bookbank_name: response["result"]["bankName"],
            bookbank_number: response["result"]["bookNum"],
            bookbank_branch: response["result"]["bookBranch"],
            bookbank_image: response["result"]["bookImage"],
            bookbank_note: response["result"]["note"],
            bookbank_showList: true,
          );
        } else if (response["result"]["verifyStatus"] == "delete") {
          bookbankData.value = BookbankModel(
            bookbank_status: "delete",
            bookbank_reviewAns: "ลบข้อมูล",
            bookbank_custName: response["result"]["bookName"],
            bookbank_name: response["result"]["bankName"],
            bookbank_number: response["result"]["bookNum"],
            bookbank_branch: response["result"]["bookBranch"],
            bookbank_image: response["result"]["bookImage"],
            bookbank_note: response["result"]["note"],
            bookbank_showList: false,
          );
        }
        update();
      } else {
        bookbankData.value = BookbankModel(
          bookbank_status: "",
          bookbank_reviewAns: "",
          bookbank_custName: "",
          bookbank_name: "",
          bookbank_number: "",
          bookbank_branch: "",
          bookbank_image: "",
          bookbank_note: "",
          bookbank_showList: false,
        );
        update();
      }
      setBookbankIcon();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkstatusbookbank() async {
    try {
      Map data = {
        "idcard": profileCtl.profile.value.idcard.toString(),
        "phone_firebase": profileCtl.profile.value.phoneFirebase.toString(),
      };
      // print("data => $data");

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.getBookBankData, data);

      if (response["status"] == 200) {
        // print("response => ${response}");

        bookbankData.value = BookbankModel(
          bookbank_status: "success",
          bookbank_reviewAns: "ตรวจสอบแล้ว",
          bookbank_custName: response["result"]["bankCustName"],
          bookbank_name: response["result"]["bankName"],
          bookbank_number: response["result"]["bankNumber"],
          bookbank_branch: response["result"]["bankBranch"],
          bookbank_image: response["result"]["bankImg"],
          bookbank_note: response["result"]["bankStatus"],
          bookbank_showList: true,
        );
        update();
      } else {
        bookbankData.value = BookbankModel(
          bookbank_status: "",
          bookbank_reviewAns: "",
          bookbank_custName: "",
          bookbank_name: "",
          bookbank_number: "",
          bookbank_branch: "",
          bookbank_image: "",
          bookbank_note: "",
          bookbank_showList: false,
        );
        update();
      }

      setBookbankIcon();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void setDropDownBank(int index) {
    selectedBank!.value = listBankName[index];
    selectedBankIcon!.value = listBankIcon[index];
    update();
  }

  void setBookbankIcon() {
    try {
      switch (bookbankData.value.bookbank_name) {
        case "ธนาคารกสิกรไทย":
        case "กสิกรไทย":
          selectedBank!.value = listBankName[0];
          selectedBankIcon!.value = listBankIcon[0];
          break;
        case "ธนาคารกรุงไทย":
        case "กรุงไทย":
          selectedBank!.value = listBankName[1];
          selectedBankIcon!.value = listBankIcon[1];
          break;
        case "ธนาคารกรุงศรีอยุธยา":
        case "กรุงศรีอยุธยา":
          selectedBank!.value = listBankName[2];
          selectedBankIcon!.value = listBankIcon[2];
        case "ธนาคารไทยพาณิชย์":
        case "ไทยพาณิชย์":
          selectedBank!.value = listBankName[3];
          selectedBankIcon!.value = listBankIcon[3];
          break;
        case "ธนาคารกรุงเทพ":
        case "กรุงเทพ":
          selectedBank!.value = listBankName[4];
          selectedBankIcon!.value = listBankIcon[4];
          break;

        case "ธนาคารออมสิน":
        case "ออมสิน":
          selectedBank!.value = listBankName[5];
          selectedBankIcon!.value = listBankIcon[5];
          break;
        case "ธนาคารทหารไทยธนชาต":
        case "ทหารไทยธนชาต":
        case "ธนาคารทีเอ็มบี":
        case "ทีเอ็มบี":
          selectedBank!.value = listBankName[6];
          selectedBankIcon!.value = listBankIcon[6];
          break;
        case "ธนาคารเพื่อการเกษตรและสหกรณ์":
        case "เพื่อการเกษตรและสหกรณ์":
        case "ธกส":
          selectedBank!.value = listBankName[7];
          selectedBankIcon!.value = listBankIcon[7];
          break;
        default:
          selectedBank!.value = "";
          selectedBankIcon!.value = "";
          break;
      }
      update();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  String formatBankAccountNumber(String accountNumber) {
    // print("formatBankAccountNumber");
    // print(accountNumber);
    if (accountNumber.isEmpty || accountNumber == "null") {
      return '';
    } else {
      String part1 = accountNumber.substring(0, 3);
      String part2 = accountNumber.substring(3, 4);
      String part3 = accountNumber.substring(4, 8);
      String part4 = accountNumber.substring(8, 9);
      String part5 = accountNumber.substring(9, 10);

      return '$part1-$part2-$part3-$part4-$part5';
    }
  }

  /// SumSub
  // Future<dynamic> chkSumSubData() async {
  //   try {
  //     var data = {
  //       "phone": profileCtl.profile.value.phone,
  //     };
  //     final response = await apiPostRequest2(UrlApi.getKycSumsub, data);
  //
  //     if (response["statusCode"] == 200) {
  //       if (response["result"]["Status"] == "completed" &&
  //           response["result"]["Result"]["reviewAnswer"] == "GREEN") {
  //         Get.to(const StatusKYC(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       } else if (response["result"]["Status"] == "completed" &&
  //           response["result"]["Result"]["clientComment"] == "Duplicate.") {
  //         Get.to(const StatusKYC(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       } else if (response["result"]["Status"] == "pending") {
  //         Get.to(const HomePage(),
  //             transition: Transition.rightToLeft,
  //             duration: const Duration(microseconds: 100));
  //       } else {
  //         createSumSubToken();
  //       }
  //     } else {
  //       createSumSubToken();
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }
  //
  // Future<dynamic> createSumSubToken() async {
  //   try {
  //     Map data = {"phone": profileCtl.profile.value.phone};
  //     final response = await ConnectCloudflare.callAPI("sumsub_token", data);
  //
  //     if (response["status"] == 200) {
  //       await launchSNSMobileSDK(response["result"]["token"])
  //           .then((value) => Get.back());
  //     } else if (response["status"] == 400) {
  //       Get.back();
  //     }
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }
  //
  // Future<dynamic> launchSNSMobileSDK(String token) async {
  //   try {
  //     final String accessToken = token; // generate one on the backend
  //     onTokenExpiration() async {
  //       return Future<String>.delayed(
  //           const Duration(seconds: 2), () => "new_access_token");
  //     }
  //
  //     onStatusChanged(
  //         SNSMobileSDKStatus newStatus, SNSMobileSDKStatus prevStatus) {}
  //     final snsMobileSDK = SNSMobileSDK.init(accessToken, onTokenExpiration)
  //         .withHandlers(onStatusChanged: onStatusChanged)
  //         .withDebug(true)
  //         .withLocale(const Locale("en"))
  //         .build();
  //     final SNSMobileSDKResult result = await snsMobileSDK.launch();
  //     return result;
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print(e);
  //     }
  //     const GetSnackBar(
  //       title: "เกิดข้อผิดพลาด",
  //       message: "เกิดข้อผิดพลาด",
  //       duration: Duration(seconds: 3),
  //     );
  //   }
  // }

  Future<dynamic> checkRejectBank(context) async {
    try {
      // if (statusBookBank!.value == "problem") {
      //   // await alertDialogRejectBookbank(context, reason_reject!.value);
      // }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void setBankName(String value) {
    // selectedBankname!.value = value;
    update();
  }

  Future<dynamic> saveBookBank() async {
    try {
      final ContractListController contractListCtl =
          Get.find<ContractListController>();

      // Map data = {
      //   "phone": profileCtl.profile.value.phone.toString(),
      //   "cust_name": bank_custname_text.value.toString().isNotEmpty
      //       ? bank_custname_text.value.toString()
      //       : '${profileCtl.profile.value.firstname.toString()} ${profileCtl.profile.value.lastname.toString()}',
      //   "cust_code": contractListCtl.custCode!.value.toString() ?? "",
      //   "bank_name": selectedBank!.value.toString().isNotEmpty
      //       ? selectedBank!.value.toString()
      //       : bookbankData.value.bookbank_name.toString(),
      //   "bank_number": bank_number_text.value.toString().isNotEmpty
      //       ? bank_number_text.value.toString()
      //       : bookbankData.value.bookbank_number.toString(),
      //   "bank_file": "", //null
      //   "ctt_code": contractListCtl.contractList.last.ctt_code.toString() ?? "",
      // };
      Map data = {
        "bank_name": selectedBank!.value.toString().isNotEmpty
            ? selectedBank!.value.toString()
            : bookbankData.value.bookbank_name.toString(),
        "bank_number": bank_number_text.value.toString().isNotEmpty
            ? bank_number_text.value.toString()
            : bookbankData.value.bookbank_number.toString(),
        "cust_name": bank_custname_text.value.toString().isNotEmpty
            ? bank_custname_text.value.toString()
            : '${profileCtl.profile.value.firstname.toString()} ${profileCtl.profile.value.lastname.toString()}',
        "phone": profileCtl.profile.value.phone.toString()
      };

      print("data => $data");
      //loading
      isVerifyBankData!.value = true;
      update();

      print("มานี้ไหมนะ");

      final response =
          await HttpService.callAPIjwt("POST", Endpoints.updateBookbank, data);
      // stop loading
      isVerifyBankData!.value = false;
      update();
      print("response => ${response}");

      if (response != false && response["status"] == 200) {
        print("response => ${response}");

        isChangeBankData.value = false;
        update();
        await updateBookBankFirebase(response["result"]);
        //ดึงข้อมูลใหม่ จาก cloud function
        checkstatusbookbank_edit();
      } else {
        rejectVerifyBank!.value = true;
        isChangeBankData.value = false;
        update();
      }
    } catch (e) {
      rejectVerifyBank!.value = true;
      isChangeBankData.value = false;
      update();
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> updateBookBankFirebase(data) async {
    try {} catch (e) {
      if (kDebugMode) {
        print(e);
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  void updateBankCustName(String value) {
    bank_custname_text = value.obs;
    isChangeBankData.value = true;
    update();
  }

  void updateBankNumber(String value) {
    print(value);
    bank_number_text = value.obs;
    isChangeBankData.value = true;
    update();
  }

  void updateBankBranch(String value) {
    bank_branch_text = value.obs;
    isChangeBankData.value = true;
    update();
  }

  void updateBankName(String value) {
    bank_name_text = value.obs;
    isChangeBankData.value = true;
    update();
  }
}
