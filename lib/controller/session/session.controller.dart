import 'package:AAMG/controller/notification/notification.controllet.dart';
import 'package:AAMG/controller/profile/delete.account.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/screen/intro/intro_page.dart';
import 'package:AAMG/view/screen/pincode/pincode.dart';

import '../config/appConfig.controller.dart';
import '../mr/mr.controller.dart';
import '../news_promotions/news.controller.dart';
import '../profile/profile.controller.dart';
import '../register/registerAddress.controller.dart';
import '../social_login/auth_result.dart';
import '../social_login/facebook_authentication.dart';

// class SessionController extends GetxController {
//   getSession(context) async {
//     try {
//       AppLoading.generalLoading(context);
//       await Future.delayed(const Duration(milliseconds: 1000))
//           .then((value) => AppLoading.Loaderhide(context));
//       var chk = await GetStorage().read('session');
//
//       if (chk == true) {
//         //TODO check Delete Account
//         var chkAcc =
//             await Get.put(DeleteAccountController()).checkDeleteAccount(Get.find<ProfileController>().profile.value.phoneFirebase.toString());
//         //TODO ถ้ามีสถานะ Delete Account
//         if(chkAcc == true){
//             Get.to(() => PinCodePage());
//         }else if (chkAcc["status"].toString() == 'false') {
//           Get.offAll(() => const IntroPage(), transition: Transition.noTransition);
//         }else{
//           debugPrint("Something went wrong try again");
//         }
//       } else {
//         Get.to(() => OnBroardingPage());
//       }
//     } catch (e) {
//       print(e);
//     }
//   }
//
//   Future<void> Logout(context) async {
//     try {
//       GetStorage storage = GetStorage();
//       await storage.erase();
//       Get.delete<ProfileController>();
//       Get.deleteAll();
//       Get.offAll(() => const IntroPage(), transition: Transition.noTransition);
//     } catch (e) {
//       print(e);
//     }
//   }
// }


class SessionController extends GetxController {
  final storage = GetStorage();

  @override
  void onInit() {
    super.onInit();
  }

  Future<void> getSession(BuildContext context) async {
    AppLoading.generalLoading(context);

    try {
      // await Future.delayed(const Duration(milliseconds: 1000));
      bool? isSessionValid = await storage.read('session');
      Get.put(RegisterAddressController());
      AppLoading.Loaderhide(context);
      if (isSessionValid == true) {
        await _handleAccountStatus();
      } else {
        Get.to(() => OnBroardingPage());
      }
    } catch (e) {
      AppLoading.Loaderhide(context);
      debugPrint("Error in getSession: $e");
    }
  }

  Future<void> _handleAccountStatus() async {
    try {
      final profileController = Get.put(ProfileController());
      final deleteAccountController = Get.find<DeleteAccountController>();
      var phone_firebase = await storage.read('phone_firebase');
      var chkAcc = await deleteAccountController.checkDeleteAccount(
        profileController.profile.value.phoneFirebase.toString() == "null" ? phone_firebase : profileController.profile.value.phoneFirebase.toString(),
      );

      if (chkAcc == true) {
        //TODO initial data config Ana biomatric
        Get.put(AppConfigController());
        Get.to(() => PinCodePage());
      } else if (chkAcc["status"]?.toString() == 'false') {
        logout(Get.context!);
        // Get.offAll(() => const IntroPage(), transition: Transition.noTransition);
      } else {
        logout(Get.context!);
        debugPrint("Something went wrong, please try again.");
      }
    } catch (e) {
      debugPrint("Error in _handleAccountStatus: $e");
    }
  }

  Future<void> logout(BuildContext context) async {
    try {
      Get.find<NotificationController>().clearCountNoti();
      await storage.erase();
      Get.delete<ProfileController>();
      Get.deleteAll();
      Get.offAll(() => const IntroPage(), transition: Transition.noTransition);
    } catch (e) {
      debugPrint("Error in logout: $e");
    }
  }
}

