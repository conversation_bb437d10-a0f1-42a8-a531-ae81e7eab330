import 'dart:async';

import 'package:AAMG/controller/sms/verification.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:sentry/sentry.dart';
import 'package:uuid/uuid.dart';

import '../../models/otp_model.dart';
import '../../service/http_service.dart';
import '../../view/componance/AppLoading.dart';
import '../../view/componance/themes/theme.dart';
import '../../service/endpoint.dart';
import '../../view/componance/widgets/app_pop_up/alert_popup.dart';
import '../profile/delete.account.controller.dart';

class SendRequestSMSController extends GetxController {
  RxString? phone_code = "".obs;
  RxBool isVerify = false.obs;
  RxInt counter_OTP = 60.obs;
  late Timer timer;
  Rx<TextEditingController> phone_login = TextEditingController().obs;
  late Widget verificationPage;

  // TODO send otp
  Future<dynamic> checkOTPConfig(context, String phone) async {
    try {
      print('phone : $phone');
      if (appConfigService.countryConfigCollection.toString() == "aam") {
        sendOTP(context, phone, '');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rplc") {
        checkFormatOTPRPLC(context, phone, '');
      } else if (appConfigService.countryConfigCollection.toString() ==
          "rafco") {
        checkFormatOTPRafco(context, phone, '');
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  // TODO resend otp
  Future<dynamic> checkResendOTPConfig(context, String phone) async {
    try {
      if (counter_OTP.value == 0) {
        resetData();
        setVerify(false);


        if (appConfigService.countryConfigCollection.toString() == "aam") {
          sendOTP(context, phone, 'resend');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rplc") {
          checkFormatOTPRPLC(context, phone, 'resend');
        } else if (appConfigService.countryConfigCollection.toString() ==
            "rafco") {
          checkFormatOTPRafco(context, phone, 'resend');
        }
      }
    } catch (e) {
      print("error checkOTPConfig : $e");
    }
  }

  Future<dynamic> sendOTP(context, String phone, String typeResend) async {
    try {
      if (phone.length == 10) {
        AppLoading.loadingVerify(context);

        phone = "+66${phone.substring(1)}";

        //TODO check Delete Account
        var chkAcc =
            await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
        //TODO ถ้ามีสถานะ Delete Account
        if (chkAcc == true) {
          //TODO call api send otp

          SendCode valueSendCode =
              SendCode.fromJson({"phone": phone, "from": "AAM", "typeSMS": ""});
          final resSendCode = await HttpService.callAPICloudflare(
              "POST", "requertOTP_CF_V2", valueSendCode.toJson());

          AppLoading.Loaderhide(context);
          // var responseSendCode = ResponseSendCode.fromJson(resSendCode["result"]);
          var status = resSendCode["result"]["statusCode"];

          if (status.toString() == "202") {
            Get.snackbar("แจ้งเตือน", "รับรหัส OTP อีกครั้ง หลังจาก 2 นาที");
          } else if (status.toString() == "200") {
            //TODO set refcode
            Get.put(VerificationSMSController())
                .setRefCode(resSendCode["result"]["refCode"]);
            if (typeResend != "resend") {
              //TODO go to route verify otp screen
              startOTPTimer();
              Get.to(verificationPage);
            }
          } else {
            Get.snackbar("แจ้งเตือน", "เกิดข้อผิดพลาดในการส่งรหัส OTP");
          }
          return status;
        } else if (chkAcc["status"].toString() == 'false') {
          AppLoading.Loaderhide(context);
          //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
          AlertPopup.AlertDeleteAccount(context);
        } else {
          AppLoading.Loaderhide(context);
          debugPrint("Something went wrong try again");
        }
      } else {
        Get.snackbar("แจ้งเตือน", "กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง");
      }
      update();
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkFormatOTPRPLC(
      context, String phone, String typeResend) async {
    try {
      print(phone_code!.value);
      print("######");
      if (phone.isNotEmpty) {
        if (phone_code!.value == '+856') {
          // if (phone.substring(0, 1) == '2' || phone.substring(0, 1) == '3') {
            // if (phone.length == 10) {
              var phoneParam = "";
              var checkPhone = phone.substring(0, 1);

              if (checkPhone == "0") {
                phoneParam =
                    phone_code!.value + phone.substring(1, phone.length);
              } else {
                phoneParam = '${phone_code!.value}$phone';
              }
              sendOTPRPLC(context, phoneParam, typeResend); //TODO ส่ง OTP RPLC
            // } else {
            //   // wrongPhone();
            // }
          // }
        } else {
          debugPrint('ส่ง otp ผ่าน bot TG : ${phone_code!.value}');
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RPLC", typeResend); // เส้น bot TG
        }
      } else {
        // print('here else');
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> checkFormatOTPRafco(
      context, String phone, String typeResend) async {
    try {
      if (phone.isNotEmpty) {
        if (phone_code!.value == '+855') {
          var phoneParam = "";
          var checkPhone = phone.substring(0, 1);

          if (checkPhone == "0") {
            phoneParam = phone_code!.value + phone.substring(1, phone.length);
          } else {
            phoneParam = '${phone_code!.value}$phone';
          }
          sendOTPRafco(context, phoneParam, typeResend); //TODO ส่ง OTP RAFCO
        } else {
          //TODO เบอร์ไทย
          sendOTPbot_TH(context, phone, "RAFCO", typeResend); // เส้น bot TG
        }
      } else {}
    } catch (e) {
      if (kDebugMode) {
        print(e);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", e.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRPLC(context, String phone, String typeResend) async {
    try {
      // ex. body
      /// *********** เบอร์สาขา RPLC
      Map data = {
        "from": "RPLC",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);

      //TODO check Delete Account
      var chkAcc =
          await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp

        final response = await HttpService.callAPIsmsService(
            "POST", Endpoints.requestLocal_OTP, data);

        int status = response["statusCode"];
        var result = response["result"];

        if (status == 200 && result == true) {
          final phoneCode = phone.substring(1, phone.length);
          var uniqueID = Uuid().v4();
          // ex. body
          /// *********** เบอร์พี่นกน้อย
          var bodyData = {
            "transaction_id": uniqueID.toString(),
            "header": "RPLC",
            "phoneNumber": phoneCode,
            "message":
                "RPLC OTP ${response["refCode"]} (Ref: ${response["otp"].toString()}) ລະຫັດຈະໝົດອາຍຸໃນ 1 ນາທີ"
          };

          final responseSendSMS = await HttpService.callAPIsmsService(
              "POST", Endpoints.sendOTPLaos, bodyData);

          AppLoading.Loaderhide(context);

          if (responseSendSMS["resultCode"] == 20000 ||
              responseSendSMS["resultDesc"]
                  .toString()
                  .toLowerCase()
                  .contains("success")) {
            //TODO set refcode
            Get.put(VerificationSMSController())
                .setRefCode(response["refCode"]);
            if (typeResend != "resend") {
              //TODO go to route verify otp screen
              startOTPTimer();
              Get.to(verificationPage);
            }
          } else {
            Get.snackbar("Error", 'Failed to send OTP');

            //TODO sms error
            Map dataSendTG = {
              "message":
                  '⚠️  แจ้งเตือน MappRPLC(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n🔐 OTP : ${response["otp"].toString()} \n📲 เบอร์โทรศัพท์ : $phoneCode \n👤 transaction ID : $uniqueID \n⏰ เวลา : ${DateTime.now()}'
            };
            final response2 = await HttpService.post(
                Endpoints.sendNotiTelegramAI,
                dataSendTG,
                HttpService.noKeyRequestHeaders);
          }
        } else if (status == 404 && result == false) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(verify_failed.tr);
        } else if (status == 202) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(please_1_min.tr);
        } else if (status == 500) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(server_error.tr);
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(register_failed.tr);
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (exception, stackTrace) {
      AppLoading.Loaderhide(context);

      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPRafco(context, String phone, String typeResend) async {
    try {
      Map data = {
        "from": "RAFCO",
        "phone": phone,
        "typeSMS": "Qsms",
      };

      AppLoading.loadingVerify(context);
      //TODO check Delete Account
      var chkAcc =
          await Get.put(DeleteAccountController()).checkDeleteAccount(phone);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp
        final response = await HttpService.callAPIsmsService(
            "POST", Endpoints.requestLocal_OTP, data);

        int status = response["statusCode"];
        var result = response["result"];

        if (status == 200 && result == true) {
          final phoneCode = phone.substring(1, phone.length);
          var uniqueID = Uuid().v4();

          var bodyData = {
            "phone": phoneCode,
            "mess":
                "RAFCO OTP ${response["otp"].toString()} (Ref: ${response["refCode"]}) លេខកូដនឹងផុតកំណត់ក្នុងរយៈពេល 1 នាទី។",
            "sender": "RAFCO"
          };

          final responseSendSMS = await HttpService.callAPIsmsService(
              "POST", Endpoints.sendOTPRafco, bodyData);

          AppLoading.Loaderhide(context);

          if (responseSendSMS["resultCode"] == 20000 ||
              responseSendSMS["resultDesc"]
                  .toString()
                  .toLowerCase()
                  .contains("success")) {
            //TODO set refcode
            Get.put(VerificationSMSController())
                .setRefCode(response["refCode"]);
            if (typeResend != "resend") {
              //TODO go to route verify otp screen
              startOTPTimer();
              Get.to(verificationPage);
            }
          } else {
            Get.snackbar("Error", 'Failed to send OTP');

            //TODO sms error
            Map dataSendTG = {
              "message":
                  '⚠️  แจ้งเตือน MappRAFCO(BETA) ขณะนี้ระบบส่ง OTP ขัดข้อง❗\n📲 เบอร์โทรศัพท์ : $phoneCode \n⏰ เวลา : ${DateTime.now()}'
            };
            final response2 = await HttpService.post(
                Endpoints.lambdaLinkSendLineMappRAFCO,
                dataSendTG,
                HttpService.noKeyRequestHeaders);
          }
        } else if (status == 404 && result == false) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(verify_failed.tr);
        } else if (status == 202) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(please_1_min.tr);
        } else if (status == 500) {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(server_error.tr);
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter(register_failed.tr);
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (exception, stackTrace) {
      AppLoading.Loaderhide(context);

      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  Future<dynamic> sendOTPbot_TH(
      context, String phone, String BU, String typeResend) async {
    try {
      if (phone == "**********") {
        // loginWithUsername(context, "demoAcount", "1234");
        return null;
      }

      var phoneParam = "";
      var checkPhone = phone.substring(0, 1);

      if (checkPhone == "0") {
        phoneParam = phone_code!.value + phone.substring(1, phone.length);
      } else {
        phoneParam = '${phone_code!.value}$phone';
      }
      final data = {"phone_firebase": phoneParam};

      AppLoading.loadingVerify(context);
      //TODO check Delete Account
      var chkAcc = await Get.put(DeleteAccountController())
          .checkDeleteAccount(phoneParam);
      //TODO ถ้ามีสถานะ Delete Account
      if (chkAcc == true) {
        //TODO call api send otp

        final dataMember = await HttpService.callAPIjwtRPLC(
            "POST", Endpoints.getDataMember, data); //ข้อมูลพนักงาน เบอร์ไทย

        if (dataMember["status"] == 200) {
          var margeInto = phoneParam + dataMember["result"][0]["memberID"];

          var bodyData = {"from": BU, "phone": margeInto, "typeSMS": "Qsms"};

          final response = await HttpService.callAPIsmsService(
              "POST", Endpoints.requestOTPbot, bodyData);
          AppLoading.Loaderhide(context);
          int status = response["statusCode"];
          var result = response["result"];

          print('status : $response');

          if (status == 200 && result == true) {
            //TODO set refcode
            Get.put(VerificationSMSController())
                .setRefCode(response["refCode"]);
            if (typeResend != "resend") {
              //TODO go to route verify otp screen
              startOTPTimer();
              Get.to(verificationPage);
            }
          } else if (status == 404 && result == false) {
            // ToastAlert.redCenter(verify_failed.tr);
          } else if (status == 202) {
            // ToastAlert.redCenter(please_1_min.tr);
          } else if (status == 500) {
            // ToastAlert.redCenter(server_error.tr);
          } else {
            // ToastAlert.redCenter(register_failed.tr);
          }
        } else {
          AppLoading.Loaderhide(context);
          // ToastAlert.redCenter("ไม่ได้เป็นสมาชิกนิ");
        }
      } else if (chkAcc["status"].toString() == 'false') {
        AppLoading.Loaderhide(context);
        //TODO show alert บัญชีนี้อยู่ระหว่างกระบวนการลบบัญชี
        AlertPopup.AlertDeleteAccount(context);
      } else {
        AppLoading.Loaderhide(context);
        debugPrint("Something went wrong try again");
      }
    } catch (exception, stackTrace) {
      await Sentry.captureException(
        exception,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print(exception);
        Get.snackbar("error", 'เกิดข้อผิดพลาด');
        Get.snackbar("error", exception.toString());
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "ไม่สามารถส่งรหัส OTP ได้",
        duration: Duration(seconds: 3),
      );
    }
  }

  void startOTPTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (counter_OTP.value > 0) {
        counter_OTP.value--;
        update();
      } else {
        timer.cancel();
      }
    });
    print('start timer');
    print(counter_OTP.value);
  }

  void setVerify(value) {
    isVerify!.value = value;
    update();
  }

  void setPhoneCode(String phoneCode) {
    phone_code!.value = phoneCode;
    update();
  }

  void setPhone(String phoneCode, String phone) {
    phone_code!.value = phoneCode;
    phone_login.value.text = phone;
    update();
  }

  void setVerificationPage(Widget page) {
    verificationPage = page;
    update();
  }

  void resetData() {
    isVerify!.value = false;
    counter_OTP.value = 60;
    update();
    startOTPTimer();
  }
}
