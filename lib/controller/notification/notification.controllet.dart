import 'package:AAMG/models/notification_model.dart';
import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:app_badge_plus/app_badge_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';

import '../../service/endpoint.dart';
import '../AppConfigService.dart';
import '../profile/profile.controller.dart';
import '../transalation/translation_key.dart';

class NotificationController extends GetxController {
  // final ProfileController profileController = Get.find<ProfileController>();
  RxBool Loading = true.obs;
  GetStorage storage = GetStorage();
  RxBool? isGuest = false.obs;
  RxBool? isURLNoti = false.obs;
  RxString noti_url = "".obs;
  RxInt countNoti = 0.obs;
  RxInt tricker_typeNoti = 0.obs;
  RxInt count_NotiService = 0.obs;
  RxInt count_NotiPoint = 0.obs;
  RxInt count_NotiNews = 0.obs;

  RxList<Notification>? notificationList = <Notification>[
    // Notification(
    //   readed: 1,
    //   ctt_code: 'AAM00',
    //   title: 'สินเชื่อของฉัน',
    //   subtitle: 'ชำระเรียบร้อย',
    //   detail:
    //       'ขอบคุณที่ชำระยอดสินเชื่อ จำนวน 8,500 บาท เลขที่สัญญา : AAM23459 กรุณตรวจสอบใบเสร็จอีก',
    //   createTime: 'May 1, 2024 - 10:00 AM',
    //   running: '0',
    //   idNotification: '1',
    //   imgNotify: '',
    //   updateTime: '',
    //   typeNotification: '0',
    // ),
    // Notification(
    //   readed: 1,
    //   ctt_code: 'AAM01',
    //   title: 'Notification 15',
    //   subtitle: 'Subtitle',
    //   detail: 'Details',
    //   createTime: 'May 2, 2024 - 10:00 AM',
    //   running: '1',
    //   idNotification: '2',
    //   imgNotify: '',
    //   updateTime: '',
    //   typeNotification: '0',
    // ),
    // Notification(
    //   readed: 1,
    //   ctt_code: 'AAM02',
    //   title: 'ยืนยันตัวตน (KYC)',
    //   subtitle: 'รับ 4,000 พอทย์',
    //   detail:
    //       'ข้อมูลการยืนยันตัวตนของคุณ อยู่ในระหว่างการตรวจสอบ ใช้ระยะเวลาไม่เกิน 1-2 วันทำการ',
    //   createTime: 'May 2, 2024 - 10:00 AM',
    //   running: '2',
    //   idNotification: '3',
    //   imgNotify: '',
    //   updateTime: '',
    //   typeNotification: '1',
    // ),
    // Notification(
    //   readed: 1,
    //   ctt_code: 'AAM03',
    //   title: 'เงินด่วน...สะดวกใช้ ดอกเบี้ยเริ่มต้น 0.75%*',
    //   subtitle: 'Subtitle',
    //   detail: 'บ้าน ที่ดิน คอนโด รถยนต์ จักรยานยนต์ รถจักรกลการเกษตร',
    //   createTime: 'May 3, 2024 - 10:00 AM',
    //   running: '3',
    //   idNotification: '4',
    //   imgNotify: '',
    //   updateTime: '',
    //   typeNotification: '2',
    // ),
    // Notification(
    //   readed: 1,
    //   ctt_code: 'AAM04',
    //   title: 'Notification 4',
    //   subtitle: 'Subtitle',
    //   detail: 'Details',
    //   createTime: 'May 4, 2024 - 10:00 AM',
    //   running: '4',
    //   idNotification: '5',
    //   imgNotify: '',
    //   updateTime: '',
    //   typeNotification: '0',
    // ),
  ].obs;

  RxList<Notification> notifyListService = <Notification>[].obs;
  RxList<Notification> notifyListClaimlike = <Notification>[].obs;
  RxList<Notification> notifyListNews = <Notification>[].obs;
  RxBool isLoading = false.obs;

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    print('NotificationController onInit');
  }

  void updateAllNotifications(List<Notification>? newNotifications) {
    if (newNotifications != null) {
      notificationList!.clear(); // เคลียร์ข้อมูลใน notificationList ก่อน
      notificationList!.addAll(
          newNotifications); // เพิ่มข้อมูลใหม่จาก newNotifications เข้าไปใน notificationList
    }
  }

  Future<RxList<Notification>?> updateReaded(
      int index, String cttCode, String newReadedValue) async {
    if (index >= 0 && index < (notificationList?.length ?? 0)) {
      // Check if the index is within the bounds of the list
      print("##### อ่านแล้วนะ");
      print(notificationList![index].statusnotification);

      // อัปเดตสถานะ noti ตาม idNotification
      var readed = await updateStatusNotification(
          [notificationList![index].idNotification], 'read');

      if (readed == true) {
        notificationList![index].statusnotification = newReadedValue;
        print(newReadedValue);
        update(); // Notify listeners about the change
      }
    }

    return notificationList;
  }

  //TODO ลบทั้งหมดตามหมวดหมู่ noti
  Future<void> removeReadZeroNotifications() async {
    List noti = [];
    if (tricker_typeNoti.value == 0) {
      //TODO รายการอ่านแล้ว หมวด งานบริการ
      noti = notificationList!
          .where((notification) =>
              notification.statusnotification == 'read' &&
              (sortTypeNotification(notification.typeNotification) ==
                      notificationService.tr ||
                  sortTypeNotification(notification.typeNotification) ==
                      notificationMyloan.tr))
          .toList();
    } else if (tricker_typeNoti.value == 1) {
      //TODO รายการอ่านแล้ว หมวด รับคะแนน , แนะนำเพื่อน
      noti = notifyListService
          .where((notification) =>
              notification.statusnotification == 'read' &&
              (sortTypeNotification(notification.typeNotification) ==
                      notificationReferFriend.tr ||
                  sortTypeNotification(notification.typeNotification) ==
                      notificationPoint.tr))
          .toList();
    } else if (tricker_typeNoti.value == 2) {
      //TODO รายการอ่านแล้ว หมวด ข่าวสาร
      noti = notifyListClaimlike
          .where((notification) =>
              notification.statusnotification == 'read' &&
              sortTypeNotification(notification.typeNotification) ==
                  notificationPromotion.tr)
          .toList();
    }

    var list_noti = [];

    //TODO sort noti id to list
    noti.forEach((noti) {
      list_noti.add(noti.idNotification.toString());
    });
    print("list_noti : ${list_noti}");

    if (list_noti.length == 0) {
      return;
    }
    var deleted = await updateStatusNotification(list_noti, 'DELETE');

    if (deleted == true) {
      print("list before");
      print(notificationList!.length);
      //TODO ลบรายการ noti หน้า UI
      if (tricker_typeNoti.value == 0) {
        notificationList?.removeWhere((notification) =>
            notification.statusnotification == 'read' &&
            (sortTypeNotification(notification.typeNotification) ==
                    notificationService.tr ||
                sortTypeNotification(notification.typeNotification) ==
                    notificationMyloan.tr));
      } else if (tricker_typeNoti.value == 1) {
        notificationList?.removeWhere((notification) =>
            notification.statusnotification == 'read' &&
            (sortTypeNotification(notification.typeNotification) ==
                    notificationReferFriend.tr ||
                sortTypeNotification(notification.typeNotification) ==
                    notificationPoint.tr));
      } else if (tricker_typeNoti.value == 2) {
        notificationList?.removeWhere((notification) =>
            notification.statusnotification == 'read' &&
            sortTypeNotification(notification.typeNotification) ==
                notificationPromotion.tr);
      }
      update();
      print("list after");
      print(notificationList!.length);
    }
  }

  Future<void> checkGuestData() async {
    storage.read('isGuest') == null
        ? isGuest!.value = true
        : isGuest!.value = storage.read('isGuest');
    update();
  }

  Future<dynamic> getNotification(context) async {
    try {
      // print("##################### getNotification");
      final ProfileController profileController = Get.find<ProfileController>();
      await checkGuestData();
      if (!isGuest!.value) {
        var phone_firebase = await storage.read("phone_firebase");
        var user_id = await storage.read("user_id");
        // if(profileController.profile.value.phoneFirebase.toString().isEmpty || profileController.profile.value.phoneFirebase.toString() == "null"){
        //   await Get.put(ProfileController()).getProfile().then((value) {
        //     getNotification(context);
        //   });
        // }

        Loading.value = true;
        notificationList!.clear();
        countNoti.value = 0;
        update();

        Map data = {
          "user_running":
              (profileController.profile.value.running.obs?.toString() ??
                  user_id),
          "phone_firebase":
              (profileController.profile.value.phoneFirebase.obs?.toString() ??
                  phone_firebase),
        };

        // print("data getNotification : ${data}");

        final response = await HttpService.callAPIjwt(
            "POST", Endpoints.getNotification, data);

        // print("response getNotification : ${response}");

        if (response['status'] == 200 && response['result'].length > 0) {
          for (var i = 0; i < response['result'].length; i++) {
            // print("#########");
            // print(response['result'][i]['idnotification']);
            // print(response['result'][i]['statusnotification']);
            // print("#########");

            Notification notification = Notification(
              running: response['result'][i]['running'].toString(),
              idNotification:
                  response['result'][i]['idnotification'].toString(),
              title: response['result'][i]['title'],
              detail: response['result'][i]['detail'],
              imgNotify: response['result'][i]['img_notify'] ?? "-",
              updateTime: response['result'][i]['update_time'].toString(),
              createTime: //"",
                  await thaiDateFormat(
                      response['result'][i]['create_time'].toString()),
              noti_time: response['result'][i]['create_time'].toString(),
              typeNotification:
                  response['result'][i]['typenotification'] ?? "-",
              statusnotification:
                  response['result'][i]['statusnotification'] ?? "",
              ctt_code: "-",
              subtitle: response['result'][i]['subtitle'] ??
                      response['result'][i]['typenotification'] == "Nextpayaut"
                  ? "ค่างวดที่ต้องชำระ"
                  : "-",
            );
            //TODO count noti ที่ยังไม่ได้อ่าน ใส่ app icon badge
            if (response['result'][i]['statusnotification'].toString() ==
                "unread") {
              countNoti.value = countNoti.value + 1;
            }
            notificationList!.add(notification);
            update();
          }
          Loading.value = false;
          update();
          debugPrint("noti unread : ${countNoti.value}");
          sortNotifyByType(context);
          if (!kIsWeb) {
            updateCountNoti();
          }
          return true;
        } else {
          Loading.value = false;
          update();
          // AppLoading.Loaderhide(context);
          return false;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("error getNotification =>$e");
      }
      const GetSnackBar(
        title: "เกิดข้อผิดพลาด",
        message: "เกิดข้อผิดพลาด getNotification",
        duration: Duration(seconds: 3),
      );
      Loading.value = false;
      update();
      return false;
    }
    Loading.value = false;
    update();
  }

  void updateCountNoti() async {
    print("###");
    print(await AppBadgePlus.isSupported());
    if (await AppBadgePlus.isSupported() && countNoti.value > 0) {
      AppBadgePlus.updateBadge(countNoti.value);
    } else {
      AppBadgePlus.updateBadge(0);
    }
  }

  void clearCountNoti() async {
    if (await AppBadgePlus.isSupported()) {
      AppBadgePlus.updateBadge(0);
    } else {
      AppBadgePlus.updateBadge(0);
    }
  }

  // String thaiDateFormat(DateTime date) {
  //   const options = {
  //     'day': 'numeric',
  //     'month': 'short',
  //     'year': 'numeric',
  //     'calendar': 'buddhist',
  //   };
  //   return DateFormat('th-TH', options.toString()).format(date);
  // }

  Future<String> thaiDateFormat(String dateString) async {
    try {
      await initializeDateFormatting('th', null);
      // แปลง String เป็น DateTime
      DateTime date = DateTime.parse(dateString);

      // ถ้า  != 'aam' ให้แสดงรูปแบบวันที่สากล
      if (appConfigService.countryConfigCollection != 'aam') {
        return DateFormat('yyyy-MM-dd – HH:mm').format(date); // รูปแบบสากล
      }

      // ฟอร์แมตวันที่และเวลา
      var day = DateFormat.d('th').format(date); // วันที่
      var month = DateFormat.MMM('th').format(date); // เดือนย่อ (ภาษาไทย)
      var year = date.year + 543; // เปลี่ยน ค.ศ. เป็น พ.ศ.
      var time = DateFormat('HH:mm').format(date); // เวลา ชั่วโมง:นาที
      // สร้างรูปแบบที่ต้องการ
      return '$day $month $year - $time';
    } catch (e) {
      print('error thaiDateFormat : ${e}');
      return "";
    }
  }

  Future<void> sortNotifyByType(context) async {
    try {
      // AppConfigService appConfigService =
      //     Get.put(AppConfigService(context: context));
      notifyListService.clear();
      notifyListClaimlike.clear();
      notifyListNews.clear();
      update();
      //TODO แยก List ตามประเภท notify
      for (var i = 0; i < notificationList!.length; i++) {
        //เงื่อนไขแบ่งประเภท notify ของ AAM
        if (appConfigService.countryConfigCollection == "aam") {
          if (notificationList![i].typeNotification == "notify") {
            notifyListNews.add(notificationList![i]);
          } else if (notificationList![i].typeNotification == "" ||
              notificationList![i].typeNotification == "-" ||
              notificationList![i].typeNotification == null ||
              notificationList![i].typeNotification == "Nextpayaut" ||
              notificationList![i].typeNotification == "claimrewar" ||
              notificationList![i].typeNotification == "recommend_" ||
              notificationList![i].typeNotification == "requestIns") {
            notifyListService.add(notificationList![i]);
          } else if (notificationList![i].typeNotification == "claimlike" ||
              notificationList![i].typeNotification == "claimlike_") {
            notifyListClaimlike.add(notificationList![i]);
          }
        } else {
          //เงื่อนไขแบ่งประเภท notify ของ RPLC RAFCO
          if (notificationList![i].typeNotification == "localNoti" ||
              notificationList![i].typeNotification == "" ||
              notificationList![i].typeNotification == null ||
              notificationList![i].typeNotification == "recommend_mr" ||
              notificationList![i].typeNotification == "evaluation" ||
              notificationList![i].typeNotification == "bill") {
            notifyListService.add(notificationList![i]);
          } else if (notificationList![i].typeNotification == "news") {
            notifyListNews.add(notificationList![i]);
          } else {
            notifyListClaimlike.add(notificationList![i]);
          }
        }
        update();
      }

      // debugPrint("notifyListService: ${notifyListService.length}");
      // debugPrint(
      //     "notifyListService Data: ${notifyListService[0].toJson().toString()}");
      // debugPrint("notifyListClaimlike: ${notifyListClaimlike.length}");
      // debugPrint(
      //     "notifyListClaimlike Data: ${notifyListClaimlike.toJson().toString()}");
      // debugPrint("notifyListNews: ${notifyListNews.length}");
      // debugPrint("notifyListNews Data: ${notifyListNews.toJson().toString()}");

      isLoading.value = true;
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<dynamic> updateStatusNotification(
      List noti_running, String status_type) async {
    try {
      Map data = {
        "status": status_type,
        "running": noti_running, //TODO id notification
        "user_running": Get.find<ProfileController>()
            .profile
            .value
            .running
            .toString(), //TODO user id
      };

      // print("data updateStatusNotification : ${data}");
      // print(Endpoints.updateStatusNotification);
      final response = await HttpService.callAPIjwt(
          "POST", Endpoints.updateStatusNotification, data);
      // print(response);
      if (response['status'] == 200) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      print(e);
    }
  }

  String sortTypeNotification(type_noti) {
    try {
      if (type_noti == "Nextpayaut") {
        return notificationMyloan.tr;
      } else if (type_noti == "claimrewar") {
        return notificationPoint.tr;
      } else if (type_noti == "recommend_") {
        return notificationReferFriend.tr;
      } else if (type_noti == "requestIns") {
        return notificationService.tr;
      } else if (type_noti == "claimlike") {
        return notificationPoint.tr;
      } else if (type_noti == "claimlike_") {
        return notificationReferFriend.tr;
      } else if (type_noti == "localNoti") {
        return notificationService.tr;
      } else if (type_noti == "recommend_mr") {
        return notificationReferFriend.tr;
      } else if (type_noti == "evaluation") {
        return notificationService.tr;
      } else if (type_noti == "bill") {
        return notificationMyloan.tr;
      } else if (type_noti == "news") {
        return notificationPromotion.tr;
      } else {
        return notificationService.tr;
      }
    } catch (e) {
      print(e);
      return "";
    }
  }

  void setTrickerTypeNoti(int type) {
    tricker_typeNoti.value = type;
    update();
  }

  void setCountNotiByType(String type, int index) {
    if (type == "service") {
      count_NotiService.value = index;
    } else if (type == "point") {
      count_NotiPoint.value = index;
    } else if (type == "news") {
      count_NotiNews.value = index;
    }
    update();
  }

  List<String> splitDateAndTime(String dateTimeString) {
    // Pattern for "20 ก.ย. 2567 - 08:03" (Thai)
    final RegExp thaiPattern =
        RegExp(r'(\d{1,2}\s\D+\s\d{4})\s-\s(\d{2}:\d{2})');
    // Pattern for "2024-09-20 - 08:03" (ISO)
    final RegExp isoPattern = RegExp(r'(\d{4}-\d{2}-\d{2})\s-\s(\d{2}:\d{2})');

    // Try matching Thai pattern first, then ISO
    final RegExpMatch? match = thaiPattern.firstMatch(dateTimeString) ??
        isoPattern.firstMatch(dateTimeString);

    if (match != null) {
      // Print matched groups
      // print("Match found: ${match.group(0)}");
      final String day = match.group(1)!;
      final String time = match.group(2)!;
      // print("Extracted day: $day, time: $time");
      return [day, time];
    } else {
      // Print error for better debugging
      print("No pattern matched for dateTimeString: $dateTimeString");
      return [];
    }
  }

  Future<void> sortNotifyLinkURL(text) async {
    try {
      isURLNoti!.value = false;
      noti_url.value = "";
      update();
      final urlPattern = r'(https?:\/\/[^\s]+)';
      final regExp = RegExp(urlPattern);
      final matches = regExp.allMatches(text);
      // print("matches : ${matches.first.group(0)}");
      if (matches.first.group(0).toString().length == 0) {
        isURLNoti!.value = false;
        update();
      } else {
        isURLNoti!.value = true;
        noti_url.value = matches.first.group(0).toString();
        update();
      }
    } catch (e) {
      print(e);
    }
  }

  String extractContractNumber(String message) {
    // Define a regular expression pattern to find 'หมายเลขสัญญา' followed by any alphanumeric characters
    final RegExp contractPattern = RegExp(r'หมายเลขสัญญา\s*([A-Za-z0-9]+)');

    // Match the pattern in the input message
    final RegExpMatch? match = contractPattern.firstMatch(message);

    if (match != null) {
      // Extract and return the contract number (group 1)
      return match.group(1)!;
    } else {
      // If no match found, return an empty string or handle the case accordingly
      return '-';
    }
  }

  Future<String> addThreeDays(String dateTimeString) async {
    // Parse the input date string to a DateTime object
    DateTime dateTime = DateTime.parse(dateTimeString);

    // Add 3 days to the parsed date
    DateTime newDate = dateTime.add(Duration(days: 3));

    // Convert the new date back to ISO 8601 string format

    var nextpay = await thaiDateFormat(newDate.toIso8601String());

    List<String> result1 = splitDateAndTime(nextpay.toString());
    // print('result1 => $result1');
    String day = result1.isEmpty ? '' : result1[0] ?? ''; // วันที่
    return day;
  }
}
