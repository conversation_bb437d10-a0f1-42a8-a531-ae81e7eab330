import 'dart:async';
import 'dart:io';

import 'package:AAMG/service/http_service.dart';
import 'package:AAMG/service/endpoint.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../app_config.dart';
import '../../firebase_options.dart';
import '../../service/secure_storage.dart';
import '../AppConfigService.dart';

class NotifyController extends GetxController {
  SecureStorage secureStorage = Get.put(SecureStorage());
  AppConfigService appConfigService = Get.find<AppConfigService>();

  RxString title = "".obs;
  RxString body = "".obs;
  RxInt countChat = 0.obs;
  @override
  void onInit() {
    print('environment :: ${appConfigService.environment}');
    // _configureFirebaseMessaging();
    Future.delayed(Duration.zero, () {
      if(!kIsWeb){
        clearNotificationWhenOpenApp();
        initializeNotifications();
      }
    });
    super.onInit();
  }


  void clearNotificationWhenOpenApp() async{
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();
    flutterLocalNotificationsPlugin.cancelAll();
  }

  void initializeNotifications() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // print(DefaultFirebaseOptions.currentPlatform);

    FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
    firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // ignore: unused_local_variable
    var fcmToken =
        firebaseMessaging.getToken().then((value) => setTokenNotify(value));

    //TODO ส่ง noti subscribe หัวข้อที่ต้องการรับ notification
    setSubscribeToTopic(firebaseMessaging);
    if(Platform.isIOS){
      getAPNToken(firebaseMessaging);
    }
    FirebaseMessaging.onMessage.listen((RemoteMessage event) {
      debugPrint('onMessage listen');
      debugPrint('${event.data}');
      sendNotification(event.notification!.title!, event.notification!.body!);  // แสดง notification
    });

    //TODO เปิดไปยังหน้าเมนูที่กำหนด เมื่อกด notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage event) async {
      if (AppConfig.of(Get.context!).countryConfigCollection.toString() ==
          'aam') {
        if (event.data['activity'].toString() == "Nextpayaut") {
          // Get.to(MyLoanList());
        } else if (event.data['activity'].toString() == "notify") {
          // Get.to(News());
        } else if (event.data['activity'].toString() == "tonewnotify") {
          // Get.to(NewNotify());
        } else if (event.data['activity'].toString() == "ChatInApp") {
          // Get.to(WebViewTelegram());
        }
      }
    });
  }

  Future<void> firebaseMessagingBackgroundHandler(RemoteMessage event) async {
    sendLocalFlutterNotification(
        event.notification!.title!, event.notification!.body!);
  }

  Future<void> setSubscribeToTopic(_fcm) async {
    await _fcm.subscribeToTopic('DAILY_REWARD');
  }

  Future<void> getAPNToken(_fcm) async {
    if (Platform.isIOS) {
      String? apnsToken = await _fcm.getAPNSToken();
      if (apnsToken != null) {
        await _fcm.subscribeToTopic('');
      } else {
        await Future<void>.delayed(
          const Duration(
            seconds: 3,
          ),
        );
        apnsToken = await _fcm.getAPNSToken();
        if (apnsToken != null) {
          await _fcm.subscribeToTopic('');
        }
      }
    } else {
      await _fcm.subscribeToTopic('');
    }
  }


  void sendNotification(String title, String body) async {
    debugPrint('sendNotification');
    debugPrint('title: $title');
    debugPrint('body: $body');
    AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
      if (!isAllowed) {
        // Insert here your friendly dialog box before call the request method
        AwesomeNotifications().requestPermissionToSendNotifications();
      }
    });

    // Initialize the notification channel
    AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher', // Set your app icon here
      [
        NotificationChannel(
          channelGroupKey: 'basic_tests',
          channelKey: 'basic_channel',
          channelName: 'Basic notifications',
          channelDescription: 'Notification channel for basic tests',
          defaultColor: Color(0xFF9D50DD),
          ledColor: Colors.white,
          importance: NotificationImportance.High,
          enableLights: true,
          enableVibration: true,
          playSound: true,
          icon: 'resource://mipmap/ic_launcher', // Use the drawable icon
          // soundSource: 'resource://raw/res_custom_notification', // Set custom sound if needed
        ),
      ],
      channelGroups: [
        NotificationChannelGroup(
          channelGroupKey: 'basic_tests',
          channelGroupName: 'Basic tests group',
        )
      ],
      debug: true, // Set to true if you want to debug
    );

    // Check if notification permissions are allowed
    bool isAllowed = await AwesomeNotifications().isNotificationAllowed();
    if (!isAllowed) {
      // Request permissions if not allowed
      bool permissionsGranted =
          await AwesomeNotifications().requestPermissionToSendNotifications();
      if (!permissionsGranted) return; // Exit if permissions not granted
    }

    // Create the notification
    AwesomeNotifications().createNotification(
      content: NotificationContent(
        id: DateTime.now()
            .millisecondsSinceEpoch
            .remainder(100000), // Unique ID
        channelKey: "basic_channel",
        title: title,
        body: body,
        notificationLayout: NotificationLayout.Default,
        autoDismissible: true,
        icon: 'resource://mipmap/ic_launcher', // Use the drawable icon
        // largeIcon: 'resource://mipmap/ic_launcher', // Use the drawable icon
      ),
    );
  }

  Future<void> sendLocalFlutterNotification(String title, String body) async {
    debugPrint('sendLocalFlutterNotification');
    debugPrint('title: $title');
    debugPrint('body: $body');
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    AndroidNotificationDetails android = const AndroidNotificationDetails(
      'channel id',
      'channel NAME',
      channelDescription: 'CHANNEL DESCRIPTION',
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      icon: '@mipmap/ic_launcher',
      channelShowBadge: false,
    );

    NotificationDetails platform = NotificationDetails(
        android: android, iOS: DarwinNotificationDetails(badgeNumber: 0));

    await flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platform,
    );
  }

  void _configureFirebaseMessaging() {
    FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // setSubscribeToTopic(FirebaseMessaging.instance);
    FirebaseMessaging.instance.getToken().then((token) {
      print('token: $token');
      if (token != null && token.isNotEmpty) {
        setTokenNotify(token);
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((event) {
      if (event != null && event.isNotEmpty) {
        setTokenNotify(event);
      }
    });

    FirebaseMessaging.instance
        .getInitialMessage()
        .then((RemoteMessage? message) {});

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // showFlutterNotification(message);
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      if (message.data['activity'].toString() == "Nextpayaut") {
        // Get.to(MyLoanList());
      } else if (message.data['activity'].toString() == "notify") {
        // Get.to(News());
      } else if (message.data['activity'].toString() == "tonewnotify") {
        // Get.to(NewNotify());
      } else if (message.data['activity'].toString() == "ChatInApp") {
        // Get.to(WebViewTelegram());
      }
    });
  }

  setTokenNotify(tokenNotify) async {
    print('tokenNotify: $tokenNotify');
    GetStorage box = GetStorage();
    await box.write('tokenNotify', tokenNotify.toString());
  }



  Future<void> addNotiChat() async {
    GetStorage box = GetStorage();
    String deviceTK = await box.read('tokenNotify');
    Map data = {"device_token": deviceTK};
    // print(data);

    final resposne = await HttpService.post(Endpoints.getCountUnreadChat, data, HttpService.noKeyRequestHeaders);
    // print('response addNotiChat : ${resposne}');
    if (resposne["statusCode"] == 200) {
      countChat.value = resposne["count"];
    } else {
      countChat.value = 0;
    }

    update();
  }

  Future<void> delNotiChat() async {
    GetStorage box = GetStorage();
    String deviceTK = await box.read('tokenNotify');
    Map data = {"device_token": deviceTK};
    // print(data);

    await await HttpService.post(Endpoints.delCountUnreadChat, data, HttpService.noKeyRequestHeaders);
    countChat.value = 0;
    update();
  }
}


class NotifyType {
  static const success = 'success';
  static const error = 'error';
  static const warning = 'warning';
  static const info = 'info';
}

