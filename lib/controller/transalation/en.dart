import "package:AAMG/controller/transalation/translation_key.dart"
    as translation;

class US {
  Map<String, String> get messages => {
        /// on boarding
        translation.onBoardingTitleEasy: "Easy to use, easy to love.",
        translation.onBoardingTitleConfident: "Confident with us",
        translation.onBoardingDescription1:
            "It's the value we are determined to deliver.",
        translation.onBoardingDescription2:
            "\nWe are committed to making everything easy and worry-free.\nOur dedication is to provide convenience, comfort\nConfidence through our trustworthy services.",
        translation.onBoardingTitleTrust: "Trustworthiness",
        translation.onBoardingTitleReliability: "Reliability",
        translation.onBoardingDescription3: "Principles we adhere to.",
        translation.onBoardingDescription4:
            "\nWe were founded on the principles of transparency,\nstraightforwardness, and accountability,\nin accordance with regulations and under\nthe supervision of the Bank of Thailand.",
        translation.onBoardingTitleFast: "Easy to use,Fast ",
        translation.onBoardingTitleHassle: "Hassle-free.",
        translation.onBoardingDescription5: "That is our goal.",
        translation.onBoardingDescription6:
            "\nWe strive to make the loan application process easy,\nconvenient, and fast, while ensuring it is also secure,\naccurate, and correct.",
        translation.onBoardingTitle4: " Financial",
        translation.onBoardingDescription7:
            "Ready to serve you through smartphones,\nfaster,and less complicated.",
        translation.onBoardingDescription8: "",
        translation.onBoardingVisit: "Guest",
        translation.onBoardingRegis: "I'am new,sign me up",
        translation.onBoardingLogin: "Login",
        translation.onBoardingAcc: "I already have an account ?",
        translation.onBoardingSkip: "Skip",
        translation.onBoardingNext: "Get Started",
        translation.onBoardingClose: "Close",

        /// sign up
        translation.signUpTitle: "AAM Financial",
        translation.signUpPhone: " Account Please enter your phone number",
        translation.signUpRegis: "Register",
        translation.signUpDescription: "By using ",
        translation.signUpPolicy: "Terms and Policy",
        translation.signUpAcc: "I already have an account ?",
        translation.signUpHere: " already",
        translation.signUpHave: " Login",
        translation.signUpHaveDescription: "",
        translation.signUpContinue: "",
        translation.signUpOTP: "Enter OTP",
        translation.signUpSendPhone: "\nWe have sent an OTP to",
        translation.signUpRefCode: "Referral: ",
        translation.signUpRefCodeAgain: "Resend OTP within ",
        translation.signUpCilkRefCodeAgain:
            "The OTP is incorrect. Please try again.",
        translation.signUpResend: "Resend",
        translation.signUpRefCodeError:
            "Please press the button below to receive the code.",
        translation.signUpReferCode: "Have you received",
        translation.signUpRefCodeDescription: "\nany advice from friends?",
        translation.signUpRefCodeDescription2:
            "Add a referral code from a friend who introduced\nyou to us to receive additional rewards.",
        translation.signUpInputRefCode: "Enter referral code (if any).",
        translation.signUpSkipReferCode: "Skip",
        translation.signUpInputRefCodeAgain:
            "The referral code is incorrect. Please try again.",
        translation.signUpName: "What is your name?",
        translation.signUpScanID: "Scan ID Card",
        translation.signUpScanIDDes:
            "Place the front of your ID card \nwithin the frame to scan.",
        translation.signUpInputData: "Enter your name",
        translation.signUpNameDataDescription:
            "\nPlease enter your full name\nfor accuracy in receiving the reward.",
        translation.signUpInputDataName: "First name",
        translation.signUpInputDataSurname: "Last name",
        translation.signUpNext: "Next",
        translation.signUpAddress: "Where do you know us from?",
        translation.signUpAddressDescription: "The branch nearest to you",
        translation.signUpChooseProvince: "Province",
        translation.signUpChooseDistrict: "District",
        translation.signUpCreatePassword: "Create a PIN code",
        translation.signUpCreatePasswordDes: "For safety in use",
        translation.signUpPasswordDescription:
            "Create a PIN for quick and easy access to the application, as \nwell as for added security in case you do not log out.",
        translation.signUpConfirmPassword: "Confirm a PIN code",
        translation.signUpEnterPass: "Enter a PIN",
        translation.signUpConfirmPasswordIncorrect:
            "Pin code is incorrect. Please try again.",
        translation.signUpPhoneAcc:
            "This phone number is already registered for an account.",
        translation.signUpPhoneAccDes:
            "This phone number Account registration information is available.Please press the button below. To log in now",
        translation.signUpConfirmPass: "Enter a PIN code again",
        /// Success Sign Up
        translation.registerSuccess: "Registration completed\nsuccessfully.",
        translation.registerWelcome: "Welcome to AAM",
        translation.registerLetStart: "Start",

        ///home
        translation.homeTitle: "AAM",
        translation.homeSubTitle: " Financial",
        translation.homeTitleDescription:
            " is a credit provider operating under the supervision of the Central Bank of ",
        translation.homeMain: "Home",
        translation.homeWelcome: "Welcome to ",
        translation.homeHello: "Hello,",
        translation.homeCategory: "Category",
        translation.homeImportant: "Important",
        translation.homeLoan: "My loan",
        translation.homeNewItem: "News",

        /// home loan
        translation.homeInstallment: "Bill",
        translation.homePaymentAmount: "Payable",
        translation.homePaymentDate: "Due date",
        translation.homePayment: "Pay bill",
        translation.homeNoContract: "Loan ID",
        translation.homeBeforePaymentDate: "Pay in advance",
        translation.homeLoanLimit: "Available",
        translation.homeLoanBalance: "Balance",
        translation.homeLoanGuarantee: "Products",
        translation.homeLoanMinPay: "Min pay",
        translation.homeMyLoan: "MyLoan",
        translation.homeLoanInterested: "Interested in a loan",
        translation.homeInterested: "What type of loan\nsuits you best?",
        translation.homeApplyLoan: "Apply loan",

        /// home new item
        translation.homePromotion: "News",
        translation.homePromotionAll: "See more",
        translation.homeMenu: "Service",
        translation.homeIncome: "Extra Income",
        translation.homePayInstallments: "Pay bill",
        translation.homeScan: "Scan",
        translation.homeBranch: "Branch",
        translation.homeReady: "Ready",
        translation.homeService: "Delivery Service",
        translation.homeApply: "Apply for a loan",

        ///Home guest
        translation.homeGuest: "",

        /// Back in
        translation.backIn: "",
        translation.backInWelcome: "Welcome to ",
        translation.backInDescription:
            "Enter 6-digit code or scan your face/fingerprint to access",
        translation.backInForgetPin: "Forgot your PIN ",
        translation.backInClick: "Click",
        translation.backInResetPin: "Create a new pin code?",
        translation.backInSendOTP: "We will send the OTP to you\n",
        translation.backInSendPhone: "\nphone number",
        translation.backInCancel: "Cancel",
        translation.backInContinue: "Continue",
        translation.backInNewPassword: "New PIN created \nsuccessfully.",
        translation.backInNewPasswordSuccess: "",
        translation.backInNewPasswordFinish: "Done",

        /// Notification
        translation.notification: "Notification",
        translation.notificationService: "Service",
        translation.notificationPoint: "Points",
        translation.notificationPromotion: "News",
        translation.notificationNoneList: "No notifications",
        translation.notificationNoneListCategory:
            "You’ve got mo messages in your inbox.",
        translation.notificationDelete: "Delete messages",
        translation.notificationDeleteMSG:
            "Do you want ro delete read messages",
        translation.notificationDeleteRead: "Clear read messages",
        translation.notificationDeleteCancel: "Cancel",
        translation.notificationMyLoan: "",
        translation.notificationGeneral: "",
        translation.notificationDetail: "Detail",
        translation.notificationMyloan: "",
        translation.notificationLotto: "",
        translation.notificationReferFriend: "",

        /// Allow pop up contact
        translation.inviteFriend: "Help your friend receive",
        translation.receivePoint: "\n+ 500 Point",
        translation.referFriend: "\nby referring them!",
        translation.openContact: "Open you contacts",
        translation.notNow: "Not now ",
        translation.skipAllow: "Skip",
        translation.allow: "Allow",
        translation.notAllow: "Don't allow",
        translation.allowTitleContact: "",
        translation.allowDescriptionContact: "",
        translation.openNoti: "Turn on notifications",
        translation.receiveNoti: "\nto receive news.",
        translation.receiveNotiDetail:
            "Stay informed about the \nstatus of various services \nand activities ahead of time",
        translation.allowTitleNoti: "",
        translation.allowDesNoti: "",
        translation.point: "Point",

        /// Menu
        /// Menu Get a loan
        translation.menuGetLoan:
            "Easy approval \nStarting interest rate at 0.75%",
        translation.menuGetLoanDes:
            "Cash loan with low interest rates, hassle-free, ready for approval, \nConvenient, free of charge",
        translation.menuGetLoanSkip: "Skip",
        translation.menuGetLoanNext: "Next",
        translation.menuGetLoanRegis: "Understood",
        translation.menuGetLoanNotList: "No loan applications.",
        translation.menuGetLoanNotListDes:
            "AAM Financing is here to serve you.\nInterested in borrowing from us?",
        translation.menuGetLoanCilk: "\nClick the button below.",
        translation.menuGetLoanPlease: "Apply for a loan",
        translation.menuGetLoanCondition: "Loan approval conditions",
        translation.menuGetLoanConditionDes:
            "Loan approval depends on collateral, \ncustomer qualifications, and as determined by the company",
        translation.menuGetLoanUnderstand: "Interested in a loan",
        translation.menuGetLoanSuccess: "",
        translation.menuGetLoanSuccessDesc:
            "Thank you for your interest in AAM FINANCE.\nWe will contact you within 24 hours.\nThank you.",

        ///กรณีมีสัญญา
        translation.menuGetLoanAmount: "Detail Apply Loan",
        translation.menuGetLoanTime: "Installment period",
        translation.menuGetLoanStatus: "Loan status",
        translation.menuGetLoanStatusSend: "Loan request sent.",
        translation.menuGetLoanFollowStatus: "Track loan status",
        translation.menuGetLoanFollowStatusDes: "",
        translation.menuGetLoanInterest: "Interest ",
        translation.menuGetLoanInstallment: "Installment",
        translation.menuGetLoanDate: "Due date",
        translation.menuGetLoanNo: "Loan ID",
        translation.menuGetLoanPay: "PAY BILL",
        translation.menuGetMyLoan: "My loan",
        translation.menuGetLoanPleaseAdd: "Take out more loans",
        translation.menuGetLoanAmountAdd: "Amount",
        translation.menuGetLoanTypeCar: "Car",
        translation.menuGetLoanTypeCarDes: "4 Wheel, VAN, SUV",
        translation.menuGetLoanTypeTruck: "Truck",
        translation.menuGetLoanTypeTruckDes: "6-10 Wheel, Big Truck",
        translation.menuGetLoanTypeMoto: "Motorcycle",
        translation.menuGetLoanTypeMotoDes: "Motorcycle 2 Wheel",
        translation.menuGetLoanTypeLand: "Land",
        translation.menuGetLoanTypeLandDes: "Vacant land, Land and Building",
        translation.menuGetLoanReqTime: "Choose",
        translation.menuGetLoanIncrease: "Take out more loans",
        translation.menuGetLoanDesCalculate:
            "This is a basic calculation.Please review \nthe loan terms and conditions before \nsubmitting your application.",
        translation.menuGetLoanConfirm: "Confirm",
        translation.menuGetLoanStatusCheck: "Document under review.",
        translation.menuGetLoanStatusConsider: "Under consideration.",
        translation.menuGetLoanStatusPassConsider: "Successfully reviewed.",
        translation.menuGetLoanStatusReject: "Thank you for interest",
        translation.menuGetLoanStatusRejectDes:
            "The loan amount you applied for still \nrequires additional collateral. We \nrecommend considering a more suitable \nloan.",
        translation.menuGetLoanStatusApprove: "Loan approved",
        translation.menuGetLoanStatusApproveDes:
            "The approved loan amount will be transferred \nto your bank account within 1-2 business days.",
        translation.menuGetLoanMoth: "month",
        translation.menuGetLoanContact: "Contact us",
        translation.menuGetLoanHistory: "History",
        translation.menuGetLoanReceipt: "Receipt",
        translation.menuGetLoanTypeThree: "Three Wheel",
        translation.menuGetLoanTypeThreeDes: "Motorcycle 3 Wheel",
        translation.LoanRequestDesc: "We have received your loan inquiry.\nPlease wait for our response within 30 minutes.",
        translation.LoanCheckDocDesc: "Appointment confirmed.\nCurrently verifying documents and collateral.\nProcess will take no more than 45 minutes.",
        translation.LoanConsiderationDesc: "Loan evaluation will be completed\nwithin 3 days, Please wait for a moment.",
        translation.LoanApprovedDesc: "The process of your loan application has been successfully\nreviewed. Congratulations! Please await confirmation to sign\nthe agreement documents.",
        translation.LoanPassedDesc: "The approved loan amount will be transferred to your bank\naccount within 1-2 business days.",
        translation.LoanRejectedDesc: "The loan amount you applied for still requires additional collateral.\nWe recommend considering\na more suitable loan.",

        ///QR Code
        translation.menuGetLoanQR: "QR Code",
        translation.menuGetLoanQRDes: "Save the QR code for a bill payment",
        translation.menuGetLoanPayAll: "Amount",
        translation.menuGetLoanBath: "USD",
        translation.menuGetLoanAAM: "aam capital service co.ltd",
        translation.menuGetLoanPleasePay: "Please pay within\n",
        translation.menuGetLoanPayTimeOut: "Time out",
        translation.menuGetLoanSaveQR: "Save",
        translation.menuGetLoanStepPay: "The payment process",
        translation.menuGetLoanStepPay1:
            "\n1.Click the \"Save\"button or capture the screen to save this QR code.",
        translation.menuGetLoanStepPay2:
            "2.Open your bank's mobile app on your device.",
        translation.menuGetLoanStepPay3:
            "3.Pay from the \"Scan to Pay\" menu by scanning the QR code.",
        translation.menuGetLoanStepPay4:
            "4.After completing the payment, please return to check the status.\nPayment in the AAM app again\nif the status is not yet update\nplease contact the team.",

        /// Account Setting
        /// Account Setting Guest View
        translation.accountSettingGuest: "Guest",
        translation.accountSettingProfile: "Manage your profile",
        translation.accountSettingApp: "Application Setting",
        translation.accountSettingLanguage: "language",
        translation.accountSettingPIN: "Change PIN",
        translation.accountSettingBiometric: "Biometric login",
        translation.accountSettingBioDes: "\nFor security and convenience",
        translation.accountSetting: "Account",
        translation.accountSettingKYC: "Verify your identity with us",
        translation.accountSettingKYCDes: "Verification",
        translation.accountSettingBank: "Bank account",
        translation.accountSettingLine: "LINE",
        translation.accountSettingFacebook: "Facebook",
        translation.accountSettingTG: "Telegram",
        translation.accountSettingApple: "Apple",
        translation.accountSettingGoogle: "Google",
        translation.accountSettingDelete: "Delete account",
        translation.accountSettingDeleteDes: "",
        translation.accountSettingHelp: "Support",
        translation.accountSettingHelpTitle: "Support Center",
        translation.accountSettingHelpDes:
            "\nContact AAM-Center Inquiries about usage issues \nor services",
        translation.accountSettingTerm: "Terms and Conditions",
        translation.accountSettingPolicy: "Privacy Policy",
        translation.accountSettingCondition: "Terms of Service",
        translation.accountSettingAnother: "Other",
        translation.accountSettingNotification: "Notification",
        translation.accountSettingLogout: "Log out",

        /// Status
        translation.statusVerify: "Verified",
        translation.statusPending: "Pending",
        translation.statusReject: "Reject",
        translation.statusMR: "",

        /// Level
        translation.levelClassic: "CLASSIC",
        translation.levelGold: "GOLD",
        translation.levelPlatinum: "PLATINUM",

        /// Account Setting  Edit Profile
        translation.accountEditProfile: "Setting",
        translation.accountEdit: "Setting Profile",
        translation.accountEditProfileAll: "Progress",
        translation.accountEditProfileName: "Full name",
        translation.accountEditProfileIDMr: "My referral code",
        translation.accountEditProfileEmail: "E-mail",
        translation.accountEditProfilePhone: "Phone number",
        translation.accountEditProfileIDCard: "ID card",
        translation.accountEditProfileAddress: "Address",
        translation.accountEditUploadImg: "Select profile",
        translation.accountEditUploadDevice: "Select your profile",
        translation.accountEditCancel: "Cancel",
        translation.accountEditMore: "Update information ?",
        translation.accountEditMoreDes:
            "Do you want ro update your personal information ?",
        translation.accountEditContact: "Contact us",
        translation.accountAddEmail: "Add E-mail",
        translation.accountAddIDCard: "Add ID card",
        translation.accountAddAddress: "Add Address",
        translation.accountEmail: "E-mail",
        translation.accountInputEmail: "Enter your e-mail",
        translation.accountSave: "Save",
        translation.accountIDCard: "ID card number",
        translation.accountInputIDCard: "",
        translation.accountErrorIDCard: "",
        translation.accountAddress: "Choose a province",
        translation.accountAddressNo: "House no.",
        translation.accountAddressInputNo: "Enter house no.",
        translation.accountAddressMoo: "Village no.",
        translation.accountAddressInputMoo: "Enter village no.",
        translation.accountAddressSoi: "",
        translation.accountAddressRoad: "",
        translation.accountAddressSubDistrict: "Subdistrict",
        translation.accountAddressDistrict: "District",
        translation.accountAddressProvince: "Province",
        translation.accountAddressAddProvince: "Choose",
        translation.accountAddressZip: "",
        translation.accountEditEmail: "Edit E-mail",
        translation.accountEditAdd: "Edit Address",
        translation.accountUpdateAddress: "",
        translation.accountUpdateAddressDesc:
            "Please update your address to the current one\nbefore proceeding with the loan application.\nThank you.",

        /// Account Setting Log out
        translation.accountLogout: "Log out",
        translation.accountLogoutDes: "Are you sure want to log out",
        translation.accountLogoutNotNow: "Not now",
        translation.accountLogoutYes: "Log out",

        /// Account Setting Delete
        translation.accountDelete: "Delete Account ?",
        translation.accountDeleteTitle: "",
        translation.accountDeleteDes:
            "Are you sure you want to delete this account?\nOnce confirmed. your account will be immediately suspended and will be permanently delete within 30 days. All service data and points will be lost. Please review your infotmation befire proceeding.",
        translation.accountDeleteNotNow: "Not now",
        translation.accountDeleteYes: "Delete",
        translation.accountReason:
            "Please tell us why you are deleting your account.",
        translation.accountReasonDetail1: "I received too many notifications.",
        translation.accountReasonDetail2:
            "I do not want to apply for a loan anymore.",
        translation.accountReasonDetail3: "Other",
        translation.accountReasonInput: "I delete my account because...",
        translation.accountOTP: "Enter OTP",
        translation.accountSendOTP: "We have sent an OTP to",
        translation.accountRefCode: "Referral :",
        translation.accountRefCodeAfter: "Resend OTP within",
        translation.accountRefCodeAgain:
            "The OTP is incorrect. Please try again.",
        translation.accountSuccess: "Account deletion\nconfirmed.",
        translation.accountSuccessDes:
            "AAM Financing hope to serve you\nand assist you again in future.",
        translation.accountOk: "Ok",
        translation.alertDeleteAccount: "Account deleted",
        translation.alertDeleteAccountDesc:
            "If you want to reactivate your account\nPlease contact us.",

        /// Sign in
        translation.signIn: "Login",
        translation.signInPass: "continue with",
        translation.signInOr: "Or",
        translation.signInPhone: "Phone number",
        translation.signInWelcome: "Welcome ",
        translation.signInWelcomeDes: " Please login to use",
        translation.signInDes:
            "By installing/accessing AAM Finance, you agree to this",
        translation.signInPolicy: "Terms and Policy",
        translation.signInDes2: "already",

        /// Policy
        translation.policyHeader:
            "at your service.\nPlease press accept to continue.",
        translation.policyDes:
            "For the safety of your information and to proceed with the service, please press accept to agree to the terms below.",
        translation.policyClickTerms: "Terms of service",
        translation.policyClickPrivacy: "Privacy Policy",
        translation.policyClickAll: "Accept",

        /// Detail Policy
        translation.detailPolicyHeader: "Privacy Policy",
        translation.detailTermsHeader: "Terms of Service",

        ///MR
        translation.mr: "Extra income",
        translation.mrLoan: "Loan service",
        translation.mrApp: "App dowloaded",
        translation.mrJoin: "Join Us as a Referral Agent",
        translation.mrJoinDes:
            "Join Us as an Agent! Enjoy Exclusive Benefits \nEarn Income,No String Arrached.",
        translation.mrSign: "Sign me up",
        translation.mrSignDes1: "1.Become a Friend Referral Agent with Us",
        translation.mrSignDes2:
            "2.Join acticities and earn points to enjoy numerous benefits without any commitment",
        translation.mrSignDes3:
            "3.The more you refer, the more you earn, increasing your income",
        translation.mrRefer: "Who Referred You?",
        translation.mrAddRefer: "Add Referrer",
        translation.mrWait: "Waiting",
        translation.mrSuccess: "Successfully",
        translation.mrTatal: "Total",
        translation.mrTatalReceive: "",
        translation.mrRecommend: "Recommended",
        translation.mrReferApp: "Refer friends to download the app",
        translation.mrReferLoan: "Refer Friends for a Loan",
        translation.mrEarn: "",
        // translation.mrEarn: "Earn up to",
        translation.mrReferFriend: "Refer Friends",
        translation.mrSpecial: "Special Benefits for Referrers",
        translation.mrSpecialDes:
            "Just join the fun with AAM referral activities, and you can earn easy money without any commitments. The more you refer, the more you earn!",
        translation.mrUnderstand: "Understood!",
        translation.mrCamera: "Camera",
        translation.mrUpload: "Upload",
        translation.mrSignUp: "Sign up as a Referral Agent",
        translation.mrScan: "Scan your ID card\nfor automatic data entry.",
        translation.mrOccupation: "Occupation.",
        translation.mrOccupationInput: "Enter your occupation",
        translation.mrJoinRefer:
            "Join as an AAM Finance representative. Please accept the following terms.",
        translation.mrSecurity:
            "For your data security and service provision, please accept the terms below to proceed.",
        translation.mrPlace:
            "Place the front of your ID card within the frame to scan.",
        translation.mrSuccessRegister:
            "Successfully registered\nas a referral agent.",
        translation.mrSuccessRegisterDes:
            "You have successfully registered to become a referral agent \nwith AAM Financing.",
        translation.mrReferCode:
            "Enter the referral code from the person who referred you to receive additional rewards.",
        translation.mrReferCodeInput: "Enter referral code",
        translation.mrPlaceQr: "Place QR Code within the frame to scan.",
        translation.mrPlaceQrDes: "Referral code\nSuccessfully verified",
        translation.mrInforRgister:
            "Your referrer’s information has been registered\nand linked your account successfully",
        translation.mrAccumulate:
            "Accumulate points from referral\nactivities to level up your representative status \nand receive greater benefits.",
        translation.mrWhat: "What will you refer friend to day",
        translation.mrLoanService: "Loan Services",
        translation.mrRenew: "Renew insurance",
        translation.mrCollateral: "Collateral picture (optional) ",
        translation.mrSubmit: "Referral information submitted successfully",
        translation.mrThank:
            "Thank you for your referral. Your friend's loan application will be reviewed within 3 business days. Thank you",
        translation.mrPoint: "Refer a friend, plus a chance to earn more if your friend joins the activity.",
        // translation.mrPoint: "Refer a friend and get 500 AAMP points instantly, plus a chance to earn more if your friend joins the activity.",
        translation.mrHave: "You have referred",
        translation.mrQr: "QR referral agent",
        translation.mrQrSend:
            "Send this referral QR code to your friend to join the referral program",
        translation.mrNoRefer: "No referral points received yet",
        translation.mrReceive: "Points received",
        translation.mrPointReceive: "Earn up to 5,000",
        translation.mrID: "ID Referral",
        translation.mrScanCode: "Scan QR Code",
        translation.mrInputName: "Enter your name",
        translation.mrRegister: "Register",
        translation.mrReward: "No referral points received yet",
        translation.mrSelectProduct: "Select products",
        translation.mrFirstName: 'First name',
        translation.mrInputFirstName: 'Enter your first name',
        translation.mrLastName: 'Last name',
        translation.mrInputLastName: 'Enter your last name',
        translation.mrPhone: 'Phone number',
        translation.mrGuarantee: ' Types of collateral',
        translation.mrInputGuarantee: 'Choose collateral',
        translation.mrShare: 'Share',

        /// update Patch
        translation.updatePatch: "Updating patch",
        translation.updatePatchDes:
            "We have improved the function system.\nFor convenience of use.",
        translation.updatePatchRestart: "Restart",

        /// chat in app
        translation.chatInAppWait: "Please wait...",
        translation.chatInAppContent:
            "System is creating chat, please wait a moment.",
        translation.chatInAppCreateGroup:
            "System is creating chat, please wait a moment.",
        translation.chatInAppNotiBanned:
            "This number is calling otp over 2 times. Please try again after 24 hours",
        translation.chatInAppCallingOTP:
            "The identity verification process is starting.",
        translation.chatInAppTowStep: "Verification 2 Step Telegram",
        translation.chatInAppTowStepDes:
            "Please enter the code 2 step from telegram",
        translation.chatInAppReTryRegister:
            "System is creating account, please wait a moment.",
        translation.chatInAppPleaseWait: "Please wait a moment.",
        translation.chatInAppPleaseWaitContent:
            "The system is creating a chat button.",
        translation.chatInAppError: "An error occurred",
        translation.chatInAppSecond: "second",
        translation.chatInAppEnterOtp: "Enter the OTP",
        translation.chatInAppOtp: "sent to your mobile no. XXXXXX",
        translation.chatInAppEmail: "Enter you email",
        translation.chatInAppEmailDes: "Please enter your email address",
        translation.search: "search",

        /// AICP
        translation.aicp: "Moto for sale",

        /// menu name
        translation.bottomBarHome: 'Home',
        translation.bottomBarMR: 'MR',
        translation.bottomBarNotify: 'Notification',

        translation.iconChatName: 'Talk',
        translation.iconSettingName: 'Setting',
        translation.iconProfileName: 'ME',

        /// branch
        translation.branchMenu: 'Branch',
        translation.searchBranchTitle: 'Find the desired branch',
        translation.searchBranchFailed: 'Branch information not found',

        /// bill payment menu
        translation.billPaymentMenu: 'Payment',
        translation.billDueDate: 'Due date',
        translation.transactionDate: 'Date of transaction',
        translation.choosebillAmount: 'Select the amount method',
        translation.paidAmount: 'Amount',
        translation.specifyAmount: 'Enter the amount',
        translation.payAmount: 'Amount',
        translation.payAmountCurrency: 'Total (Bath)',
        translation.amount: 'Total (Bath)',
        translation.payment_channels: 'Payment Methods',
        translation.payment_qr: 'QR Payment',
        translation.payment_banking: 'Mobile Banking',
        translation.save_qrcode: 'Save QR code',
        translation.payment_success: 'Success',
        translation.payment_success_btn: 'Done',
        translation.time_Out:"Time Out",
        translation.token_timeout:"Token transaction time out, Please try again later",
        translation.try_again:"try again",
        translation.homepage:"HomePage",
        translation.onProcessTitle:"On Processing..",
        translation.onProcessDesc:"We will verify your payment information and\nupdate it within 1-2 business days.",
        translation.currency_th: "",
        translation.currency_km: "",
        translation.currency_lo: "",
        translation.saved: "Done",
        translation.confirm_slip: "Confirm slip",
        translation.uploaded_slip: "Slip uploaded",


        /// Pop up Tutorial
        translation.popUpTutorialHello: 'Hello,',
        translation.popUpTutorialHello1: 'I\'m ',
        translation.popUpTutorialHello2: 'Let me help you get started with the basics of using the ',
        translation.popUpTutorialHello3: 'app.',
        translation.popUpTutorialName: 'Sue-sat.',
        translation.popUpTutorialExplore: ' ',
        translation.popUpTutorialExplore1: 'you can explore our main menu from these categories: ',
        translation.popUpTutorialExplore2: '\nMy important, Loans, and What\'s New',
        translation.popUpTutorialImportant: 'In the ',
        translation.popUpTutorialImportant1: ' My Important page,',
        translation.popUpTutorialImportant2: 'you’ll find key information you ',
        translation.popUpTutorialImportant3: 'frequently need,',
        translation.popUpTutorialImportant4: 'such as due dates, outstanding balances, and payment options',
        translation.popUpTutorialOutstanding: 'If ',
        translation.popUpTutorialOutstanding1: ' have any outstanding payments,the system will display the amount due here for you to view right away.',
        translation.popUpTutorialLoans: 'In the Loans page, if you have a loan with us,',
        translation.popUpTutorialLoans1: ', we’ll display all loan details for you to review',
        translation.popUpTutorialCheckLoan: 'If you have a loan with us,',
        translation.popUpTutorialCheckLoan1: ',you can instantly check your total balance and the next due date. You can also swipe left or right to view other loans, if any.',
        translation.popUpTutorialStayUpdate: 'If you’d like to stay updated on new info, exclusive promotions, and fun activities,',
        translation.popUpTutorialStayUpdate1: ', check out the News menu.',
        translation.popUpTutorialAAMService: 'If you\'d like to use any ',
        translation.popUpTutorialAAMService1: ' services, you can access them right from the Services menu',
        translation.popUpTutorialShowPoint: 'This section shows the points you’ve earned by participating in various activities with us, ',
        translation.popUpTutorialShowPoint1: 'Your points will be collected and displayed along with their total value',
        translation.popUpTutorialChangData: 'If you’d like to make any changes or adjust settings within the app,',
        translation.popUpTutorialChangData1: ', you can do so in the Settings menu here.',
        translation.popUpTutorialAssistance: 'If you need assistance,',
        translation.popUpTutorialAssistance1: ', Sue-sat is available in the Help menu above. Feel free to reach out if you have any issues',
        translation.popUpTutorialIncome: 'This page is for the Income Enhancement Activities, where you can find a summary of all the activities you\'ve participated in, gathered in one place',
        translation.popUpTutorialSummarize: 'We’ll summarize everything, whether it’s referring loans or downloading the app, along with the total points earned from these activities',
        translation.popUpTutorialRecommend: 'If you’d like to recommend someone,',
        translation.popUpTutorialRecommend1: ',you can add it in the Referral Activities menu right here.',
        translation.popUpTutorialNotificationPage: 'This is the Notifications page, where you\'ll find important personal updates and can review past entries.',
        translation.popUpTutorialSettingPage: 'This is the Settings page, where you can edit your personal information and configure various app settings.',
        translation.popUpTutorialUpdateProfile: 'If you want to update or edit your personal information, you can do so in the Edit Personal Information menu',
        translation.popUpTutorialWouldYouLike: 'What would you like Sue-sat to assist you with today?',
        translation.popUpTutorialWouldYouLike1: 'I\'m experiencing issues using the app!',
        translation.popUpTutorialWouldYouLike2: 'I want to speak with a staff member.',
        translation.popUpTutorialWouldYouLike3: 'I would like to provide feedback on the app.',
        translation.popUpTutorialWouldYouLike4: 'I’d like to know how to use this feature again.',
        translation.popUpTutorialWhatCan: 'Hello! What can Sue-sat assist you with?',
        translation.popUpTutorialWhatCan1: 'Report other usage issues',
        translation.popUpTutorialWhatCan2: 'The app frequently freezes',
        translation.popUpTutorialWhatCan3: 'My information is displayed incorrectly',
        translation.popUpTutorialWhatCan4: 'Sue-sat is checking that for you. Please hold on for a moment.',

        ///-----for feedback-----
        translation.feedbackMenu : 'Feedback',
        translation.feedbackTextTitle : 'For better usability',
        translation.feedbackText : '......................Just now, usage\nHow do you feel?',
        translation.likeFeedback : 'Excellent',
        translation.unlikeFeedback : 'Terrible',
        translation.takeSuggestionText : 'We will take all your suggestions to make improvements\nto make our application even better',
        translation.thanksForAdviceTitle : 'Thank you for the suggestions',
        translation.tellWhyUnlike : 'Please let us know what made you feel bad\nwhat should be improved for better usability',
        translation.waitTooLong : 'It took too long',
        translation.difficultUnderstand : 'The interface is difficult to understand',
        translation.tooManySteps : 'There are too many steps',
        translation.slowApplication : 'The application is slow',
        translation.others : 'others',
        translation.submit : 'Submit',
        translation.thanksForAdviceText : 'ขอบคุณสำหรับคำแนะนำ\nเราจะนำทุกคำแนะนำของคุณ เพื่อไปปรับปรุง\nให้แอปพลิเคชั่นของเราใช้งานให้ดียิ่งขึ้น',
        translation.done : 'Done',
        translation.feedbackBottomSheetTitle : 'Lorem ipsum dolor sit',
        translation.feedbackBottomSheetText : 'Lorem ipsum dolor sit amet\nconsectetur. Libero enim.',
        translation.feedbackReactionSelection1 : 'Is the app easy to use and understand',
        translation.feedbackReactionSelection2 : 'Did you encounter any problems or obstacles while using the app?',
        translation.feedbackReactionSelection3 : 'Does the app meet your needs?',
        translation.feedbackReactionSelection4 : 'Are you satisfied with the speed and performance of the app?',

        ///----- term & condition -----
        translation.termAndCondition: '',
        translation.termAndConditionDesc: '',
        translation.definition: '',
        translation.definitionDesc: '',
        translation.disclosure: '',
        translation.disclosureDesc1: '',
        translation.disclosureDesc2: '',
        translation.disclosureDesc3: '',
        translation.disclosureDesc4: '',
        translation.disclosureDesc4_1: '',
        translation.disclosureDesc4_2: '',
        translation.disclosureDesc4_3: '',
        translation.disclosureDesc5: '',
        translation.disclosureDesc6: '',
        translation.disclosureDesc7: '',
        translation.disclosureDesc8: '',
        translation.disclosureDesc8_1: '',
        translation.disclosureDesc8_2: '',
        translation.disclosureDesc8_3: '',
        translation.Intellectual_assets: '',
        translation.Intellectual_assetsDesc: '',
        translation.service_and_usage_restrictions: '',
        translation.service_and_usage_restrictionsDesc1: '',
        translation.service_and_usage_restrictionsDesc2: '',
        translation.service_and_usage_restrictionsDesc3: '',
        translation.amendments: '',
        translation.amendmentsDesc: '',
        translation.suggestions: '',
        translation.suggestionsDesc1: '',
        translation.contact: '',
        translation.contactDesc1: '',
        translation.contactDesc2: '',

        /// AAM
        translation.termAndCondition_aam: '',
        translation.termAndConditionDesc_aam: '',
        translation.definition_aam: '',
        translation.definitionDesc_aam: '',
        translation.disclosure_aam: '',
        translation.disclosureDesc1_aam: '',
        translation.disclosureDesc2_aam: '',
        translation.disclosureDesc3_aam: '',
        translation.disclosureDesc4_aam: '',
        translation.disclosureDesc4_1_aam: '',
        translation.disclosureDesc4_2_aam: '',
        translation.disclosureDesc4_3_aam: '',
        translation.Intellectual_assets_aam: '',
        translation.Intellectual_assetsDesc_aam: '',
        translation.benefits_aam: '',
        translation.benefits_aamDesc: '',
        translation.cookies_aam: '',
        translation.cookies_aamDesc: '',
        translation.service_and_usage_restrictions_aam: '',
        translation.service_and_usage_restrictionsDesc1_aam: '',
        translation.service_and_usage_restrictionsDesc2_aam: '',
        translation.service_and_usage_restrictionsDesc3_aam: '',
        translation.accumulated_points_aam: '',
        translation.accumulated_pointsDesc_aam: '',
        translation.amendments_aam: '',
        translation.amendmentsDesc1_aam: '',
        translation.amendmentsDesc2_aam: '',
        translation.suggestions_aam: '',
        translation.suggestionsDesc1_aam: '',
        translation.suggestionsDesc2_aam: '',
        translation.contact_aam: '',
        translation.contactDesc_aam: '',
        translation.privacy_policy_notice_aam: '',
        translation.privacy_policy_noticeDesc_aam: '',

        /// error message
        translation.error_msg: 'An error occurred.',

  };
}
