import "package:AAMG/controller/transalation/translation_key.dart" as translation;

class enKm {
  Map<String, String> get messages => {
    /// on boarding
    translation.onBoardingTitleEasy: "Easy to use, easy to love.",
    translation.onBoardingTitleConfident: "Confident with us",
    translation.onBoardingDescription1: "It's the value we are determined to deliver.",
    translation.onBoardingDescription2: "\nWe are committed to making everything easy and worry-free.\nOur dedication is to provide convenience, comfort\nConfidence through our trustworthy services.",
    translation.onBoardingTitleTrust: "Trustworthiness",
    translation.onBoardingTitleReliability: "Reliability",
    translation.onBoardingDescription3: "Principles we adhere to.",
    translation.onBoardingDescription4: "\nWe were founded on the principles of transparency,\nstraightforwardness, and accountability,\nin accordance with regulations and under\nthe supervision of the Bank of Cambodia.",
    translation.onBoardingTitleFast: "Easy to use,Fast ",
    translation.onBoardingTitleHassle: "Hassle-free.",
    translation.onBoardingDescription5: "That is our goal.",
    translation.onBoardingDescription6: "\nWe strive to make the loan application process easy,\nconvenient, and fast, while ensuring it is also secure,\naccurate, and correct.",
    translation.onBoardingTitle4: "RAFCO Financial",
    translation.onBoardingDescription7: "Ready to serve you through smartphones,faster, and less complicated.",
    translation.onBoardingDescription8: "",
    translation.onBoardingVisit: "Guest",
    translation.onBoardingRegis: "I'am new, sign me up",
    translation.onBoardingLogin: "Login",
    translation.onBoardingAcc: "I already have an account ?",
    translation.onBoardingSkip: "Skip",
    translation.onBoardingNext: "Get Started",
    translation.onBoardingClose: "Close",
    /// sign up
    translation.signUpTitle: "RAFCO Financial",
    translation.signUpPhone: " Account Please enter your phone number",
    translation.signUpRegis: "Register",
    translation.signUpDescription: "By using RAFCO you agree to our",
    translation.signUpPolicy: "\nTerms and Policy",
    translation.signUpAcc: "I already have an account ?",
    translation.signUpHere: " already",
    translation.signUpHave: " Login",
    translation.signUpHaveDescription: "",
    translation.signUpContinue: "",
    translation.signUpOTP: "Enter OTP",
    translation.signUpSendPhone: "\nWe have sent an OTP to",
    translation.signUpRefCode: "Referral: ",
    translation.signUpRefCodeAgain: "Resend OTP within ",
    translation.signUpCilkRefCodeAgain: "The OTP is incorrect. Please try again.",
    translation.signUpResend: "Resend",
    translation.signUpRefCodeError: "Please press the button below to receive the code.",
    translation.signUpReferCode: "Have you received",
    translation.signUpRefCodeDescription: "\nany advice from friends?",
    translation.signUpRefCodeDescription2: "Add a referral code from a friend who introduced\nyou to us to receive additional rewards.",
    translation.signUpInputRefCode: "Enter referral code (if any).",
    translation.signUpSkipReferCode: "Skip",
    translation.signUpInputRefCodeAgain: "The referral code is incorrect. Please try again.",
    translation.signUpName: "What is your name?",
    translation.signUpScanID: "Scan ID Card",
    translation.signUpScanIDDes: "Place the front of your ID card \nwithin the frame to scan.",
    translation.signUpInputData: "Enter your name",
    translation.signUpNameDataDescription: "\nPlease enter your full name\nfor accuracy in receiving the reward.",
    translation.signUpInputDataName: "First name",
    translation.signUpInputDataSurname: "Last name",
    translation.signUpNext: "Next",
    translation.signUpAddress: "Where do you know us from?",
    translation.signUpAddressDescription: "The branch nearest to you",
    translation.signUpChooseProvince: "Province",
    translation.signUpChooseDistrict: "District",
    translation.signUpCreatePassword: "Create a PIN code",
    translation.signUpCreatePasswordDes: "For safety in use",
    translation.signUpPasswordDescription: "Create a PIN for quick and easy access to the application, as \nwell as for added security in case you do not log out.",
    translation.signUpConfirmPassword: "Confirm a PIN code",
    translation.signUpEnterPass: "Enter a PIN",
    translation.signUpConfirmPasswordIncorrect: "Pin code is incorrect. Please try again.",
    translation.signUpPhoneAcc: "This phone number is already registered for an account.",
    translation.signUpPhoneAccDes: "This phone number Account registration information is available.Please press the button below. To log in now",
    /// Success Sign Up
    translation.registerSuccess: "Registration completed\nsuccessfully.",
    translation.registerWelcome: "Welcome to RAFCO",
    translation.registerLetStart: "Start",
    ///home
    translation.homeTitle: "RAFCO",
    translation.homeSubTitle: " Financial",
    translation.homeTitleDescription: " is a credit provider operating under \nthe supervision of the Central Bank of Cambodia",
    translation.homeMain: "Home",
    translation.homeWelcome: "Welcome to RAFCO",
    translation.homeHello: "Hello,",
    translation.homeCategory: "Category",
    translation.homeImportant: "Important",
    translation.homeLoan: "My loan",
    translation.homeNewItem: "News",
    /// home loan
    translation.homeInstallment: "Bill",
    translation.homePaymentAmount: "Payable",
    translation.homePaymentDate: "Due date",
    translation.homePayment: "Pay bill",
    translation.homeNoContract: "Loan ID",
    translation.homeBeforePaymentDate: "Pay in advance",
    translation.homeLoanLimit: "Available",
    translation.homeLoanBalance: "Balance",
    translation.homeLoanGuarantee: "Products",
    translation.homeLoanMinPay: "Min pay",
    translation.homeMyLoan: "MyLoan",
    translation.homeLoanInterested: "Interested in a loan",
    translation.homeInterested: "What type of loan\nsuits you best?",
    translation.homeApplyLoan: "Apply loan",
    /// home new item
    translation.homePromotion: "News",
    translation.homePromotionAll: "See more",
    translation.homeMenu: "Service",
    translation.homeIncome: "Extra Income",
    translation.homePayInstallments: "Pay bill",
    translation.homeScan: "Scan",
    translation.homeBranch: "Branch",
    translation.homeReady: "Ready",
    translation.homeService: "Delivery Service",
    translation.homeApply: "Apply for a loan",
    ///Home guest
    translation.homeGuest: "",
    /// Back in
    translation.backIn: "",
    translation.backInWelcome: "Welcome to RAFCO",
    translation.backInDescription: "Enter 6-digit code or scan your face/fingerprint to access",
    translation.backInForgetPin: "Forgot your PIN ",
    translation.backInClick: "Click",
    translation.backInResetPin: "Create a new pin code?",
    translation.backInSendOTP: "We will send the OTP to you\n",
    translation.backInSendPhone: "\nphone number",
    translation.backInCancel: "Cancel",
    translation.backInContinue: "Continue",
    translation.backInNewPassword: "New PIN created \nsuccessfully.",
    translation.backInNewPasswordSuccess: "",
    translation.backInNewPasswordFinish: "Done",
    /// Notification
    translation.notification: "Notification",
    translation.notificationService: "Service",
    translation.notificationPoint: "Points",
    translation.notificationPromotion: "News",
    translation.notificationNoneList: "No notifications",
    translation.notificationNoneListCategory: "You’ve got mo messages in your inbox.",
    translation.notificationDelete: "Delete messages",
    translation.notificationDeleteMSG: "Do you want ro delete read messages",
    translation.notificationDeleteRead: "Clear read messages",
    translation.notificationDeleteCancel: "Cancel",
    translation.notificationMyLoan: "",
    translation.notificationGeneral: "",
    translation.notificationDetail: "",
    translation.notificationMyloan: "",
    translation.notificationLotto: "",
    translation.notificationReferFriend: "",

    /// Allow pop up contact
    translation.inviteFriend: "Help your friend receive",
    translation.receivePoint: "\n+ 500 Point",
    translation.referFriend: "\nby referring them!",
    translation.openContact: "Open you contacts",
    translation.notNow: "Not now ",
    translation.skipAllow: "Skip",
    translation.allow: "Allow",
    translation.notAllow: "Don't allow",
    translation.allowTitleContact: "",
    translation.allowDescriptionContact: "",
    translation.openNoti: "Turn on notifications",
    translation.receiveNoti: "\nto receive news.",
    translation.receiveNotiDetail: "Stay informed about the \nstatus of various services \nand activities ahead of time",
    translation.allowTitleNoti: "",
    translation.allowDesNoti: "",
    translation.point: "Point",
    /// Menu
    /// Menu Get a loan
    translation.menuGetLoan: "Easy approval \nStarting interest rate at 0.75%",
    translation.menuGetLoanDes: "Cash loan with low interest rates, hassle-free, ready for approval, \nConvenient, free of charge",
    translation.menuGetLoanSkip: "Skip",
    translation.menuGetLoanNext: "Next",
    translation.menuGetLoanRegis: "Understood",
    translation.menuGetLoanNotList: "No loan applications.",
    translation.menuGetLoanNotListDes: "RAFCO Financing is here to serve you.\nInterested in borrowing from us?",
    translation.menuGetLoanCilk: "\nClick the button below.",
    translation.menuGetLoanPlease: "Apply for a loan",
    translation.menuGetLoanCondition: "Loan approval conditions",
    translation.menuGetLoanConditionDes: "Loan approval depends on collateral, \ncustomer qualifications, and as determined by the company",
    translation.menuGetLoanUnderstand: "Interested in a loan",
    translation.menuGetLoanSuccess: "",
    translation.menuGetLoanSuccessDesc: "Thank you for your interest in AAM FINANCE.\nWe will contact you within 24 hours.\nThank you.",
    ///กรณีมีสัญญา
    translation.menuGetLoanAmount: "Detail Apply Loan",
    translation.menuGetLoanTime: "Installment period",
    translation.menuGetLoanStatus: "Loan status",
    translation.menuGetLoanStatusSend: "Loan request sent.",
    translation.menuGetLoanFollowStatus: "Track loan status",
    translation.menuGetLoanFollowStatusDes: "",
    translation.menuGetLoanInterest: "Interest ",
    translation.menuGetLoanInstallment: "Installment",
    translation.menuGetLoanDate: "Due date",
    translation.menuGetLoanNo: "Loan ID",
    translation.menuGetLoanPay: "PAY BILL",
    translation.menuGetMyLoan: "My loan",
    translation.menuGetLoanPleaseAdd: "Take out more loans",
    translation.menuGetLoanAmountAdd: "Amount",
    translation.menuGetLoanTypeCar: "Car",
    translation.menuGetLoanTypeCarDes: "4 Wheel, VAN, SUV",
    translation.menuGetLoanTypeTruck: "Truck",
    translation.menuGetLoanTypeTruckDes: "6-10 Wheel, Big Truck",
    translation.menuGetLoanTypeMoto: "Motorcycle",
    translation.menuGetLoanTypeMotoDes: "Motorcycle 2 Wheel",
    translation.menuGetLoanTypeLand: "Land",
    translation.menuGetLoanTypeLandDes: "Vacant land, Land and Building",
    translation.menuGetLoanReqTime: "Choose",
    translation.menuGetLoanIncrease: "Take out more loans",
    translation.menuGetLoanDesCalculate: "This is a basic calculation.Please review \nthe loan terms and conditions before \nsubmitting your application.",
    translation.menuGetLoanConfirm: "Confirm",
    translation.menuGetLoanStatusCheck: "Document under review.",
    translation.menuGetLoanStatusConsider: "Under consideration.",
    translation.menuGetLoanStatusPassConsider: "Successfully reviewed.",
    translation.menuGetLoanStatusReject: "Thank you for interest",
    translation.menuGetLoanStatusRejectDes: "The loan amount you applied for still \nrequires additional collateral. We \nrecommend considering a more suitable \nloan.",
    translation.menuGetLoanStatusApprove: "Loan approved",
    translation.menuGetLoanStatusApproveDes: "The approved loan amount will be transferred \nto your bank account within 1-2 business days.",
    translation.menuGetLoanMoth: "month",
    translation.menuGetLoanContact: "Contact us",
    translation.menuGetLoanHistory: "History",
    translation.menuGetLoanReceipt: "Receipt",
    translation.LoanRequestDesc: "We have received your loan inquiry.\nPlease wait for our response within 30 minutes.",
    translation.LoanCheckDocDesc: "Appointment confirmed.\nCurrently verifying documents and collateral.\nProcess will take no more than 45 minutes.",
    translation.LoanConsiderationDesc: "Loan evaluation will be completed\nwithin 3 days, Please wait for a moment.",
    translation.LoanApprovedDesc: "The process of your loan application has been successfully\nreviewed. Congratulations! Please await confirmation to sign\nthe agreement documents.",
    translation.LoanPassedDesc: "The approved loan amount will be transferred to your bank\naccount within 1-2 business days.",
    translation.LoanRejectedDesc: "The loan amount you applied for still requires additional collateral.\nWe recommend considering\na more suitable loan.",


    ///QR Code
    translation.menuGetLoanQR: "QR Code",
    translation.menuGetLoanQRDes: "Save the QR code for a bill payment",
    translation.menuGetLoanPayAll: "Amount",
    translation.menuGetLoanBath: "USD",
    translation.menuGetLoanAAM: "Rafco cambidia company",
    translation.menuGetLoanPleasePay: "Please pay within\n",
    translation.menuGetLoanPayTimeOut: "Time out",
    translation.menuGetLoanSaveQR: "Save",
    translation.menuGetLoanStepPay: "The payment process",
    translation.menuGetLoanStepPay1: "\n1.Click the \"Save\"button or capture the screen to save this QR code.",
    translation.menuGetLoanStepPay2: "2.Open your bank's mobile app on your device.",
    translation.menuGetLoanStepPay3: "3.Pay from the \"Scan to Pay\" menu by scanning the QR code.",
    translation.menuGetLoanStepPay4: "4.After completing the payment, please return to check the status.\nPayment in the RAFCO app again\nif the status is not yet update\nplease contact the team.",
    /// Account Setting
    /// Account Setting Guest View
    translation.accountSettingGuest: "Guest",
    translation.accountSettingProfile: "Manage your profile",
    translation.accountSettingApp: "Application Setting",
    translation.accountSettingLanguage: "language",
    translation.accountSettingPIN: "Change PIN",
    translation.accountSettingBiometric: "Biometric login",
    translation.accountSettingBioDes: "\nFor security and convenience",
    translation.accountSetting: "Account",
    translation.accountSettingKYC: "Verify your identity with us",
    translation.accountSettingKYCDes: "Verification",
    translation.accountSettingBank: "Bank account",
    translation.accountSettingLine: "LINE",
    translation.accountSettingFacebook: "Facebook",
    translation.accountSettingTG: "Telegram",
    translation.accountSettingApple: "Apple",
    translation.accountSettingGoogle: "Google",
    translation.accountSettingDelete: "Delete account",
    translation.accountSettingDeleteDes: "",
    translation.accountSettingHelp: "Support",
    translation.accountSettingHelpTitle: "Support Center",
    translation.accountSettingHelpDes: "\nContact RAFCO-Center Inquiries about usage issues \nor services",
    translation.accountSettingTerm: "Terms and Conditions",
    translation.accountSettingPolicy: "Privacy Policy",
    translation.accountSettingCondition: "Terms of Service",
    translation.accountSettingAnother: "Other",
    translation.accountSettingNotification: "Notification",
    translation.accountSettingLogout: "Log out",
    /// Status
    translation.statusVerify: "Verified",
    translation.statusPending: "Pending",
    translation.statusReject: "Reject",
    translation.statusMR: "",
    /// Level
    translation.levelClassic: "CLASSIC",
    translation.levelGold: "GOLD",
    translation.levelPlatinum: "PLATINUM",
    /// Account Setting  Edit Profile
    translation.accountEditProfile: "Setting",
    translation.accountEdit: "Setting Profile",
    translation.accountEditProfileAll: "Progress",
    translation.accountEditProfileName: "Full name",
    translation.accountEditProfileIDMr: "My referral code",
    translation.accountEditProfileEmail: "E-mail",
    translation.accountEditProfilePhone: "Phone number",
    translation.accountEditProfileIDCard: "ID card",
    translation.accountEditProfileAddress: "Address",
    translation.accountEditUploadImg: "Select profile",
    translation.accountEditUploadDevice: "Select your profile",
    translation.accountEditCancel: "Cancel",
    translation.accountEditMore: "",
    translation.accountEditMoreDes: "",
    translation.accountEditContact: "",
    translation.accountAddEmail: "+ Add E-mail",
    translation.accountAddIDCard: "+ Add ID card",
    translation.accountAddAddress: "+ Add Address",
    translation.accountEmail: "E-mail",
    translation.accountInputEmail: "Enter your e-mail",
    translation.accountSave: "Save",
    translation.accountIDCard: "ID card number",
    translation.accountInputIDCard: "",
    translation.accountErrorIDCard: "",
    translation.accountAddress: "",
    translation.accountAddressNo: "House no.",
    translation.accountAddressInputNo: "Enter house no.",
    translation.accountAddressMoo: "Village no.",
    translation.accountAddressInputMoo: "Enter village no.",
    translation.accountAddressSoi: "",
    translation.accountAddressRoad: "",
    translation.accountAddressSubDistrict: "Subdistrict",
    translation.accountAddressDistrict: "District",
    translation.accountAddressProvince: "Province",
    translation.accountAddressAddProvince: "Choose",
    translation.accountAddressZip: "",
    /// Account Setting Log out
    translation.accountLogout: "Log out",
    translation.accountLogoutDes: "Are you sure want to log out",
    translation.accountLogoutNotNow: "Not now",
    translation.accountLogoutYes: "Log out",
    /// Account Setting Delete
    translation.accountDelete: "Delete Account ?",
    translation.accountDeleteTitle: "",
    translation.accountDeleteDes: "Are you sure you want to delete this account?\nOnce confirmed. your account will be immediately suspended and will be permanently delete within 30 days. All service data and points will be lost. Please review your infotmation befire proceeding.",
    translation.accountDeleteNotNow: "Not now",
    translation.accountDeleteYes: "Delete",
    translation.accountReason: "Please tell us why you are deleting your account.",
    translation.accountReasonDetail1: "I received too many notifications.",
    translation.accountReasonDetail2: "I do not want to apply for a loan anymore.",
    translation.accountReasonDetail3: "Other",
    translation.accountReasonInput: "I delete my account because...",
    translation.accountOTP: "Enter OTP",
    translation.accountSendOTP: "We have sent an OTP to",
    translation.accountRefCode: "Referral :",
    translation.accountRefCodeAfter: "Resend OTP within",
    translation.accountRefCodeAgain: "The OTP is incorrect. Please try again.",
    translation.accountSuccess: "Account deletion\nconfirmed.",
    translation.accountSuccessDes: "AAM Financing hope to serve you\nand assist you again in future.",
    translation.accountOk: "Ok",
    translation.alertDeleteAccount: "Account deleted",
    translation.alertDeleteAccountDesc: "If you want to reactivate your account\nPlease contact us.",
    /// Sign in
    translation.signIn: "Login",
    translation.signInPass: "continue with",
    translation.signInOr: "Or",
    translation.signInPhone: "Phone number",
    translation.signInWelcome: "Welcome ",
    translation.signInWelcomeDes: " Please login to use",
    translation.signInDes: "By installing/accessing AAM Finance, you agree to this",
    translation.signInPolicy: "Terms and Policy",
    translation.signInDes2: "already",

    /// menu name
    translation.bottomBarHome: 'Home',
    translation.bottomBarMR: 'MR',
    translation.bottomBarNotify: 'Notification',

    translation.iconChatName: 'Talk',
    translation.iconSettingName: 'Setting',
    translation.iconProfileName: 'ME',

    /// branch
    translation.branchMenu: 'Branch',
    translation.searchBranchTitle: 'Find the desired branch',
    translation.searchBranchFailed: 'Branch information not found',

    /// bill payment menu
    translation.billPaymentMenu: 'Payment',
    translation.billDueDate: 'Due date',
    translation.transactionDate: 'Date of transaction',
    translation.choosebillAmount: 'Select the amount method',
    translation.paidAmount: 'Amount',
    translation.specifyAmount: 'Enter the amount',
    translation.payAmount: 'Amount',
    translation.payAmountCurrency: 'Total (Bath)',
    translation.amount: 'Total (Bath)',
    translation.payment_channels: 'Payment Methods',
    translation.payment_qr: 'QR Payment',
    translation.payment_banking: 'Mobile Banking',
    translation.save_qrcode: 'Save QR code',
    translation.payment_success: 'Success',
    translation.payment_success_btn: 'Done',
    translation.time_Out:"Time Out",
    translation.token_timeout:"Token transaction time out, Please try again later",
    translation.try_again:"try again",
    translation.homepage:"HomePage",
    translation.onProcessTitle:"On Processing..",
    translation.onProcessDesc:"We will verify your payment information and\nupdate it within 1-2 business days.",
    translation.currency_th: "",
    translation.currency_km: "",
    translation.currency_lo: "",
    translation.saved: "Done",
    translation.confirm_slip: "Confirm slip",
    translation.uploaded_slip: "Slip uploaded",

    ///----- term & condition -----
    translation.termAndCondition_rafco: '',
    translation.termAndConditionDesc_rafco: '',
    translation.definition_rafco: '',
    translation.definitionDesc_rafco: '',
    translation.disclosure_rafco: '',
    translation.disclosureDesc1_rafco: '',
    translation.disclosureDesc2_rafco: '',
    translation.disclosureDesc3_rafco: '',
    translation.disclosureDesc4_rafco: '',
    translation.disclosureDesc5_rafco: '',
    translation.disclosureDesc6_rafco: '',
    translation.disclosureDesc7_rafco: '',
    translation.disclosureDesc8_rafco: '',
    translation.disclosureDesc8_1_rafco: '',
    translation.disclosureDesc8_2_rafco: '',
    translation.disclosureDesc8_3_rafco: '',
    translation.Intellectual_assets_rafco: '',
    translation.Intellectual_assetsDesc_rafco: '',
    translation.service_and_usage_restrictions_rafco: '',
    translation.service_and_usage_restrictionsDesc1_rafco: '',
    translation.service_and_usage_restrictionsDesc2_rafco: '',
    translation.service_and_usage_restrictionsDesc3_rafco: '',
    translation.service_and_usage_restrictionsDesc4_rafco: '',
    translation.amendments_rafco: '',
    translation.amendmentsDesc_rafco: '',
    translation.suggestions_rafco: '',
    translation.suggestionsDesc1_rafco: '',
    translation.contact_rafco: '',
    translation.contactDesc1_rafco: '',
    translation.contactDesc2_rafco: '',
    /// error message
    translation.error_msg: 'An error occurred.',
  };
}