import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../config/appConfig.controller.dart';
import '../profile/profile.controller.dart';

class DailyActiveUsersController extends GetxController {

  RxString? isAndroid = RxString('');
  RxString? isIOS = RxString('');
  RxString? isWeb = RxString('');

  Future<dynamic> saveLogMenu(int menuId) async {
    try {
      final ProfileController profileCtl = Get.find<ProfileController>();
      await setOSDevice();
      Map data = {
        "phone": profileCtl.profile.value.phone.obs.string,
        "running": profileCtl.profile.value.running.obs.string,
        "menuId": menuId,
        "platform_android": isAndroid!.value,
        "platform_ios": isIOS!.value,
        "platform_web": isWeb!.value,
      };
      // await ConnectCloudflare.callAPI("logMenu", data);
    } catch (e) {
      print(e);
    }
  }

  Future<void> setOSDevice() async {
    try {
      final AppConfigController appConfigCtl = Get.find<AppConfigController>();

      if (appConfigCtl.appOS.value.deviceOS == "android") {
        isAndroid!.value = "Y";
        isIOS!.value = "N";
        isWeb!.value = "N";
      } else if (appConfigCtl.appOS.value.deviceOS == "ios") {
        isIOS!.value = "Y";
        isAndroid!.value = "N";
        isWeb!.value = "N";
      } else if (kIsWeb) {
        isWeb!.value = "Y";
        isAndroid!.value = "N";
        isIOS!.value = "N";
      }
      update();
    } catch (e) {
      print(e);
    }
  }
}
