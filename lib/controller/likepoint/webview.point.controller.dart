import 'dart:convert';

import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:intl/intl.dart';

import '../../models/likepoint/member_likepoint_model.dart';
import '../../service/AppService.dart';
import '../../service/firebase/firebase_service.dart';
import '../../service/http_service.dart';
import '../../service/secure_storage.dart';
import '../../view/componance/utils/AppSvgImage.dart';
import '../AppConfigService.dart';
import '../profile/profile.controller.dart';

class WebViewPointController extends GetxController {
  RxString? phoneEncode = RxString('');
  RxString? merchantID = RxString('');
  RxBool? isOpen = false.obs;
  RxString? valuePoint = RxString('');
  RxString firstName = "".obs;
  RxString lastName = "".obs;
  RxString urlLikePointAPI = "".obs;
  RxString urlLikePoint = "".obs;
  RxString balanceLikePoint = "0".obs;
  RxString balanceLikePointTH = "0".obs;
  RxString namePoint = "-".obs;
  RxString download_poi = "".obs;
  RxString referfriend_poi = "".obs;
  RxBool noLikeWallet = false.obs;

  RxBool statusMigrate = false.obs;
  RxBool isChecked = false.obs;
  RxBool isUpgrading = false.obs;
  RxBool isSuccess = false.obs;
  RxBool useOnlyLikePoint = false.obs;
  RxBool readyForUse = false.obs;
  SecureStorage secureStorage = SecureStorage();

  RxBool isLoading = false.obs;

  // FOR MIGRATE
  RxString amount = "0".obs;
  RxString address = "".obs;
  RxString displayName = "".obs;
  RxString likewalletUID = "".obs;
  RxString memberID = "".obs;

  // FOR MIGRATE

  var profileCtl;

  RxString phoneFirebase = "".obs;
  RxMap notiPoint = {
    "title": "",
    "dec": "",
  }.obs;

  RxBool isAlreadyMigrated = false.obs;

  RxString point_icon = AppSvgImage.icon_defalut.toString().obs;
  RxString language = "th".obs;

  @override
  void onInit() {
    super.onInit();
    initializeProfileData();
    setIconPointByBU();
    // initializeData();
  }

  void initializeProfileData() async {
    if (Get.isRegistered<ProfileController>()) {
      profileCtl = Get.find<ProfileController>();
    } else {
      profileCtl = Get.put(ProfileController());
    }
  }

  void initializeData() async {
    debugPrint("initializeData");
    // โหลดข้อมูลที่ต้องการ
    await loadData();
    await getPocketBalance();
    await checkMigrate(phoneFirebase.value);
  }

  void setIconPointByBU() {
    getInfoLikePoint();
    appConfigService.countryConfigCollection.toString() == 'aam'
        ? point_icon.value = AppSvgImage.icon_AAMP.toString()
        : appConfigService.countryConfigCollection.toString() == 'rafco'
            ? point_icon.value = AppSvgImage.icon_RAFP.toString()
            : appConfigService.countryConfigCollection.toString() == 'rplc'
                ? point_icon.value = AppSvgImage.icon_RPLP.toString()
                : point_icon.value = AppSvgImage.icon_AAMP.toString();
    update();
    setLanguage();
  }

  void setLanguage() {
    appConfigService.countryConfigCollection.toString() == 'aam'
        ? language.value = ''
        : appConfigService.countryConfigCollection.toString() == 'rafco'
        ? language.value = 'kh'
        : appConfigService.countryConfigCollection.toString() == 'rplc'
        ? language.value = 'la'
        : language.value = '';
    update();
  }

  Future<void> loadData() async {
    try {
      // ตรวจสอบ phoneFirebase จาก profileCtl
      final phoneFromProfile = profileCtl.profile.value.phoneFirebase;
      if (phoneFromProfile != null && phoneFromProfile.isNotEmpty) {
        phoneFirebase.value = phoneFromProfile;
      } else {
        // อ่านข้อมูลจาก secure storage
        String? phone = await secureStorage.readSecureData("firebasePhone");
        if (phone != null && phone.isNotEmpty) {
          phoneFirebase.value = phone;
        } else {
          phoneFirebase.value =
              AppService.phoneToPhoneCode(profileCtl.profile.value.phone);
        }
      }
      update();
    } catch (e, stackTrace) {
      // จัดการข้อยกเว้นและแสดงข้อความแสดงข้อผิดพลาด
      debugPrint('An error occurred while loading data: $e');
      debugPrint(stackTrace.toString());
      debugPrint("ไม่มีข้อมูล session user ใหม่");
    }
  }

  Future<dynamic> setPhoneEncode(value) async {
    phoneEncode!.value = base64Encode(utf8.encode(value));
    // phoneEncode!.value =
    //     base64Encode(utf8.encode('+66922723107')); // test user เบอร์พีี่เต้
    update();
    // debugPrint('phoneEncode: ${phoneEncode!.value}');
  }

  Future<dynamic> setUserData(first_name, last_name, phone_firebase) async {
    debugPrint('setUserData');
    firstName.value = first_name;
    lastName.value = last_name;
    phoneFirebase.value = phone_firebase;
    update();
  }

  Future<dynamic> getPocketBalance_old_version() async {
    try {
      print('getPocketBalance :${phoneFirebase.value}');
      if (phoneFirebase.value == null ||
          phoneFirebase.value.toString().isEmpty) {
        readyForUse.value = true;
        noLikeWallet.value = true;
        useOnlyLikePoint.value = false;
        return;
      }

      var collectionData = await FirebaseFirestore.instance
          .collection('config')
          .doc('minilike')
          .get();
      var rawData = collectionData.data();
      merchantID!.value = rawData!['merchantID'];
      urlLikePointAPI.value = rawData['urlAPI'];
      urlLikePoint.value = rawData['url'];
      namePoint.value = rawData['namePoint'];
      update();

      Map requestBody = {
        'phone': phoneFirebase.value,
        'merchantID': merchantID!.value,
        // 'phone': '+66111999558',
        // 'merchantID': '185562d3-03e3-4fe7-a244-1261823b4b3f',  pms merchantID
      };
      Uri apiLikePoint =
          Uri.parse('${urlLikePointAPI.value}/wallet/findWalletByPhone');

      final responseWallet = await HttpService.post(apiLikePoint.toString(),
          requestBody, HttpService.KeyRequestHeadersLikePointV2);

      if (responseWallet['data'] != null &&
          responseWallet['data'][0] != null &&
          responseWallet['data'][0]['memberInfo'] == null) {
        Map requestBody = {
          'phone': phoneFirebase.value,
          'firstName': profileCtl.profile.value.firstname,
          'lastName': profileCtl.profile.value.lastname,
          'merchantID': merchantID!.value,
        };
        debugPrint("responseWallet $requestBody");

        Uri apiLikePointPartners =
            Uri.parse('${urlLikePointAPI.value}/member/regis-partners');
        final responseRegis = await HttpService.post(
            apiLikePointPartners.toString(),
            requestBody,
            HttpService.KeyRequestHeadersLikePointV2);

        if (responseRegis &&
            responseRegis['data'] &&
            responseRegis['data'][0] &&
            responseRegis['data'][0]['id']) {
          var memberInfo =
              ResponseMemberLikePoint.fromJson(responseRegis['data'][0]);
          memberID.value = memberInfo.id!;
          Map dataCreatePocket = {
            "memberID": memberID.value,
            "merchantID": merchantID!.value,
          };
          Uri.parse('${urlLikePointAPI.value}/pocket/create-pocket-merchant');
          await HttpService.post(apiLikePointPartners.toString(),
              dataCreatePocket, HttpService.KeyRequestHeadersLikePointV2);
          update();
        }
      } else {
        if (responseWallet['data'] != null &&
            responseWallet['data'][0] != null &&
            responseWallet['data'][0]['walletInfo'] == null) {
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          Map dataCreatePocket = {
            "memberID": memberID.value,
            "merchantID": merchantID!.value,
          };
          Uri apiLikePointCreatePocket = Uri.parse(
              '${urlLikePointAPI.value}/pocket/create-pocket-merchant');
          await HttpService.post(apiLikePointCreatePocket.toString(),
              dataCreatePocket, HttpService.KeyRequestHeadersLikePointV2);
          update();
          debugPrint("memberID ${memberID.value}");
        } else {
          var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
          final pocketBalance = responseWallet['data'][0]['walletInfo']
              ['pocket'][0]['pocketBalance'];
          balanceLikePoint.value =
              f.format(int.parse(pocketBalance.toString()));
          var pocketBalanceTH = int.parse(pocketBalance.toString()) / 100;
          balanceLikePointTH.value = f.format(pocketBalanceTH);
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          debugPrint("memberID ${memberID.value}");
          update();
        }
      }
    } catch (e) {
      debugPrint('error Get Pocket Balance : $e');
    }
  }

  Future<dynamic> getPocketBalance() async {
    try {
      // ตรวจสอบเบอร์โทรศัพท์
      final phoneValue = phoneFirebase.value?.toString();
      if (phoneValue == null || phoneValue.isEmpty) {
        readyForUse.value = true;
        noLikeWallet.value = true;
        useOnlyLikePoint.value = false;
        return;
      }

      var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');

      await getInfoLikePoint(); // TODO get info like point from firebase

      if (merchantID!.value.isEmpty || urlLikePointAPI.value.isEmpty) {
        debugPrint('Invalid LikePoint2.0 data');
        return;
      }

      Map requestBody = {
        'phone': phoneFirebase.value,
        'merchantID': merchantID!.value,
      };
      Uri apiLikePoint =
          Uri.parse('${urlLikePointAPI.value}/wallet/findWalletByPhone');

      final responseWallet = await HttpService.post(apiLikePoint.toString(),
          requestBody, HttpService.KeyRequestHeadersLikePointV2);
      if (responseWallet == null) {
        debugPrint('Response wallet is null');
        return;
      }
      debugPrint("responseWallet $responseWallet");

      if (responseWallet['data'][0]['memberInfo'] == null) {
        Map requestBody = {
          'phone': phoneFirebase.value,
          'firstName': profileCtl.profile.value.firstname,
          'lastName': profileCtl.profile.value.lastname,
          'merchantID': merchantID!.value,
        };

        Uri apiLikePointPartners =
            Uri.parse('${urlLikePointAPI.value}/member/regis-partners');
        final responseRegis = await HttpService.post(
            apiLikePointPartners.toString(),
            requestBody,
            HttpService.KeyRequestHeadersLikePointV2);

        if (responseRegis['data'].length > 0) {
          var memberInfo =
              ResponseMemberLikePoint.fromJson(responseRegis['data'][0]);
          memberID.value = memberInfo.id!;
          Map dataCreatePocket = {
            "memberID": memberID.value,
            "merchantID": merchantID!.value,
          };
          Uri.parse('${urlLikePointAPI.value}/pocket/create-pocket-merchant');
          await HttpService.post(apiLikePointPartners.toString(),
              dataCreatePocket, HttpService.KeyRequestHeadersLikePointV2);
          update();
        } else {
          if (responseWallet['data'][0]['walletInfo'] == null) {
            Map dataCreatePocket = {
              "memberID": responseWallet['data'][0]['memberInfo']['id'],
              "merchantID": merchantID!.value,
            };

            Uri.parse('${urlLikePointAPI.value}/pocket/create-pocket-merchant');
            await HttpService.post(apiLikePointPartners.toString(),
                dataCreatePocket, HttpService.KeyRequestHeadersLikePointV2);
            update();
          } else {
            final pocketBalance = responseWallet['data'][0]['walletInfo']
                ['pocket'][0]['pocketBalance'];
            balanceLikePoint.value =
                f.format(int.parse(pocketBalance.toString()));
            var pocketBalanceTH = int.parse(pocketBalance.toString()) *
                double.parse(valuePoint!.value.toString());
            balanceLikePointTH.value = f.format(pocketBalanceTH);
            memberID.value = responseWallet['data'][0]['memberInfo']['id'];
            update();
          }
        }
      } else {
        if (responseWallet['data'][0]['walletInfo'] == null) {
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          Map dataCreatePocket = {
            "memberID": responseWallet['data'][0]['memberInfo']['id'],
            "merchantID": merchantID!.value,
          };
          Uri apiLikePointCreatePocket = Uri.parse(
              '${urlLikePointAPI.value}/pocket/create-pocket-merchant');
          await HttpService.post(apiLikePointCreatePocket.toString(),
              dataCreatePocket, HttpService.KeyRequestHeadersLikePointV2);
          update();
        } else {
          final pocketBalance = responseWallet['data'][0]['walletInfo']
              ['pocket'][0]['pocketBalance'];
          balanceLikePoint.value =
              f.format(int.parse(pocketBalance.toString()));
          var pocketBalanceTH = int.parse(pocketBalance.toString()) *
              double.parse(valuePoint!.value.toString());
          balanceLikePointTH.value = f.format(pocketBalanceTH);
          memberID.value = responseWallet['data'][0]['memberInfo']['id'];
          update();
        }
      }
    } catch (e) {
      debugPrint('error Get Pocket Balance : $e');
    }
  }

  Future<void> getInfoLikePoint() async {
    try {
      debugPrint('getInfoLikePoint');
      final AppConfigService appConfig = Get.find<AppConfigService>();
      final String environment = appConfig.environment.toString();
      final String configDoc =
          environment.contains("_prod") ? 'minilike' : 'minilike_uat';

      // Fetch Firestore document
      final response = await FirebaseService.firebaseDoc('config', configDoc);

      if (response == null || response.data() == null) {
        debugPrint('Invalid Firestore data in $configDoc');
        return;
      }

      final data = response.data()!;
      debugPrint('data: $data');

      // Update variables
      merchantID?.value = data['merchantID'];
      urlLikePointAPI.value = data['urlAPI'];
      urlLikePoint.value = data['url'];
      namePoint.value = data['namePoint'];
      isOpen?.value = data['status'];
      valuePoint?.value = data['valuePoint'];

      // Notify observers
      update();
    } catch (e, stackTrace) {
      debugPrint('Error in getInfoLikePoint: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }



  Future<void> getActivityPOI() async {
    try {
      // Fetch Firestore document
      final response = await FirebaseService.firebaseDoc('config', 'activity');

      if (response == null || response.data() == null) {
        debugPrint('Invalid Firestore data in activity');
        return;
      }

      final data = response.data()!;
      // debugPrint('data: $data');

      // Update variables with default fallback values
      download_poi.value = data['download'] ?? "";
      referfriend_poi.value = data['referFriend'] ?? "";

      // Notify observers
      update();

      // Fetch additional info
      await getInfoLikePoint();
    } catch (e, stackTrace) {
      debugPrint('Error in getActivityPOI: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  Future<bool> upgradeLikePoint(String phoneFirebase) async {
    try {
      if (phoneFirebase == null || phoneFirebase.toString().isEmpty) {
        debugPrint("phoneFirebase is null");
        return false;
      }
      isUpgrading.value = true;
      update();
      Map requestBodyLikeWallet = {
        'phone': phoneFirebase,
      };

      final responseBalanceLikeWallet = await HttpService.post(
          Uri.parse('${urlLikePointAPI.value}/member/balance-likewallet')
              .toString(),
          requestBodyLikeWallet,
          HttpService.KeyRequestHeadersLikePointV2);

      var totalBalance = double.parse(
          responseBalanceLikeWallet['data']['totalBalance'].toString());
      if (totalBalance > 0) {
        amount.value = totalBalance.toString();
      }
      Map requestBody = {
        'phone': phoneFirebase,
        'merchantID': merchantID?.value,
        'amount': amount.value,
        'address': address.value,
        'displayName': displayName.value,
        'likewalletUID': likewalletUID.value,
        'memberID': memberID.value,
      };
      final response = await HttpService.post(
          Uri.parse(
                  '${urlLikePointAPI.value}/mint-point/mint-point-member-migrate')
              .toString(),
          requestBody,
          HttpService.KeyRequestHeadersLikePointV2);
      if (response['data']['status'] == "Success") {
        isSuccess.value = true;
        useOnlyLikePoint.value = true;
        isAlreadyMigrated.value = true;
        update();
        await getPocketBalance();

        return true;
      } else {
        return false;
      }
    } catch (e) {
      print('error Migrate Like Point : $e');
      return false;
    }
  }

  Future<dynamic> checkMigrate(String phoneFirebase) async {
    try {
      debugPrint("check Migration Point");
      if (appConfigService.countryConfigCollection.toString() != 'aam') {
        Loading(false);
        isAlreadyMigrated.value = true;
        update();
        debugPrint("countryConfigCollection is not aam");
        return;
      }

      if (phoneFirebase == null || phoneFirebase.toString().isEmpty) {
        print("dsdsdds");
        return;
      }

      if (urlLikePointAPI.value.isEmpty ||
          urlLikePointAPI.value == null ||
          urlLikePointAPI.value == "") {
        var url = await secureStorage.readSecureData("urlLikePointAPI");
        urlLikePointAPI.value = url;
        update();
      }

      Map requestBody = {
        'phone': phoneFirebase,
      };

      final responseInfoLikeWallet = await HttpService.post(
          Uri.parse('${urlLikePointAPI.value}/member/info-likewallet')
              .toString(),
          requestBody,
          HttpService.KeyRequestHeadersLikePointV2);

      if (responseInfoLikeWallet['data'].length > 0) {
        var migratable = responseInfoLikeWallet['data']['migratable'];
        var detail = responseInfoLikeWallet['data']['detail'];
        if (migratable == true && detail == 'พบข้อมูลผู้ใช้งาน') {
          Map requestCheckMigrate = {
            'uid': responseInfoLikeWallet['data']['data']['uid']
          };

          final responseMigrate = await HttpService.post(
              Uri.parse('${urlLikePointAPI.value}/member/check-migrate')
                  .toString(),
              requestCheckMigrate,
              HttpService.KeyRequestHeadersLikePointV2);

          // print(responseMigrate);
          var migrateStatus = responseMigrate['data']['status'];

          if (migrateStatus == "already migrated") {
            debugPrint("migrateStatus : already migrated");
            useOnlyLikePoint.value = true;
            isAlreadyMigrated.value = true;
          } else if(migrateStatus == "not migrated") {
            //todo Auto Migrate Like Point
            print("Auto Migrate Like Point");
            statusMigrate.value = true;
            address.value = responseInfoLikeWallet['data']['data']['address'];
            displayName.value =
                responseInfoLikeWallet['data']['data']['displayName'];
            likewalletUID.value = responseInfoLikeWallet['data']['data']['uid'];
            update();
            //todo Auto Migrate Like Point
            await upgradeLikePoint(phoneFirebase);
          }else {
            statusMigrate.value = true;
            address.value = responseInfoLikeWallet['data']['data']['address'];
            displayName.value =
                responseInfoLikeWallet['data']['data']['displayName'];
            likewalletUID.value = responseInfoLikeWallet['data']['data']['uid'];
          }
          Loading(false); //TODO หมุนๆ รอโหลดข้อมูลหน้า home ตรงชื่อ , point
          readyForUse.value = true;
          update();
          print("responseMigrate $responseMigrate");
        } else {
          // print("### else ##");
          // print(responseInfoLikeWallet['data']);
          useOnlyLikePoint.value = true;
          Loading(false); //TODO หมุนๆ รอโหลดข้อมูลหน้า home ตรงชื่อ , point
          readyForUse.value = true;
          update();
          if(responseInfoLikeWallet['data']['detail'] == 'เป็นลูกค้า tier1' && migratable == false){
            debugPrint("เป็นลูกค้า tier1 และ migrate แล้ว ");
            isAlreadyMigrated.value = true;
            update();
          }else if(migratable == false){
            debugPrint("migratable == false");
            isAlreadyMigrated.value = true;
            update();
          }
        }
      }else{
        print("else");
        print(responseInfoLikeWallet);
      }
    } catch (e) {
      print('error Get Phone Encode : $e');
    }
  }

  Future checkAccept(value) async {
    try {
      isChecked.value = value;
      update();
    } catch (e) {
      print('error Pay POI : $e');
    }
  }

  void Loading(value) {
    isLoading.value = value;
    update();
  }
}
