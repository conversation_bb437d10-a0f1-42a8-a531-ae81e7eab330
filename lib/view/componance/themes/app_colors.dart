import 'package:flutter/material.dart';

class AppColors {
  //TODO AAM
  static const Color primaryAAM = Colors.deepPurple;
  static const Color AAMPurple = Color(0xFF792AFF);
  static const Color AAMPurpleBill = Color(0xFF792AFF);
  static const Color AAMPurpleBill2 = Color(0xFFA069FF);
  static const Color AAMPurpleSecondary = Color(0xFFA069FF);
  static const Color AAMPurpleFade = Color(0x26792AFF);
  static const Color AAMPurpleSolid = Color(0xFF995DFE);
  static const Color AAMPrimaryOrange = Color(0xFFFF9300);
  static const Color onBoradingAAM = Color(0xFFFF9300);
  static const Color onBoradingAAM2 = Color(0xFFFFBF69);
  static const Color onBoradingFinalAAM = Color(0xFFFF9300);
  static const Color onBoradingFinalAAM2 = Color(0xFFFFBF69);
  static const Color AAMMyloanCard = Color(0x66FF9300);
  static const Color AAMMyloanCard2 = Color(0x26FF9300);
  static Color AAMLightPurple = const Color(0xFF792AFF).withOpacity(0.15);//


  //TODO rafco
  static const Color primaryRafco = Color(0xFFEA1B23);
  static const Color primaryBillRafco = Color(0xFFF2484E);
  static const Color primaryRafco_Blue = Color(0xFF22409A);
  static const Color Rafco_BlueSecondary = Color(0xFF22409A);
  static const Color onBoradingRAFCO = Color(0xFFEA1B23);
  static const Color onBoradingRAFCO2 = Color(0xFFF1676C);
  static const Color onBoradingFinalRAFCO = Color(0xFFEA1B23);
  static const Color onBoradingFinalRAFCO2 = Color(0xFFF1676C);
  static const Color RAFCOMyloanCard = Color(0xFF22409A);
  static const Color RAFCOMyloanCard2 = Color(0xFF4976FF);
  static Color RAFCOLightRed = const Color(0xFFEA1B23).withOpacity(0.15);//

  //TODO RPLC
  static const Color primaryRPLC = Color(0xFFFDDD46);
  static const Color headingColorRPLC = Color(0xFFFFE500);
  static const Color primaryRPLC_Yellow = Color(0xFFFFC20E);
  static const Color RPLC_YellowSecondary = Color(0xFFFFC20E);
  static const Color primaryRPLC_Grey = Color(0xFF6A7165);
  static const Color onBoradingRPLC = Color(0xFF6A7165);
  static const Color onBoradingRPLC2 = Color(0xFFB8C2B0);
  static const Color primaryRPLC_Heading = Color(0xFFFFE600);
  static const Color onBoradingFinalRPLC = Color(0xFF997408);
  static const Color onBoradingFinalRPLC2 = Color(0xFFFFC20E);
  static const Color buttonColorRPLC = Color(0xBF1A1818);
  static const Color RPLCMyloanCard = Color(0xFFD9A200);
  static const Color RPLCMyloanCard2 = Color(0xFFFFC20E);
  static const Color RPLCYellow = Color(0xFFFFC20E) ; //
  static Color RPLCLightYellow = Color(0xFFFFC20E).withOpacity(0.15) ;//

  static const Color textBlackColor = Color(0xFF1A1818);
  static const Color inActiveButtonColor = Color(0x191A1818);
  static const Color inActiveLoanColor = Color(0x331A1818);
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color indigoPrimary = Color(0xFF032D54);
  static const Color indigoBgSlide = Color(0xFF345776);
  static const Color indigoLow = Color(0xFF305679);
  static const Color indigoDrawer = Color(0XFFFCF0BB);
  static const Color indigoBgMrHistory = Color(0XFFE8ECEF);
  static const Color indigoTran = Color(0xFF9AABBB);
  static const Color indigoProfile = Color(0xFF4D7CA7);
  static const Color indigoProfileText = Color(0xFF083358);
  static Color indigoOpacity = const Color(0xFF9AABBB).withOpacity(0.5);
  static const Color transparent = Colors.transparent;
  static const Color bgNotification = Color(0XFFE1E7EB);
  static const Color errorColor = Color(0xFFFF3B30);

  static const Color loadingColor = Color(0xD9D9D980);
  static const Color grey_01 = Color(0xFFF9F9F9);


  //common color
  static const Color light = Color(0x14147480);
  static const Color darkerGrey = Color(0xFF383838);
  static const Color darkGrey = Color(0xFF6A7165);
  static const Color grey = Color(0xF9AFAEAE);
  static const Color softGrey = Color(0xFFD5D4D4);
  static const Color lightGrey = Color(0xFFECEBEB);



}
