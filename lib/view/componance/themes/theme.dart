import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../controller/AppConfigService.dart';
import 'app_colors.dart';

AppConfigService appConfigService = Get.find<AppConfigService>();

ThemeData configTheme() {
  if (appConfigService.countryConfigCollection.toString() == 'aam') {
    return ThemeData(
      brightness: Brightness.light,
      fontFamily: TextStyleTheme.text_Regular.fontFamily,
      splashColor: AppColors.white, //สี  เอฟเฟค กระพริบ BottomNavigationBar
      focusColor: AppColors.AAMPrimaryOrange, //สี  แจ้งเตือน noti
      highlightColor: AppColors.AAMPurpleSolid,
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.white.withOpacity(0.9),
        titleTextStyle: TextStyle(
          color: AppColors.textBlackColor,
          fontSize: 16,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
          height: 0.09,
        ),
      ),


      //evaluate button
      elevatedButtonTheme :ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          foregroundColor: Colors.white,
          backgroundColor: Colors.grey,
          disabledForegroundColor: Colors.grey,
          disabledBackgroundColor: Colors.grey,
          // side: const BorderSide(color: Colors.grey),
          padding: const EdgeInsets.symmetric(vertical: 12),
          textStyle: const TextStyle( fontSize: 16, color: Colors.white, fontWeight: FontWeight.w600),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),




      //TODO font text
      primaryTextTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 22.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displayMedium: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displaySmall: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        headlineLarge: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        headlineMedium: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        headlineSmall: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        titleLarge: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        titleMedium: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        titleSmall: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        bodyLarge: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        bodyMedium: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        bodySmall: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        labelLarge: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        labelMedium: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        labelSmall: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
      ),
      buttonTheme: ButtonThemeData(
        colorScheme: const ColorScheme(
          primary: AppColors.AAMPurple, // สีหลัก สีม่วง
          secondary: AppColors.AAMPurpleSecondary, // สีรอง สีส้ม
          tertiary: AppColors.inActiveButtonColor, // สี inActive Button
          error: AppColors.errorColor,
          onPrimary: Colors.white,
          onSecondary: Colors.white,
          onBackground: AppColors.textBlackColor,
          onError: AppColors.errorColor,
          brightness: Brightness.light,
          background: AppColors.AAMPurple,
          surface: AppColors.AAMMyloanCard, // สีหลัก my loan
          onSurface: AppColors.AAMMyloanCard2, // สีรอง my loan
        ),
        textTheme: ButtonTextTheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      //TODO สีหลัก สีรอง สีที่สาม สีพื้นหลัง สี error
      colorScheme: const ColorScheme(
        primary: AppColors.AAMPurple, // สีหลัก สีม่วง
        secondary: AppColors.AAMPrimaryOrange, // สีรอง สีส้ม
        tertiary: AppColors.AAMPurpleBill, // สี bill detail หน้า home 1
        surface: AppColors.onBoradingAAM, // สีพื้นหลัง หน้า on boarding
        background: Colors.white,
        error: AppColors.errorColor,
        surfaceVariant: AppColors
            .onBoradingFinalAAM, // สีพื้นหลัง หน้า on boarding หน้า final
        onPrimary: AppColors.AAMPurpleSolid, // สี dot pin หน้า pin code
        onSecondary: AppColors.AAMPurple, // สี reset pin detail หน้า pin code
        onTertiary: AppColors.AAMPurpleBill2, // สี bill detail หน้า home  2
        onSurface: AppColors.onBoradingAAM2,
        onBackground: AppColors.textBlackColor,
        onError: AppColors.errorColor,
        onSurfaceVariant: AppColors
            .onBoradingFinalAAM2, // สีพื้นหลัง หน้า on boarding หน้า final
        brightness: Brightness.light,
      ),

      textTheme: const TextTheme(
        //TODO สี text button
        titleMedium: TextStyle(
          color: AppColors.white,
        ),
        //TODO สี text ทั่วไป
        bodyMedium: TextStyle(
          color: AppColors.textBlackColor,
        ),
      ),

      primaryColor: AppColors.AAMPurple, // สี Header หลักหน้า on boarding & สีจุด หน้า on boarding
      secondaryHeaderColor:
          AppColors.AAMPurple, // สี Header รองหน้า on boarding
      dialogBackgroundColor:
          AppColors.textBlackColor, // สี dialog หน้า on boarding
      primaryColorLight: AppColors.white, // สีพื้นหลัง
      primaryColorDark: AppColors.textBlackColor,
      dividerTheme: DividerThemeData(
        thickness: 1.h,
        color: Colors.white.withOpacity(0.7),
        space: 1,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.textBlackColor.withOpacity(0.5),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        // focusedBorder: OutlineInputBorder(
        //   borderSide: BorderSide(
        //     color: AppColors.AAMPrimaryOrange,
        //   ),
        //   borderRadius: BorderRadius.circular(8),
        // ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: AppColors.errorColor,
          ),
        ),
        hintStyle: TextStyle(
          color: AppColors.textBlackColor.withOpacity(0.2),
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
        ),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
          // color: primaryColor,
          ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          unselectedItemColor: Colors.black.withOpacity(0.5),
          selectedItemColor: AppColors.AAMPrimaryOrange,
        ),
    );
  } else if (appConfigService.countryConfigCollection.toString() == 'rplc') {
    return ThemeData(
      // canvasColor: whiteColor,
      brightness: Brightness.light,
      fontFamily: TextStyleTheme.text_Regular.fontFamily,
      focusColor: AppColors.primaryRPLC_Yellow, //สี  แจ้งเตือน noti
      // splashColor: blackColor.withOpacity(0.08),
      highlightColor: AppColors.primaryRPLC_Yellow,
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.white.withOpacity(0.9),
        titleTextStyle: TextStyle(
          color: AppColors.textBlackColor,
          fontSize: 16,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
          height: 0.09,
        ),
      ),
      //TODO font text
      primaryTextTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 22.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displayMedium: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displaySmall: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        headlineLarge: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        headlineMedium: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        headlineSmall: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        titleLarge: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        titleMedium: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        titleSmall: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        bodyLarge: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        bodyMedium: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        bodySmall: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        labelLarge: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        labelMedium: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        labelSmall: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
      ),
      buttonTheme: ButtonThemeData(
        colorScheme: const ColorScheme(
          primary: AppColors.primaryRPLC_Yellow, // สีหลัก สีม่วง
          secondary: AppColors.RPLC_YellowSecondary, // สีรอง สีส้ม
          tertiary: AppColors.inActiveButtonColor, // สี inActive Button
          error: AppColors.errorColor,
          onPrimary: Colors.white,
          onSecondary: Colors.white,
          onBackground: AppColors.textBlackColor,
          onError: AppColors.errorColor,
          brightness: Brightness.light,
          background: AppColors.buttonColorRPLC,
          surface: AppColors.RPLCMyloanCard, // สีหลัก my loan
          onSurface: AppColors.RPLCMyloanCard2, // สีรอง my loan
        ),
        textTheme: ButtonTextTheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      //TODO สีหลัก สีรอง สีที่สาม สีพื้นหลัง สี error
      colorScheme: const ColorScheme(
        primary: AppColors.primaryRPLC_Yellow, // สีหลัก สีม่วง
        secondary: AppColors.primaryRPLC_Grey, // สีรอง สีส้ม
        tertiary: AppColors.primaryRPLC_Grey, // สี bill detail หน้า home1
        surface: AppColors.onBoradingRPLC, // สีพื้นหลัง หน้า on boarding
        background: Colors.white,
        error: AppColors.errorColor,
        surfaceVariant: AppColors
            .onBoradingFinalRPLC, // สีพื้นหลัง หน้า on boarding หน้า final
        onPrimary: AppColors.primaryRPLC_Grey, // สี dot pin หน้า pin code
        onSecondary:
            AppColors.primaryRPLC_Yellow, // สี reset pin detail หน้า pin code
        onTertiary: AppColors.onBoradingRPLC2, // สี bill detail หน้า home  2
        onSurface: AppColors.onBoradingRPLC2,
        onBackground: AppColors.textBlackColor,
        onError: AppColors.errorColor,
        onSurfaceVariant: AppColors
            .onBoradingFinalRPLC2, // สีพื้นหลัง หน้า on boarding หน้า final
        brightness: Brightness.light,
      ),

      textTheme: const TextTheme(
        //TODO สี text button
        titleMedium: TextStyle(
          color: AppColors.textBlackColor,
        ),
        //TODO สี text ทั่วไป
        bodyMedium: TextStyle(
          color: AppColors.textBlackColor,
        ),
      ),
      primaryColor: AppColors.white, // สี Header หลักหน้า on boarding & สีจุด หน้า on boarding
      secondaryHeaderColor:
          AppColors.primaryRPLC_Heading, // สี Header รองหน้า on boarding
      dialogBackgroundColor: AppColors.white, // สี dialog หน้า on boarding
      primaryColorLight: AppColors.white, // สีพื้นหลัง
      primaryColorDark: AppColors.textBlackColor,
      dividerTheme: DividerThemeData(
        thickness: 1.h,
        color: Colors.white.withOpacity(0.7),
        space: 1,
      ),
      inputDecorationTheme: InputDecorationTheme(
        // prefixStyle: TextStyle(
        //   // color: mainBlack,
        //   fontSize: 14,
        //   fontWeight: FontWeight.w600,
        // ),
        hintStyle: TextStyle(
          color: AppColors.textBlackColor.withOpacity(0.2),
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
        ),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
          // color: primaryColor,
          ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          unselectedItemColor: Colors.black.withOpacity(0.5),
          selectedItemColor: AppColors.primaryRPLC_Yellow,
        )
    );
  } else {
    return ThemeData(
      // canvasColor: whiteColor,
      brightness: Brightness.light,
      fontFamily: TextStyleTheme.text_Regular.fontFamily,
        focusColor: AppColors.primaryRafco_Blue, //สี  แจ้งเตือน noti
      // splashColor: blackColor.withOpacity(0.08),
      highlightColor: AppColors.primaryRafco,
      appBarTheme: AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.white.withOpacity(0.9),
        titleTextStyle: TextStyle(
          color: AppColors.textBlackColor,
          fontSize: 16,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
          height: 0.09,
        ),
      ),
      //TODO font text
      primaryTextTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: 22.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displayMedium: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        displaySmall: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        headlineLarge: TextStyle(
          fontSize: 20.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        headlineMedium: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        headlineSmall: TextStyle(
          fontSize: 18.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        titleLarge: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        titleMedium: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        titleSmall: TextStyle(
          fontSize: 16.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        bodyLarge: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        bodyMedium: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        bodySmall: TextStyle(
          fontSize: 14.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
        labelLarge: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Bold.fontFamily,
          fontWeight: FontWeight.w700,
        ),
        labelMedium: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Semi_Bold.fontFamily,
          fontWeight: FontWeight.w600,
        ),
        labelSmall: TextStyle(
          fontSize: 12.sp,
          fontFamily: TextStyleTheme.text_Regular.fontFamily,
          fontWeight: FontWeight.w400,
        ),
      ),
      buttonTheme: ButtonThemeData(
        colorScheme: const ColorScheme(
          primary: AppColors.primaryRafco_Blue, // สีหลัก สีม่วง
          secondary: AppColors.Rafco_BlueSecondary, // สีรอง สีส้ม
          tertiary: AppColors.inActiveButtonColor, // สี inActive Button
          error: AppColors.errorColor,
          onPrimary: Colors.white,
          onSecondary: Colors.white,
          onBackground: AppColors.textBlackColor,
          onError: AppColors.errorColor,
          brightness: Brightness.light,
          background: AppColors.primaryRafco_Blue,
          surface: AppColors.RAFCOMyloanCard, // สีหลัก my loan
          onSurface: AppColors.RAFCOMyloanCard2, // สีรอง my loan
        ),
        textTheme: ButtonTextTheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      //TODO สีหลัก สีรอง สีที่สาม สีพื้นหลัง สี error
      colorScheme: const ColorScheme(
        primary: AppColors.primaryRafco_Blue, // สีหลัก สีม่วง
        secondary: AppColors.primaryRafco, // สีรอง สีส้ม
        tertiary: AppColors.primaryRafco, // // สี bill detail หน้า home 1
        surface: AppColors.onBoradingRAFCO, // สีพื้นหลัง หน้า on boarding
        background: Colors.white,
        error: AppColors.errorColor,
        surfaceVariant: AppColors
            .onBoradingFinalRAFCO, // สีพื้นหลัง หน้า on boarding หน้า final
        onPrimary: AppColors.primaryRafco_Blue, // สี dot pin หน้า pin code
        onSecondary:
            AppColors.primaryRafco, // สี reset pin detail หน้า pin code
        onTertiary: AppColors.primaryBillRafco, // สี bill detail หน้า home  2
        onSurface: AppColors.onBoradingRAFCO2,
        onBackground: AppColors.textBlackColor,
        onError: AppColors.errorColor,
        onSurfaceVariant: AppColors
            .onBoradingFinalRAFCO2, // สีพื้นหลัง หน้า on boarding หน้า final
        brightness: Brightness.light,
      ),

      textTheme: const TextTheme(
        //TODO สี text button
        titleMedium: TextStyle(
          color: AppColors.white,
        ),
        //TODO สี text ทั่วไป
        bodyMedium: TextStyle(
          color: AppColors.textBlackColor,
        ),
      ),

      primaryColor: AppColors.white, // สี Header หลักหน้า on boarding  & สีจุด หน้า on boarding
      secondaryHeaderColor:
          AppColors.primaryRafco_Blue, // สี Header รองหน้า on boarding
      dialogBackgroundColor: AppColors.white, // สี dialog หน้า on boarding
      primaryColorLight: AppColors.white, // สีพื้นหลัง
      primaryColorDark: AppColors.textBlackColor,
      dividerTheme: DividerThemeData(
        thickness: 1.h,
        color: Colors.white.withOpacity(0.7),
        space: 1,
      ),
      inputDecorationTheme: InputDecorationTheme(
        // prefixStyle: TextStyle(
        //   // color: mainBlack,
        //   fontSize: 14,
        //   fontWeight: FontWeight.w600,
        // ),
        hintStyle: TextStyle(
          color: AppColors.textBlackColor.withOpacity(0.2),
          fontSize: 14.sp,
          fontWeight: FontWeight.w400,
        ),
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
          // color: primaryColor,
          ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          unselectedItemColor: Colors.black.withOpacity(0.5),
          selectedItemColor: AppColors.primaryRafco_Blue,
        )
    );
  }
}
