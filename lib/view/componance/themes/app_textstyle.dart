
import 'package:flutter/cupertino.dart';

class TextStyleTheme {

static TextStyle get text_Bold {
    return const TextStyle(
      fontFamily: 'NotoSansThai-Bold',
    );
}
static TextStyle get text_Semi_Bold {
    return const TextStyle(
      fontFamily: 'NotoSansThai-Bold',
    );
}
static TextStyle get text_Regular {
    return const TextStyle(
      fontFamily: 'NotoSansThai',
    );
}

static TextStyle get IBMPlexSansThai {
    return const TextStyle(
      fontFamily: 'IBMPlexSansThai',
    );
  }

  static TextStyle get subtitle {
    return const TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.normal,
    );
  }

  static TextStyle get body {
    return const TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.normal,
    );
  }

  static TextStyle get caption {
    return const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.normal,
    );
  }

}
