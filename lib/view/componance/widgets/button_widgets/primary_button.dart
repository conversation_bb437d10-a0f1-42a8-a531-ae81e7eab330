import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PrimaryButton extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final double? height;
  final double? buttonWidth;
  final Color? backgroundColor;
  final Color? backgroundInactiveColor;
  final Color? textColor;
  final bool? isActive;

  const PrimaryButton(
      {Key? key,
      required this.title,
      required this.onPressed,
      this.height,
      this.buttonWidth,
      this.backgroundColor,
      this.backgroundInactiveColor,
      this.textColor,
      this.isActive = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: buttonWidth,
        height: 52.h,
        margin: EdgeInsets.only(top: height ?? 0.0),
        decoration: ShapeDecoration(
          color: isActive == true ? backgroundColor : backgroundInactiveColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: textColor,
            fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
            fontFamily: configTheme().primaryTextTheme.bodyMedium?.fontFamily,
            fontWeight: FontWeight.w500,
            // height: 0.14.h,
          ),
        ),
      ),
    );
  }
}

class PrimaryButtonWithIcon extends StatelessWidget {
  final String title;
  final VoidCallback onPressed;
  final double? height;
  final double? buttonWidth;
  final Color? backgroundColor;
  final Color? backgroundInactiveColor;
  final Color? textColor;
  final bool? isActive;
  final Widget? icon;
  final Widget? iconBehind;

  const PrimaryButtonWithIcon(
      {Key? key,
      required this.title,
      required this.onPressed,
      this.height,
      this.buttonWidth,
      this.backgroundColor,
      this.backgroundInactiveColor,
      this.textColor,
      this.isActive = false,
      this.icon,
      this.iconBehind})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: buttonWidth,
        height: 52.h,
        margin: EdgeInsets.only(top: height ?? 0.0),
        decoration: ShapeDecoration(
          color: isActive == true ? backgroundColor : backgroundInactiveColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon ?? Container(),
            SizedBox(
              width: 10.w,
            ),
            Container(
              alignment: Alignment.center,
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: textColor,
                  fontSize: configTheme().primaryTextTheme.titleMedium?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.titleMedium?.fontFamily,
                  fontWeight: configTheme().primaryTextTheme.titleMedium?.fontWeight,
                  // height: 0.14.h,
                ),
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
            iconBehind ?? Container(),
          ],
        ),
      ),
    );
  }
}
