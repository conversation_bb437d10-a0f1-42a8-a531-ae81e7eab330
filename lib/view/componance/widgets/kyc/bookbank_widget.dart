import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';
import '../../themes/theme.dart';
import '../../utils/AppSvgImage.dart';
import '../button_widgets/primary_button.dart';

class BookBankComponance {
  static buildDropDownBookBank(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<KYCController>(builder: (kycCtl) {
            return Container(
              width: 375.w,
              height: 624.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                children: [
                  Container(
                    height: 34.h,
                    alignment: Alignment.center,
                    child: SvgPicture.string(AppSvgImage.close_bar),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Container(
                    height: 48.h,
                    alignment: Alignment.center,
                    child: Text(
                      'เลือกธนาคาร',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize: configTheme().primaryTextTheme.titleMedium!.fontSize,
                        fontFamily: configTheme().primaryTextTheme.titleMedium!.fontFamily,
                        fontWeight: configTheme().primaryTextTheme.titleMedium!.fontWeight,
                      ),
                    ),
                  ),
                  Divider(
                    height: 0.03.h,
                    thickness: 1,
                    color: Color(0x331A1818),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Container(
                     height:  430.h,
                    // color: Colors.red,
                    margin: EdgeInsets.symmetric(horizontal: 24.w),
                    child: ListView.builder(
                      itemCount: kycCtl.listBankName.length,
                        itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          kycCtl.setDropDownBank(index);
                          Get.back();
                        },
                        child: Column(
                          children: [
                            Container(
                              width: 327.w,
                              height: 70.h,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 34.w,
                                    height: 34.h,
                                   child: SvgPicture.string(kycCtl.listBankIcon[index]),
                                  ),
                                  SizedBox(width: 12.w),
                                  SizedBox(
                                    height: 22.h,
                                    child: Text(
                                      kycCtl.listBankName[index],
                                      style: TextStyle(
                                        color: Color(0xFF1A1818),
                                        fontSize: configTheme().primaryTextTheme.bodySmall!.fontSize,
                                        fontFamily: configTheme().primaryTextTheme.bodySmall!.fontFamily,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Divider(
                              height: 0.03.h,
                              thickness: 1,
                              color: Color(0x331A1818),
                            ),
                          ],
                        ),
                      );
                    }),
                  )
                ],
              ),
            );
          });
        });
  }
}