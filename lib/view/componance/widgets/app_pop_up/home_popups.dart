import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../controller/config/appConfig.controller.dart';

class HomePopUp {
  static alertAllowContactAAM(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: Get.width,
                  height: 221.h,
                  child: Image.asset(
                    AppImageAssets.aam_allow_access,
                    fit: BoxFit.fill,
                  ),
                ),
                Container(
                  width: Get.width,
                  height: 168.h,
                  decoration: BoxDecoration(
                    color: Color(0xFF792AFF),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 26.w, top: 18.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: inviteFriend.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 14,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextSpan(
                                    text: receivePoint.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 20,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  TextSpan(
                                    text: referFriend.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 12,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 25.w, top: 38.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: () async {
                                print('allow access');
                                await Get.find<AppConfigController>().allowContractPermission();
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                width: 158.w,
                                height: 52.h,
                                decoration: ShapeDecoration(
                                  color: Color(0xBF1A1818),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  openContact.tr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white
                                        .withOpacity(0.8999999761581421),
                                    fontSize: 14,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 26.h),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                color: Colors.transparent,
                                height: 17.h,
                                alignment: Alignment.centerRight,
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: notNow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: skipAllow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  static alertAllowContactRPLC(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: Get.width,
                        height: 221.h,
                        child: Image.asset(
                          AppImageAssets.rplc_allow_access,
                          fit: BoxFit.fill,
                        ),
                      ),
                      Container(
                        width: Get.width,
                        height: 168.h,
                        decoration: BoxDecoration(
                          color: Color(0xFF6A7165),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(
                                  margin:
                                      EdgeInsets.only(left: 26.w, top: 18.h),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text.rich(
                                        TextSpan(
                                          children: [
                                            TextSpan(
                                              text: inviteFriend.tr,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 14,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            TextSpan(
                                              text: receivePoint.tr,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 20,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            TextSpan(
                                              text: referFriend.tr,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 12,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(right: 25.w),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      InkWell(
                                        onTap: () async {
                                          print('allow access');
                                          await Get.find<AppConfigController>().allowContractPermission();
                                          Navigator.pop(context, true);
                                        },
                                        child: Container(
                                          width: 158.w,
                                          height: 52.h,
                                          decoration: ShapeDecoration(
                                            color: Color(0xBF1A1818),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                          ),
                                          alignment: Alignment.center,
                                          child: Text(
                                            openContact.tr,
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              color: Colors.white.withOpacity(
                                                  0.8999999761581421),
                                              fontSize: 14,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                color: Colors.transparent,
                                height: 17.h,
                                margin: EdgeInsets.only(top: 25.h),
                                alignment: Alignment.center,
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: notNow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: skipAllow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: Get.width,
                  alignment: Alignment.bottomCenter,
                  padding: EdgeInsets.only(bottom: 150.h),
                  child: Image.asset(
                    AppImageAssets.rplc_allow_access2,
                    fit: BoxFit.fill,
                  ),
                ),
              ],
            ),
          );
        });
  }

  static alertAllowContactRAFCO(BuildContext context) {
    // print("dsdsksfjkgd");
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: Get.width,
                        height: 221.h,
                        child: Image.asset(
                          AppImageAssets.rafco_allow_access,
                          fit: BoxFit.fill,
                        ),
                      ),
                      Container(
                        width: Get.width,
                        height: 168.h,
                        decoration: BoxDecoration(
                          color: Color(0xFF22409A),
                        ),
                        child: Container(
                          margin: EdgeInsets.only(top: 20.h),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(left: 26.w),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: inviteFriend.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 14,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              TextSpan(
                                                text: receivePoint.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 20,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              TextSpan(
                                                text: referFriend.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 12,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(right: 25.w),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () async {
                                            print('allow access');
                                            await Get.find<AppConfigController>().allowContractPermission();
                                            Navigator.pop(context, true);
                                          },
                                          child: Container(
                                            width: 158.w,
                                            height: 52.h,
                                            decoration: ShapeDecoration(
                                              color: Color(0xFFEA1B23),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                            alignment: Alignment.center,
                                            child: Text(
                                              openContact.tr,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 14,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              InkWell(
                                onTap: () {
                                  Navigator.pop(context, true);
                                },
                                child: Container(
                                  color: Colors.transparent,
                                  height: 17.h,
                                  margin: EdgeInsets.only(top: 25.h),
                                  alignment: Alignment.center,
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                          text: notNow.tr,
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(
                                                0.8999999761581421),
                                            fontSize: 12,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                        TextSpan(
                                          text: skipAllow.tr,
                                          style: TextStyle(
                                            color: Colors.white.withOpacity(
                                                0.8999999761581421),
                                            fontSize: 12,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w700,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                        ),
                                      ],
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }

  static alertAllowNotificationAAM(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Stack(
              children: [
                Container(
                  width: Get.width,
                  height: 168.h,
                  margin: EdgeInsets.only(top: 644.h),
                  decoration: BoxDecoration(color: Color(0xFF792AFF)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 26.w, top: 18.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: openNoti.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 14,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextSpan(
                                    text: receiveNoti.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 20,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: receiveNotiDetail.tr,
                                    style: TextStyle(
                                      color: Colors.white
                                          .withOpacity(0.8999999761581421),
                                      fontSize: 12,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  // TextSpan(
                                  //   text: 'และกิจกรรมก่อนใคร',
                                  //   style: TextStyle(
                                  //     color: Colors.white
                                  //         .withOpacity(0.8999999761581421),
                                  //     fontSize: 12,
                                  //     fontFamily: TextStyleTheme
                                  //         .text_Regular.fontFamily,
                                  //     fontWeight: FontWeight.w400,
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(right: 25.w, top: 38.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: () async {
                                print('allow notification');
                                await Get.find<AppConfigController>().allowNotificationPermission();
                                // ขออนุญาตการแจ้งเตือน
                                // await Get.find<AppConfigController>()
                                //     .checkAndRequestNotificationPermission(
                                //         context);
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                width: 158.w,
                                height: 52.h,
                                decoration: ShapeDecoration(
                                  color: Color(0xBF1A1818),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  openNoti.tr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Colors.white
                                        .withOpacity(0.8999999761581421),
                                    fontSize: 14,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(height: 26.h),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                color: Colors.transparent,
                                height: 17.h,
                                alignment: Alignment.centerRight,
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: notNow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: skipAllow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      alignment: Alignment.centerRight,
                      width: 129.w,
                      height: 112.h,
                      margin: EdgeInsets.only(top: 562.h),
                      child: Image.asset(
                        AppImageAssets.aam_allow_noti,
                        fit: BoxFit.fill,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }

  static alertAllowNotificationRPLC(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: Get.width,
                        height: 190.h,
                        decoration: BoxDecoration(
                          color: Color(0xFF6A7165),
                        ),
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(top: 27.h),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(left: 26.w),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: openNoti.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 14,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              TextSpan(
                                                text: receiveNoti.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 20,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(right: 25.w),
                                    alignment: Alignment.topRight,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () async {
                                            print('allow notification');
                                            // ขออนุญาตการแจ้งเตือน
                                            await Get.find<AppConfigController>().allowNotificationPermission();
                                            // await Get.find<
                                            //         AppConfigController>()
                                            //     .checkAndRequestNotificationPermission(
                                            //         context);
                                            Navigator.pop(context, true);
                                          },
                                          child: Container(
                                            width: 158.w,
                                            height: 52.h,
                                            decoration: ShapeDecoration(
                                              color: Color(0xBF1A1818),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                            alignment: Alignment.center,
                                            child: Text(
                                              openNoti.tr,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 14,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 24.w, top: 6.h),
                              alignment: Alignment.topLeft,
                              child: Text.rich(
                                textAlign: TextAlign.start,
                                TextSpan(
                                  text: receiveNotiDetail.tr,
                                  style: TextStyle(
                                    color: Colors.white
                                        .withOpacity(0.8999999761581421),
                                    fontSize: 12,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                color: Colors.transparent,
                                height: 17.h,
                                margin: EdgeInsets.only(top: 20.h),
                                alignment: Alignment.center,
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: notNow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: skipAllow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: Get.width,
                  alignment: Alignment.bottomRight,
                  padding: EdgeInsets.only(bottom: 133.h, left: 160.w),
                  child: Image.asset(
                    AppImageAssets.rplc_allow_noit,
                    fit: BoxFit.fill,
                  ),
                ),
              ],
            ),
          );
        });
  }

  static alertAllowNotificationRAFCO(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: Get.height,
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: Get.width,
                        height: 190.h,
                        decoration: BoxDecoration(
                          color: Color(0xFF22409A),
                        ),
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(top: 27.h),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: 161.w,
                                    margin: EdgeInsets.only(left: 26.w),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text.rich(
                                          overflow: TextOverflow.ellipsis,
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                text: openNoti.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 14,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                              TextSpan(
                                                text: receiveNoti.tr,
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(
                                                          0.8999999761581421),
                                                  fontSize: 20,
                                                  fontFamily: TextStyleTheme
                                                      .text_Regular.fontFamily,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(right: 24.w),
                                    alignment: Alignment.topRight,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        InkWell(
                                          onTap: () async {
                                            print('allow notification');
                                            // ขออนุญาตการแจ้งเตือน
                                            await Get.find<AppConfigController>().allowNotificationPermission();
                                            // await Get.find<
                                            //         AppConfigController>()
                                            //     .checkAndRequestNotificationPermission(
                                            //         context);
                                            Navigator.pop(context, true);
                                          },
                                          child: Container(
                                            width: 158.w,
                                            height: 52.h,
                                            decoration: ShapeDecoration(
                                              color: Color(0xFFEA1B23),
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                            ),
                                            alignment: Alignment.center,
                                            child: Text(
                                              openNoti.tr,
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(
                                                    0.8999999761581421),
                                                fontSize: 14,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 24.w),
                              alignment: Alignment.topLeft,
                              child: Text.rich(
                                overflow: TextOverflow.ellipsis,
                                textAlign: TextAlign.start,
                                TextSpan(
                                  text: receiveNotiDetail.tr,
                                  style: TextStyle(
                                    color: Colors.white
                                        .withOpacity(0.8999999761581421),
                                    fontSize: 12,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context, true);
                              },
                              child: Container(
                                color: Colors.transparent,
                                height: 17.h,
                                margin: EdgeInsets.only(top: 20.h),
                                alignment: Alignment.center,
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: notNow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                      TextSpan(
                                        text: skipAllow.tr,
                                        style: TextStyle(
                                          color: Colors.white
                                              .withOpacity(0.8999999761581421),
                                          fontSize: 12,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w700,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: Get.width,
                  alignment: Alignment.bottomRight,
                  padding: EdgeInsets.only(
                      bottom: 174.76.h, left: 211.w, right: 24.w),
                  child: Image.asset(
                    AppImageAssets.rafco_allow_noit,
                    fit: BoxFit.fill,
                  ),
                ),
              ],
            ),
          );
        });
  }
}
