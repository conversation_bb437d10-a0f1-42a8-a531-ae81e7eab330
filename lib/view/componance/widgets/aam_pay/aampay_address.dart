import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/register/registerAddress.controller.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../../controller/transalation/translation_key.dart';
import '../../themes/app_textstyle.dart';
import '../../themes/theme.dart';
import '../../utils/AppSvgImage.dart';

class AAMPay_address {
  static buildUpdateAddressData(context, title) {
    FocusNode textFieldFocus = FocusNode();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (context) {
          return GetBuilder<AAMPayController>(builder: (aampayCtl) {
            return Container(
              width: Get.width,
              height: 519.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.0.r),
                  topRight: Radius.circular(12.0.r),
                ),
                color: Color(0xFFFFFFFF),
              ),
              child: Container(
                // width: 327.w,
                // height: 158.h,
                margin: EdgeInsets.only(top: 20.h, left: 24.w, right: 24.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  // crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: Get.width,
                      height: 34.h,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 24.h,
                            child: Center(
                              child: Text(
                                title == "address" ? "บ้านเลขที่" : "หมู่",
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: 14.sp,
                                  fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w600,
                                  height: 0.10,
                                ),
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              alignment: Alignment.topRight,
                              child: SvgPicture.string(AppSvgImage.close_btn),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Divider(
                      color: Color(0x1A181833),
                      thickness: 0.3.h,
                    ),
                    SizedBox(height: 12.h),
                    Container(
                      width: Get.width,
                      height: 48.h,
                      decoration: ShapeDecoration(
                        color: Color(0xFFF9F9F9),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: TextFormField(
                        textAlign: TextAlign.start,
                        cursorWidth: 2.w,
                        cursorHeight: 24.h,
                        cursorColor: configTheme().colorScheme.primary,
                        controller: title == "address" ? aampayCtl.controllerAddress.value :aampayCtl.controllerMoo.value,
                        // keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.only(left: 14.w),
                          border: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          errorBorder: InputBorder.none,
                          disabledBorder: InputBorder.none,
                          // labelText: '',

                          labelStyle: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 12.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w700,
                            height: 0.12,
                          ),
                          hintText: title == "address"
                              ? "กรอกบ้านเลขที่"
                              : "หมู่",
                          hintStyle: TextStyle(
                            color: Color(0x7F1A1818),
                            fontSize: 12.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w400,
                            height: 0.12,
                          ),
                        ),
                        style: TextStyle(
                          color: Color(0xFF1A1818),
                          fontSize: 14.sp,
                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                          fontWeight: FontWeight.w600,
                          // height: 0.12,
                        ),
                        focusNode: textFieldFocus,
                        onTap: () {
                          FocusScope.of(context).requestFocus(textFieldFocus);
                        },
                      ),
                    ),
                    SizedBox(height: 12.h),
                    PrimaryButton(
                        title: accountSave.tr,
                        buttonWidth: Get.width,
                        textColor: Colors.white
                            .withOpacity(0.****************),
                        backgroundColor: configTheme().colorScheme.primary,
                        backgroundInactiveColor: Color(0xFF1A181826)
                            .withOpacity(0.15),
                        isActive: title == "address" ? aampayCtl.controllerAddress.value.text.isNotEmpty :aampayCtl.controllerMoo.value.text.isNotEmpty
                            ? true
                            : false,
                        onPressed: () {
                          aampayCtl.updateAddressValue(
                              title, title == "address" ? aampayCtl.controllerAddress.value.text :aampayCtl.controllerMoo.value.text);
                        })
                  ],
                ),
              ),
            );
          });
        });
  }

  static buildUpdateProvince(context, String type, controller) {
    final AAMPayController aampayCtl = Get.find<AAMPayController>();
    final RegisterAddressController addressCtl = Get.find<RegisterAddressController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            height: 624.h,
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 375.w,
                  height: 48.h,
                  // color: Colors.teal,
                  // margin: EdgeInsets.only(top: 28.h),
                  child: Center(
                    child: Text(
                      type == accountAddressProvince.tr
                          ? accountAddress.tr
                          : type == accountAddressDistrict.tr?
                      signUpChooseDistrict.tr
                          : accountAddressSubDistrict.tr ,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize: 16.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w600,
                        // height: 0.19.h,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: const Color(0x191A1818),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 524.h,
                  child: ListView.builder(
                      itemCount: type == accountAddressProvince.tr
                          ? addressCtl.citiesData.length
                          : type == accountAddressDistrict.tr
                          ? addressCtl.districtsData.length
                          : type == accountAddressSubDistrict.tr
                          ? addressCtl.subDistrictsData.length
                          : 0,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(left: 24.w, right: 24.w),
                          child: InkWell(
                            onTap: () {
                              aampayCtl.setDropdownAddress(context, type, index);
                              // if(type == accountAddressProvince.tr){
                              //   aampayCtl.selectedProvince!.value = addressCtl.citiesData[index].cityName.toString();
                              //   aampayCtl.selectedDistrict!.value = '';
                              //   aampayCtl.selectedSubDistrict!.value = '';
                              // } else if(type == accountAddressDistrict.tr){
                              //   aampayCtl.selectedDistrict!.value = addressCtl.districtsData[index].districtName.toString();
                              //   aampayCtl.selectedSubDistrict!.value = '';
                              // } else if(type == accountAddressSubDistrict.tr){
                              //   aampayCtl.selectedSubDistrict!.value = addressCtl.subDistrictsData[index].subDistrictName.toString();
                              // }
                            },
                            child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: const Color(0x191A1818),
                                      width: 0.5.w,
                                    ),
                                  ),
                                ),
                                child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      type == accountAddressProvince.tr
                                          ? addressCtl
                                          .citiesData[index].cityNameLocal
                                          .toString()
                                          : type == accountAddressDistrict.tr
                                          ? addressCtl.districtsData[index]
                                          .districtNameLocal
                                          .toString()
                                          : type == accountAddressSubDistrict.tr
                                          ? addressCtl
                                          .subDistrictsData[index]
                                          .subDistrictNameLocal
                                          .toString()
                                          : '',
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 16.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        // height: 0.19.h,
                                      ),
                                    ))),
                          ),
                        );
                      }),
                )
              ],
            ),
          );
        });
  }

}
