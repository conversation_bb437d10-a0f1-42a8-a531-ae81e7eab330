import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/aampay/aampay_form.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pinput/pinput.dart';

import '../../../../controller/transalation/translation_key.dart';
import '../../../screen/loan_screen/myloan_detail_screen.dart';
import '../../themes/app_textstyle.dart';
import '../../utils/AppSvgImage.dart';
import '../animation/shimmer_effect.dart';
import '../app_pop_up/policy_popups/terms_condition.dart';
import '../button_widgets/primary_button.dart';

class AAMPAY_Componance {
  static aampayAds(String loanAmount) {
    var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
    final ContractListController contractListCtl = Get.find<ContractListController>();
    return Opacity(
      opacity: contractListCtl.chkCountAAMPay!.value == true ?0.5 :1,
      child: Container(
        width: double.maxFinite,
        margin: EdgeInsets.symmetric(horizontal: 22.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 160.h,
              width: 154.w,
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                gradient: AppColorsGradient.AAMPAYGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  Container(
                    height: 28.h,
                    alignment: Alignment.centerLeft,
                    child: ShaderMask(
                      shaderCallback: (Rect bounds) {
                        return LinearGradient(
                          colors: <Color>[
                            AppColors.AAMPurple.withOpacity(1),
                            AppColors.AAMPurple.withOpacity(0.8)
                          ],
                          stops: [0.00, -1.00],
                          tileMode: TileMode.mirror,
                        ).createShader(bounds);
                      },
                      child: SizedBox(
                        height: 28.h,
                        child: Text(
                          '฿${f.format(int.parse(loanAmount))}',
                          style: TextStyle(
                            fontSize: 22.sp,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w600,
                            color: Colors
                                .white, // Important to set this to see the gradient
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  SizedBox(
                    width: 130.w,
                    height: 60.h,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 20.h,
                          child: Text(
                            "เอเอเอ็ม เปย์",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .titleMedium!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .titleMedium!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .titleMedium!
                                  .fontWeight,
                              color: configTheme()
                                  .textTheme
                                  .bodyMedium!
                                  .color, // Important to set this to see the gradient
                            ),
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Container(
                          height: 36.h,
                          alignment: Alignment.bottomLeft,
                          child: Text(
                            "สินเชื่อเงินสดพร้อมใช้\nสะดวกให้ก่อน อนุมัติง่าย",
                            style: TextStyle(
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontWeight,
                              color: configTheme().textTheme.bodyMedium!.color,
                              letterSpacing: 0,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  InkWell(
                    onTap: () {
                      if(!contractListCtl.chkCountAAMPay!.value){
                        Get.to(() => AAMPAYFormScreen());
                      }else{
                        Get.snackbar('แจ้งเตือน', 'คุณมีสัญญาเอเอเอ็ม เปย์ อยู่แล้ว\nกรุณาติดต่อเจ้าหน้าที่');
                      }
                    },
                    child: Container(
                      width: 130.w,
                      height: 36.h,
                      decoration: ShapeDecoration(
                        gradient: AppColorsGradient.buttonAAMGradient,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        shadows: [
                          BoxShadow(
                            color: AppColors.AAMPurple.withOpacity(0.5),
                            spreadRadius: 0,
                            blurRadius: 4,
                            offset: Offset(0, 4), // changes position of shadow
                          ),
                        ],
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        "รับสินเชื่อ",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize:
                              configTheme().primaryTextTheme.bodyMedium?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontFamily,
                          fontWeight: FontWeight.w500,
                          // height: 0.14.h,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static acceptAAMPAYPolicy(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: false,
        enableDrag: false,
        isDismissible: false,
        backgroundColor: Colors.white.withOpacity(1.0),
        barrierColor: Colors.black.withOpacity(0.2),
        builder: (context) {
          return GetBuilder<AAMPayController>(
            init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return WillPopScope(
              onWillPop: () async {
                // Prevents the back button from closing the bottom sheet
                return false;
              },
              child: Container(
                  width: Get.width,
                  height: 326.h,
                  decoration: ShapeDecoration(
                    color: configTheme().colorScheme.background,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                  ),
                  child: Column(children: [
                    Container(
                        width: 335.w,
                        margin:
                            EdgeInsets.only(left: 20.w, right: 20.w, top: 30.h),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 50.h,
                              padding: const EdgeInsets.only(left: 2, right: 2),
                              decoration: const BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0x0A000000),
                                    blurRadius: 18,
                                    offset: Offset(3, 6),
                                    spreadRadius: 0,
                                  )
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    width: 2.w,
                                    height: 50.h,
                                    decoration: BoxDecoration(
                                      color: AppColors.AAMPurple,
                                      borderRadius: BorderRadius.circular(50),
                                    ),
                                  ),
                                  SizedBox(width: 12.w),
                                  Container(
                                    child: Text(
                                      "ข้อตกลงและเงื่อนไขการใช้บริการ\nสินเชื่อเอเอเอ็ม เปย์",
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            SizedBox(
                              child: Text(
                                "เพื่อรักษาประโยชน์ของตัวท่านสำหรับการให้บริการนี้\nกรุณากดยอมรับข้อตกลงด้านล่าง เพื่อดำเนินการต่อ",
                                style: TextStyle(
                                  color: const Color(0x7F1A1818),
                                  fontSize: 14,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 34.h,
                            ),
                            SizedBox(
                              height: 20.h,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      aampayCtl.acceptTermPolicy();
                                    },
                                    child: Container(
                                        child: Row(
                                      children: [
                                        Container(
                                          width: 20.w,
                                          height: 20.h,
                                          decoration: ShapeDecoration(
                                            color: aampayCtl
                                                    .isAcceptedTermPolicy!.value
                                                ? AppColors.AAMPurple
                                                : Colors.transparent,
                                            shape: OvalBorder(
                                              side: BorderSide(
                                                  width: 1.w,
                                                  color: AppColors.AAMPurple
                                                      .withOpacity(0.25)),
                                            ),
                                          ),
                                          child: SvgPicture.string(
                                              AppSvgImage.check),
                                        ),
                                        const SizedBox(width: 10),
                                        SizedBox(
                                          height: 20,
                                          child: Text(
                                            "ยอมรับข้อตกลงการใช้บริการ",
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: 14,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                        ),
                                      ],
                                    )),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      TermsAndConditionWidget.buildTermsAndConditionsAAM_Pay(context);
                                    },
                                    child: Container(
                                      color: Colors.transparent,
                                      child: SvgPicture.string(
                                          AppSvgImage.icon_more_info),
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        )),
                    SizedBox(
                      height: 34.h,
                    ),
                    PrimaryButton(
                        title: "ยอมรับ",
                        onPressed: () async {
                          if (aampayCtl.isAcceptedTermPolicy!.value) {
                            var chk = await aampayCtl.updateDigitalAgreement(
                                context); //TODO เปิดถ้าปล่อยใช้จริง ใช้สำหรับเก็บข้อมูล appcept policy & set script สำหรับโอนเงินอัตโนมัติ
                            if (chk) {
                              Navigator.pop(context, true);
                            }
                            // Navigator.pop(context, true);
                          }
                        },
                        buttonWidth: 327.w,
                        backgroundColor: AppColors.AAMPurple,
                        backgroundInactiveColor: AppColors.inActiveButtonColor,
                        textColor: Colors.white,
                        isActive: aampayCtl.isAcceptedTermPolicy!.value),
                  ])),
            );
          });
        });
  }

  static buildInfoContract(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<AAMPayController>(
              init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return Container(
              width: 375.w,
              height: 364.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                children: [
                  Container(
                    height: 34.h,
                    alignment: Alignment.center,
                    child: SvgPicture.string(AppSvgImage.close_bar),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  SvgPicture.string(
                    AppSvgImage.aampay_infoIcon,
                    width: 70.w,
                    height: 70.h,
                  ),
                  SizedBox(
                    height: 11.18.h,
                  ),
                  SizedBox(
                    width: 327.w,
                    height: 102.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 327.w,
                          height: 20.h,
                          child: Text(
                            'สินเชื่อเงินสด อนุมัติง่าย',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .titleLarge!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .titleLarge!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .titleLarge!
                                  .fontWeight,
                            ),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        SizedBox(
                          width: 327.w,
                          child: Text(
                            'สินเชื่อพร้อมให้ สิทธิประโยชน์สุดพิเศษ\nสำหรับลูกค้าเครดิตดี ของเอเอเอ็ม เท่านั้น\nพร้อมโอน ไม่ต้องรอนาน อนุมัติทันที',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xBF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 26.h,
                  ),
                  PrimaryButton(
                    title: "เข้าใจแล้ว",
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    buttonWidth: 327.w,
                    backgroundColor: AppColors.AAMPurple,
                    backgroundInactiveColor: AppColors.inActiveButtonColor,
                    textColor: Colors.white,
                    isActive: true,
                  )
                ],
              ),
            );
          });
        });
  }

  static buildCancleContract(context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<AAMPayController>(
              init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return Container(
              width: 375.w,
              height: 308.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                children: [
                  Container(
                    height: 34.h,
                    alignment: Alignment.center,
                    child: SvgPicture.string(AppSvgImage.close_bar),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  SizedBox(
                    width: 327.w,
                    height: 80.h,
                    child: Row(
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              width: 250.w,
                              height: 20.h,
                              child: Text(
                                'ยกเลิกการทำรายการ',
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontWeight,
                                ),
                              ),
                            ),
                            SizedBox(height: 6.h),
                            Text(
                              'คุณต้องการจะยกเลิกรายการขอสินเชื่อ\nเอเอเอ็ม เปย์ หรือไม่?',
                              style: TextStyle(
                                color: Color(0xBF1A1818),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall!
                                    .fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall!
                                    .fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall!
                                    .fontWeight,
                              ),
                            ),
                          ],
                        ),
                        Container(
                          alignment: Alignment.topRight,
                          child: SvgPicture.string(
                            AppSvgImage.aampay_cancle,
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  PrimaryButton(
                    title: "ดำเนินการต่อ",
                    onPressed: () {
                      Navigator.pop(context, true);
                    },
                    buttonWidth: 327.w,
                    backgroundColor: AppColors.AAMPurpleSolid,
                    backgroundInactiveColor: AppColors.inActiveButtonColor,
                    textColor: Colors.white,
                    isActive: true,
                  ),
                  PrimaryButton(
                    title: "ยกเลิก",
                    onPressed: () {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => HomeNavigator()));
                      // Navigator.pop(context, false);
                    },
                    buttonWidth: 327.w,
                    backgroundColor: Colors.transparent,
                    backgroundInactiveColor: AppColors.inActiveButtonColor,
                    textColor: configTheme().textTheme.bodyMedium!.color,
                    isActive: true,
                  ),
                ],
              ),
            );
          });
        });
  }

  static buildConfirmContract(context) {
    var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
    final KYCController kycCtl = Get.put(KYCController());
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<AAMPayController>(
              init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return Container(
              width: 375.w,
              height: 519.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                      width: 375.w,
                      height: 34.h,
                      child: SvgPicture.string(AppSvgImage.close_bar)),
                  SizedBox(height: 16.h),
                  Container(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 327.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 23.h,
                                child: Text(
                                  'รายละเอียดคำขอสินเชื่อ',
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontWeight,
                                  ),
                                ),
                              ),
                              //todo icon
                              SizedBox(
                                height: 43.h,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 327.w,
                          // height: 248.h,
                          padding: EdgeInsets.symmetric(
                              horizontal: 14.w, vertical: 16.h),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                  width: 1, color: Color(0x19792AFF)),
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'เลขที่สัญญา',
                                        style: TextStyle(
                                          color: Color(0xFF1A1818),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        '',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: AppColors.AAMPurpleSolid,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'จำนวนเงินที่จะได้รับ',
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color!
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        '฿${aampayCtl.loan_amount.value.text}',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              SizedBox(
                                width: 299.w,
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'อัตราดอกเบี้ยลดต้นลดลดดอก/ปี',
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color!
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22,
                                      child: Text(
                                        '${aampayCtl.Fee!.value}%',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              SizedBox(
                                width: 299.w,
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'ค่าอากรแสตมป์',
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color!
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22,
                                      child: Text(
                                        '฿${aampayCtl.tax_contract!.value}',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              SizedBox(
                                width: 299.w,
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'ดอกเบี้ยโดยรวม',
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color!
                                              .withOpacity(0.5),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        '฿${f.format(int.parse(aampayCtl.interest_remain!.value.toString()))}',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              SizedBox(
                                width: 299.w,
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'ยอดชำระรายเดือน',
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        '฿${f.format(int.parse(aampayCtl.paypermonth!.value))} X${aampayCtl.selectedPeriod.value}',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: Color(0xFF995DFE),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Container(
                                width: 299.w,
                                decoration: ShapeDecoration(
                                  shape: RoundedRectangleBorder(
                                    side: BorderSide(
                                      width: 1,
                                      strokeAlign: BorderSide.strokeAlignCenter,
                                      color: Color(0x0C792AFF),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 10.h),
                              Container(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      width: 299.w,
                                      height: 22.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            height: 22.h,
                                            child: Text(
                                              'โอนเข้าบัญชี',
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium!
                                                    .color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontWeight,
                                              ),
                                            ),
                                          ),
                                          SizedBox(
                                            height: 22.h,
                                            child: Text(
                                              Get.find<KYCController>()
                                                      .bookbankData
                                                      .value
                                                      .bookbank_name ??
                                                  '',
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium!
                                                    .color,
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontWeight,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 2.h),
                                    SizedBox(
                                      width: 299.w,
                                      height: 22.h,
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Text(
                                            "${kycCtl.formatBankAccountNumber(Get.find<KYCController>().bookbankData.value.bookbank_number ?? '')} ${Get.find<KYCController>().bookbankData.value.bookbank_custName ?? ''}",
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontWeight,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                  // todo button ยืนยันทำรายการ
                  Container(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        PrimaryButton(
                          title: "ยืนยันคำขอ",
                          onPressed: () {
                            aampayCtl.confirmContractAndVerify(context);
                          },
                          buttonWidth: 327.w,
                          backgroundColor: AppColors.AAMPurpleSolid,
                          isActive: true,
                          textColor:
                              configTheme().buttonTheme.colorScheme!.onPrimary,
                        ),
                        SizedBox(height: 10.h),
                        PrimaryButton(
                          title: "ยกเลิก",
                          onPressed: () {},
                          buttonWidth: 327.w,
                          backgroundColor: AppColors.AAMPurpleSolid,
                          isActive: false,
                          textColor: configTheme().textTheme.bodyMedium!.color,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          });
        });
  }

  static buildUpdateInfo(context) {
    final KYCController kycCtl = Get.find<KYCController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: false,
        isDismissible: false,
        useRootNavigator: false,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<AAMPayController>(
              init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return WillPopScope(
              onWillPop: () async {
                // Prevents the back button from closing the bottom sheet
                return false;
              },
              child: Container(
                width: 375.w,
                height: 618.h,
                decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      height: 34.h,
                      alignment: Alignment.center,
                      child: SvgPicture.string(AppSvgImage.close_bar),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 24.w, right: 24.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                              width: 73.w,
                              height: 43.h,
                              child:
                                  SvgPicture.string(AppSvgImage.update_icon)),
                          SizedBox(height: 12.h),
                          Container(
                            width: 327.w,
                            height: 72.h,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(top: 4.h),
                                  width: 2.w,
                                  height: 42.h,
                                  alignment: Alignment.topCenter,
                                  decoration: BoxDecoration(
                                    color: AppColors.AAMPurple,
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                ),
                                SizedBox(width: 10.w),
                                Container(
                                  alignment: Alignment.center,
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                          text:
                                              'ข้อมูลนี้ ใช่คุณหรือไม่? ช่วยบอกเราหน่อย\nเพื่อความสะดวกต่อการรับสินเชื่อ เอเอเอ็ม เปย์\n',
                                          style: TextStyle(
                                            color: Color(0xFF1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .titleMedium!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .titleSmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .titleMedium!
                                                .fontWeight,
                                          ),
                                        ),
                                        TextSpan(
                                          text:
                                              'กรุณาตรวจสอบและยืนยันข้อมูล ด้านล่างนี้',
                                          style: TextStyle(
                                            color: Color(0x7F1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontWeight,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 12.h),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 14.w, vertical: 16.h),
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                    width: 1.w, color: Color(0x19792AFF)),
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: double.infinity,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: 52,
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Container(
                                                width: 20.w,
                                                height: 20.h,
                                                child: SvgPicture.string(
                                                    AppSvgImage.location_icon)),
                                            SizedBox(width: 6.w),
                                            Text(
                                              'ที่อยู่',
                                              style: TextStyle(
                                                color: Color(0xFF1A1818),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium!
                                                    .fontWeight,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 32.w),
                                      SizedBox(
                                        width: 161.w,
                                        child: Obx(() {
                                          return Text(
                                            "${aampayCtl.addressAms4!.value} หมู่ ${aampayCtl.mooAms4!.value} ต.${aampayCtl.sub_districtAms4!.value} อ.${aampayCtl.districtAms4!.value} จ.${aampayCtl.provinceAms4!.value}",
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: Color(0x7F1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontWeight,
                                            ),
                                            maxLines: 3,
                                          );
                                        }),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 12.h),
                                Divider(
                                  height: 1.h,
                                  thickness: 1,
                                  color: Color(0x0C792AFF),
                                ),
                                SizedBox(height: 12.h),
                                Container(
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                          width: 20.w,
                                          height: 20.h,
                                          child: SvgPicture.string(
                                              AppSvgImage.bookbank_icon)),
                                      SizedBox(width: 6.w),
                                      Text(
                                        'บัญชีธนาคาร',
                                        style: TextStyle(
                                          color: Color(0xFF1A1818),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 161.w,
                                        child: Text(
                                          '${kycCtl.bookbankData.value.bookbank_name}\n${kycCtl.formatBankAccountNumber(kycCtl.bookbankData.value.bookbank_number.toString())}\n${kycCtl.bookbankData.value.bookbank_custName}',
                                          textAlign: TextAlign.right,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            color: Color(0x7F1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontWeight,
                                          ),
                                          maxLines: 3,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 12.h),
                                Divider(
                                  height: 1.h,
                                  thickness: 1,
                                  color: Color(0x0C792AFF),
                                ),
                                SizedBox(height: 12.h),
                                Container(
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 18.w,
                                        height: 18.h,
                                        child: SvgPicture.string(
                                            AppSvgImage.moreInfo_icon),
                                      ),
                                      SizedBox(width: 8.w),
                                      SizedBox(
                                        child: Text(
                                          "กรณีแก้ไขข้อมูลบัญชีธนาคาร จะต้องผ่านการ\nตรวจสอบข้อมูลจากทางบริษัทก่อน ถึงจะสามารถ\nรับบริการสินเชื่อ เอเอเอ็ม เปย์ นี้ได้",
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                            color: Color(0xBF792AFF),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontWeight,
                                            height: 1.2.h,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    PrimaryButton(
                      title: "ยืนยันข้อมูลเดิม",
                      onPressed: () async {
                        var chk = await Get.find<AAMPayController>()
                            .updateOldAddress(context);
                        if (chk == true) {
                          Navigator.pop(context, true);
                        }
                      },
                      backgroundColor: AppColors.AAMPurpleSolid,
                      buttonWidth: 327.w,
                      isActive: true,
                      textColor: Colors.white,
                    ),
                    SizedBox(height: 10.h),
                    PrimaryButton(
                      title: "แก้ไขข้อมูล",
                      onPressed: () {
                        Navigator.pop(context, false);
                      },
                      backgroundColor: AppColors.AAMPurpleSolid,
                      buttonWidth: 327.w,
                      isActive: false,
                      textColor: Color(0xFF1A1818),
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }

  static buildVerifyOTP(context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        color: configTheme().textTheme.bodyMedium?.color,
        fontSize: 20,
        fontFamily: configTheme().primaryTextTheme.headlineLarge?.fontFamily,
        fontWeight: configTheme().primaryTextTheme.headlineLarge?.fontWeight,
        height: 0,
      ),
      decoration: const BoxDecoration(),
    );
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        barrierColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<AAMPayController>(
              init: Get.find<AAMPayController>(),
              builder: (aampayCtl) {
            return Container(
              width: 375.w,
              height: 706.h,
              margin: EdgeInsets.only(top: 106.h),
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                children: [
                  Container(
                    height: 34.h,
                    alignment: Alignment.center,
                    child: SvgPicture.string(AppSvgImage.close_bar),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Container(
                      height: 265.h,
                      margin: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 73.h,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text.rich(
                                        TextSpan(
                                          children: [
                                            TextSpan(
                                              text: 'ยืนยันรหัส OTP\n',
                                              style: TextStyle(
                                                color: Color(0xFF1A1818),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .titleLarge!
                                                    .fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .titleLarge!
                                                    .fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .titleLarge!
                                                    .fontWeight,
                                              ),
                                            ),
                                            TextSpan(
                                              text:
                                                  'เพื่อยืนยันการทำการขอสินเชื่อ',
                                              style: TextStyle(
                                                color: Color(0xFF1A1818),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodySmall!
                                                    .fontWeight,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        child: Text(
                                          'เราได้ส่งรหัส 6 หลักไปที่ ${Get.find<ProfileController>().profile.value.phoneFirebase}',
                                          style: TextStyle(
                                            color: Color(0x7F1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .labelSmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .labelSmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .labelSmall!
                                                .fontWeight,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 14.h,
                          ),
                          Container(
                            width: 255.w,
                            height: 78.h,
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 242.w,
                                  height: 30.h,
                                  child: Pinput(
                                    androidSmsAutofillMethod:
                                        AndroidSmsAutofillMethod
                                            .smsRetrieverApi,
                                    pinAnimationType: PinAnimationType.scale,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    defaultPinTheme: defaultPinTheme,
                                    length: 6,
                                    controller: aampayCtl.otpController.value,
                                    // focusNode: focusNode,
                                    listenForMultipleSmsOnAndroid: true,
                                    showCursor: false,
                                    closeKeyboardWhenCompleted: true,
                                    animationCurve: Curves.easeInOut,
                                    separatorBuilder: (index) =>
                                        const SizedBox(width: 22),
                                    hapticFeedbackType:
                                        HapticFeedbackType.vibrate,
                                    onCompleted: (pin) async {
                                      print("YourCodeOTP = $pin");
                                    },
                                    onChanged: (value) {
                                      if (value.length == 6) {
                                        aampayCtl.checkOtpFormat(
                                            context, value);
                                      } else {
                                        aampayCtl.isAlert!.value = false;
                                        aampayCtl.setVerify(false);
                                        aampayCtl.update();
                                      }
                                    },
                                  ),
                                ),
                                SizedBox(
                                    width: 254.w,
                                    child: Image.asset(
                                      aampayCtl.isAlert!.value == true &&
                                              aampayCtl.otpController.value.text
                                                      .length ==
                                                  6 &&
                                              aampayCtl.counter_OTP.value
                                                      .toString() !=
                                                  '0'
                                          ? 'assets/register/icon/OTP_error.png'
                                          : aampayCtl.otpController.value.text
                                                      .isNotEmpty &&
                                                  aampayCtl.counter_OTP.value
                                                          .toString() !=
                                                      '0'
                                              ? 'assets/register/icon/OTP_success.png'
                                              : 'assets/register/icon/OTP_defalt.png',
                                      fit: BoxFit.fill,
                                      color: aampayCtl.otpController.value.text
                                                  .isNotEmpty &&
                                              appConfigService.countryConfigCollection
                                                      .toString() ==
                                                  'rafco' &&
                                              aampayCtl.isAlert!.value == false
                                          ? const Color(0xFF22409A)
                                          : aampayCtl.otpController.value.text
                                                      .isNotEmpty &&
                                                  appConfigService
                                                          .countryConfigCollection
                                                          .toString() ==
                                                      'rplc' &&
                                                  aampayCtl.isAlert!.value ==
                                                      false
                                              ? const Color(0xFF6A7165)
                                              : null,
                                    )),
                                SizedBox(
                                  height: 24.h,
                                ),
                                Obx(() {
                                  return aampayCtl.isVerify!.value == true &&
                                          aampayCtl.counter_OTP.value
                                                  .toString() !=
                                              '0'
                                      ? Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              width: 18.w,
                                              height: 18.h,
                                              alignment: Alignment.center,
                                              // margin: EdgeInsets.only(
                                              //     top: 30.h, bottom: 30.h),
                                              child: Center(
                                                child:
                                                    CircularProgressIndicator(
                                                  backgroundColor: configTheme()
                                                      .colorScheme
                                                      .secondary,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                              Color>(
                                                          configTheme()
                                                              .colorScheme
                                                              .primary
                                                              .withOpacity(
                                                                  1.0)),
                                                  strokeWidth: 1.0,
                                                ),
                                              ),
                                            ),
                                          ],
                                        )
                                      : Container();
                                })
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 14.h,
                          ),
                          Container(
                              height: 26.h,
                              child: Obx(() {
                                if (aampayCtl.isAlert!.value == true &&
                                    aampayCtl.otpController.value.text.length ==
                                        6 &&
                                    aampayCtl.counter_OTP.value.toString() !=
                                        '0') {
                                  return Container(
                                    height: 20.h,
                                    alignment: Alignment.center,
                                    margin: EdgeInsets.only(
                                        top: 30.h, bottom: 24.h),
                                    child: Center(
                                      child: Text(
                                        signUpCilkRefCodeAgain.tr,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color:
                                              configTheme().colorScheme.onError,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                          height: 0.14.h,
                                        ),
                                      ),
                                    ),
                                  );
                                } else if (aampayCtl.counter_OTP.value
                                        .toString() !=
                                    '0') {
                                  return Container(
                                    alignment: Alignment.center,
                                    child: Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                            text:
                                                '${signUpRefCode.tr} ${aampayCtl.refCode!.value} ',
                                            style: TextStyle(
                                              color: const Color(0xFF1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontWeight,
                                              height: 0.18.h,
                                            ),
                                          ),
                                          TextSpan(
                                            text: "ยืนยันรหัสภายใน ",
                                            style: TextStyle(
                                              color: const Color(0x7F1A1818),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelSmall
                                                  ?.fontWeight,
                                              height: 0.18.h,
                                            ),
                                          ),
                                          TextSpan(
                                            text: aampayCtl.counter_OTP.value
                                                .toString(),
                                            style: TextStyle(
                                              color: appConfigService
                                                          .countryConfigCollection
                                                          .toString() ==
                                                      'aam'
                                                  ? const Color(0xFFFF9300)
                                                  : appConfigService
                                                              .countryConfigCollection
                                                              .toString() ==
                                                          'rafco'
                                                      ? const Color(0xFFEA1B23)
                                                      : const Color(0xFFFFC20E),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .labelLarge
                                                  ?.fontWeight,
                                              height: 0.18.h,
                                            ),
                                          ),
                                        ],
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  );
                                } else {
                                  return Text(
                                    signUpRefCodeError.tr,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontWeight,
                                    ),
                                  );
                                }
                              })),
                          SizedBox(
                            height: 14.h,
                          ),
                          GestureDetector(
                            onTap: () {
                              //TODO : ส่ง OTP ไปใหม่
                              print("Resend OTP");
                              aampayCtl.checkResendOTPConfig(context);
                            },
                            child: Container(
                              width: 105.w,
                              height: 46.h,
                              decoration: ShapeDecoration(
                                color: aampayCtl.counter_OTP.value == 0
                                    ? configTheme().colorScheme.primary
                                    : const Color(0x0C1A1818),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  // 'รับรหัสอีกครั้ง',
                                  signUpResend.tr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: aampayCtl.counter_OTP.value == 0
                                        ? Colors.white
                                        : const Color(0x331A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelMedium
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelMedium
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelMedium
                                        ?.fontWeight,
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      )),
                ],
              ),
            );
          });
        });
  }

  static buildStatusCreateContract(context) {
    // final AAMPayController aampayCtl = Get.find<AAMPayController>();
    return GetBuilder<AAMPayController>(
        init: Get.find<AAMPayController>(),
        builder: (aampayCtl) {
      return Container(
        width: 327.w,
        height: 112.h,
        padding: const EdgeInsets.only(
          top: 6,
          left: 16,
          right: 16,
          bottom: 10,
        ),
        decoration: ShapeDecoration(
          color: Color(0x14FF9300),
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 0.80, color: Color(0x33FF9300)),
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              height: 35.h,
              child: Row(
                children: [
                  SizedBox(
                      width: 38.17.w,
                      height: 28.h,
                      child: SvgPicture.string(AppSvgImage.aampay_mascot)),
                  SizedBox(
                    width: 9.w,
                  ),
                  Container(
                    alignment: Alignment.center,
                    height: 28.h,
                    child: Text(
                      'สถานะคำขอสินเชื่อ',
                      style: TextStyle(
                        color: Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                      ),
                    ),
                  )
                ],
              ),
            ),
            Divider(
              height: 0.80.w,
              color: Color(0x33FF9300),
            ),
            SizedBox(height: 10.h),
            Container(
              width: 295.w,
              height: 42.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 42.w,
                          height: 42.h,
                          child: SvgPicture.string(AppSvgImage.aampay_icon),
                        ),
                        SizedBox(width: 8.w),
                        SizedBox(
                          height: 42.h,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            // จัดชิดซ้ายเหมือน Text.rich เดิม
                            children: [
                              Text(
                                'เอเอเอ็ม เปย์',
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium!
                                      .fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium!
                                      .fontWeight,
                                ),
                              ),
                              Obx(() {
                                if (aampayCtl.aampay_ctt_code.value.isEmpty) {
                                  return AnimatedShimmer(width: 100.w);
                                }
                                return Text(
                                  aampayCtl.aampay_ctt_code.value,
                                  style: TextStyle(
                                    color: Color(0x7F1A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .labelSmall!
                                        .fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall!
                                        .fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall!
                                        .fontWeight,
                                  ),
                                );
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: ShapeDecoration(
                          color: Color(0x14792AFF),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8)),
                        ),
                        child: Text(
                          aampayCtl.aampay_contract_status!.value == "pending"
                              ? "กำลังสร้างสัญญา"
                              : aampayCtl.aampay_contract_status!.value ==
                                      "tranfer"
                                  ? "รอทำการโอน"
                                  : aampayCtl.aampay_contract_status!.value ==
                                          "completed"
                                      ? "เสร็จสิ้น"
                                      : "",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Color(0xBF792AFF),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      aampayCtl.aampay_contract_status!.value == "completed"
                          ? SizedBox(width: 8.w)
                          : SizedBox(width: 0),
                      aampayCtl.aampay_contract_status!.value == "completed"
                          ? GestureDetector(
                              onTap: () async {
                                // TODO ไปหน้าสินเชื่อของฉัน
                                print('ไปหน้าสินเชื่อของฉัน');
                                // Get.lazyPut(() =>
                                //     ContractListController()
                                //         .customerContactList(), fenix: true);
                                //set ปิด widget กำลังสร้างสัญญานี้
                                await aampayCtl.resetData();
                                Get.to(() => const MyloanDetailScreen());
                              },
                              child: Container(
                                width: 30.w,
                                height: 30.h,
                                padding: EdgeInsets.all(3),
                                child: SvgPicture.string(
                                    AppSvgImage.see_more_icon),
                              ),
                            )
                          : SizedBox(width: 0),
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      );
    });
  }

  static buildViewPeriodsAAMPayContract(context) {
    var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return GetBuilder<MyloanController>(builder: (myloanCtl) {
            return Container(
              width: 375.w,
              // height: 519.h,
              decoration: ShapeDecoration(
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    topRight: Radius.circular(20.r),
                  ),
                ),
                shadows: [
                  BoxShadow(
                    color: Color(0x33000000),
                    blurRadius: 50,
                    offset: Offset(0, 4),
                    spreadRadius: 0,
                  )
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                      width: 375.w,
                      height: 34.h,
                      child: SvgPicture.string(AppSvgImage.close_bar)),
                  SizedBox(height: 16.h),
                  Container(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 327.w,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 23.h,
                                child: Text(
                                  'รายละเอียดค่างวด',
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .titleLarge!
                                        .fontWeight,
                                  ),
                                ),
                              ),
                              //todo icon
                              SizedBox(
                                height: 43.h,
                                child: SvgPicture.string(
                                    AppSvgImage.mascot_aampay),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 327.w,
                          height: 248.h,
                          padding: EdgeInsets.symmetric(
                              horizontal: 14.w, vertical: 16.h),
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                  width: 1, color: Color(0x19792AFF)),
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'เลขที่สัญญา',
                                        style: TextStyle(
                                          color: Color(0xFF1A1818),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        myloanCtl.selectedLoan!.value.toString(),
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: AppColors.AAMPurpleSolid,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: 22.h,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        'จำนวนค่างวดทั้งหมด',
                                        style: TextStyle(
                                          color: Color(0xFF1A1818),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      height: 22.h,
                                      child: Text(
                                        '${myloanCtl
                                            .nextpaydigilist!
                                            .length} งวด',
                                        textAlign: TextAlign.right,
                                        style: TextStyle(
                                          color: AppColors.AAMPurpleSolid,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall!
                                              .fontWeight,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10.h),
                              //List
                              SizedBox(
                                height: 100.h,
                                child: ListView.builder(
                                  itemCount: myloanCtl
                                      .nextpaydigilist!
                                      .length, // เปลี่ยนเป็นจำนวนที่ต้องการ
                                  itemBuilder: (context, index) {
                                    return Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        SizedBox(
                                          // width: 299.w,
                                          height: 22.h,
                                          child: Text(
                                            'งวดที่ ${index + 1} ${myloanCtl.due_datedigilist![index].toString()}',
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color!
                                                  .withOpacity(0.5),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontWeight,
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          // width: 100.w,
                                          height: 22.h,
                                          child: Text(
                                            '฿ ${myloanCtl.nextpaydigilist![index].toString()}',
                                            // '฿ ${f.format(1000)}',
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium!
                                                  .color!
                                                  .withOpacity(0.5),
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodySmall!
                                                  .fontWeight,
                                            ),
                                          ),
                                        )
                                      ],
                                    );
                                  },
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Container(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        PrimaryButton(
                          title: "ตกลง",
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          buttonWidth: 327.w,
                          backgroundColor: AppColors.AAMPurpleSolid,
                          isActive: true,
                          textColor:
                              configTheme().buttonTheme.colorScheme!.onPrimary,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 48.h,
                  )
                ],
              ),
            );
          });
        });
  }

  static buildViewPeriodsAAMPayArCard(context,periods,installment) {
    var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
    print(installment.length);
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: 375.w,
            // height: 519.h,
            decoration: ShapeDecoration(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
              ),
              shadows: [
                BoxShadow(
                  color: Color(0x33000000),
                  blurRadius: 50,
                  offset: Offset(0, 4),
                  spreadRadius: 0,
                )
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 375.w,
                    height: 34.h,
                    child: SvgPicture.string(AppSvgImage.close_bar)),
                SizedBox(height: 16.h),
                Container(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 327.w,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 23.h,
                              child: Text(
                                'รายละเอียดค่างวด',
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleLarge!
                                      .fontWeight,
                                ),
                              ),
                            ),
                            //todo icon
                            SizedBox(
                              height: 43.h,
                              child: SvgPicture.string(
                                  AppSvgImage.mascot_aampay),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        width: 327.w,
                        // height: 248.h,
                        padding: EdgeInsets.symmetric(
                            horizontal: 14.w, vertical: 16.h),
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                                width: 1, color: Color(0x19792AFF)),
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 22.h,
                              child: Row(
                                mainAxisAlignment:
                                MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    height: 22.h,
                                    child: Text(
                                      'จำนวนค่างวดทั้งหมด',
                                      style: TextStyle(
                                        color: Color(0xFF1A1818),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium!
                                            .fontWeight,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 22.h,
                                    child: Text(
                                      '${periods.toString()} งวด',
                                      textAlign: TextAlign.right,
                                      style: TextStyle(
                                        color: AppColors.AAMPurpleSolid,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall!
                                            .fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall!
                                            .fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall!
                                            .fontWeight,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(height: 10.h),
                            //List
                            SizedBox(
                              height: 100.h,
                              child: ListView.builder(
                                itemCount: installment.length, // เปลี่ยนเป็นจำนวนที่ต้องการ
                                itemBuilder: (context, index) {
                                  return Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        // width: 299.w,
                                        height: 22.h,
                                        child: Text(
                                          'งวดที่ ${index + 1}',
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium!
                                                .color!
                                                .withOpacity(0.5),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontWeight,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        // width: 100.w,
                                        height: 22.h,
                                        child: Text(
                                          '฿ ${installment[index].toString()}',
                                          // '฿ ${f.format(1000)}',
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium!
                                                .color!
                                                .withOpacity(0.5),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall!
                                                .fontWeight,
                                          ),
                                        ),
                                      )
                                    ],
                                  );
                                },
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16.h),
                Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      PrimaryButton(
                        title: "ตกลง",
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        buttonWidth: 327.w,
                        backgroundColor: AppColors.AAMPurpleSolid,
                        isActive: true,
                        textColor:
                        configTheme().buttonTheme.colorScheme!.onPrimary,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 48.h,
                )
              ],
            ),
          );
        });
  }
}
