import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:AAMG/controller/steam.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/AppLoading.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart' as http;
import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/notification/notify.controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:url_launcher/url_launcher.dart';

class WebViewTelegram extends StatefulWidget {
  const WebViewTelegram({super.key});

  @override
  State<WebViewTelegram> createState() => _WebViewTelegramState();
}

class _WebViewTelegramState extends State<WebViewTelegram> {
  ChatInAppController chatInAppCtl = Get.put(ChatInAppController());
  NotifyController notifyCtl = Get.put(NotifyController());
  final GlobalKey webViewKey = GlobalKey();

  var clickCount = 0;
  var clickCount2 = 0;
  bool checkStopLoading = false;
  // final storage = FlutterSecureStorage();
  final box = GetStorage();
  late PullToRefreshController pullToRefreshController;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        useShouldOverrideUrlLoading: true,
        clearCache: true,
        mediaPlaybackRequiresUserGesture: true,
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
      ),
      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));
  String url = "";
  InAppWebViewController? webViewController;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    pullToRefreshController = PullToRefreshController(
      options: PullToRefreshOptions(
        color: Colors.yellow,
      ),
      onRefresh: () async {
        if (Platform.isAndroid) {
          webViewController?.reload();
        } else if (Platform.isIOS) {
          webViewController?.loadUrl(
              urlRequest: URLRequest(url: await webViewController?.getUrl()));
        }
      },
    );
    // print("WebViewTelegramState");
    // print(chatInAppCtl.url.value);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          InAppWebView(
            onLoadStart: (controller, url) async {
              chatInAppCtl.webViewCtrl = controller;
              // chatInAppCtl.approveOpenUI(true);
              // print("onLoadStart");
              coveredUI(context);
              chatInAppCtl.checkUse(context);
            },
            onLoadStop: (controller, url) async {
              // print("onLoadStop");
              // print(url);
              if (url.toString() ==
                  "https://telegramserv.agilesoftgroup.com/") {
                if (chatInAppCtl.chkAlertGroup == false) {
                  showWaitingGroupAlert(context);
                }
                await Future.delayed(const Duration(seconds: 10));
                chatInAppCtl.changeLoginAutoFill();
                clickCount++;
                if (clickCount == 1) {
                  var step0 = await controller.evaluateJavascript(
                      source:
                          'var button = document.querySelector("#auth-phone-number-form > div > form > button.Button.default.primary.text.has-ripple"); console.log("click1"); button.click()');
                }
              } else {
                chatInAppCtl.chkAlertGroup = false;
                chatInAppCtl.update();
                // if(!checkStopLoading){
                //
                //   setState(() {
                //     checkStopLoading = true;
                //     // chatInAppCtl.chkAlertGroup = false;
                //   });

                // Loader.hide(context);
                // Navigator.pop(context);
              }

              // }
            },
            onConsoleMessage: (controller, consoleMessage) async {
              // print(consoleMessage.message);
              if (consoleMessage.message == "click1") {
                await Future.delayed(const Duration(seconds: 6));
                clickCount2++;
                if (clickCount2 == 1) {
                  var step1 = await controller.evaluateJavascript(
                      source:
                          'var token = document.getElementsByClassName("qr-container_token")[0].value; console.log("qrToken " + token);');
                }
              }
              if (consoleMessage.message.startsWith("qrToken ")) {
                chatInAppCtl.token.value = consoleMessage.message.substring(8);
                // box.write("localChatButGST", "true");
                //TODO แจ้งเตือน
                // coveredUI();
                // showWaitingGroupAlert(context);
                // print('https://telegram.agilesoftgroup.com/loginViaQR_RPLC');
                final response = await http.post(
                  Uri.parse(
                      'https://telegram.agilesoftgroup.com/${chatInAppCtl.checkLoginPath.value}'),
                  body: {
                    'phone': chatInAppCtl.phoneCode.value,
                    'base64': "",
                    "QRtoken": chatInAppCtl.token.value
                  },
                );
                var data = jsonDecode(response.body);
                if (data['status'] == "success") {
                  chatInAppCtl.changeLoginAutoFill();
                  if (chatInAppCtl.useCount == 0) {
                    await Future.delayed(const Duration(seconds: 2));
                    setState(() async {
                      chatInAppCtl.useCount = '1';
                      chatInAppCtl.box.write("useCount", '1');
                      // AppLoader.dismiss(context);
                    });
                  }
                } else {
                  Get.snackbar(chatInAppError.tr, chatInAppReTryRegister.tr,
                      snackPosition: SnackPosition.BOTTOM);
                  box.write("localChatButGST", "true");
                  Get.back();
                  chatInAppCtl.fullProcessRegister(context);
                }
              }
            },
            key: webViewKey,
            pullToRefreshController: pullToRefreshController,
            initialUrlRequest:
                URLRequest(url: Uri.parse(chatInAppCtl.url.value)),
            initialOptions: options,
            androidOnPermissionRequest: (controller, origin, resources) async {
              return PermissionRequestResponse(
                  resources: resources,
                  action: PermissionRequestResponseAction.GRANT);
            },
            onLoadError: (controller, url, code, message) {
              pullToRefreshController.endRefreshing();
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              var uri = navigationAction.request.url!;

              if (![
                "http",
                "https",
                "file",
                "chrome",
                "data",
                "javascript",
                "about"
              ].contains(uri.scheme)) {
                if (await canLaunch(url)) {
                  // Launch the App
                  await launch(
                    url,
                  );
                  // and cancel the request
                  return NavigationActionPolicy.CANCEL;
                }
              }

              return NavigationActionPolicy.ALLOW;
            },
          ),
          Container(
            width: Get.width,
            height: 104,
            color: Colors.white,
            alignment: Alignment.bottomCenter,
            padding:
                const EdgeInsets.only(top: 20, right: 20, left: 20, bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    Get.back();
                  },
                  child: Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          topLeft: Radius.circular(15),
                          bottomRight: Radius.circular(15),
                          bottomLeft: Radius.circular(15),
                        ),
                        border: Border.all(
                            width: 1, color: const Color(0xFFE8E6E2))),
                    child:  Icon(
                      Icons.arrow_back_ios_new,
                      size: 18,
                      color: configTheme().colorScheme.onSecondary,
                    ),
                  ),
                ),
                 Row(
                  children: [
                    // Icon(Icons.chat_outlined),
                    appConfigService.countryConfigCollection=="aam"?
                    Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.purpleAccent.withOpacity(0.7),
                        image: DecorationImage(
                          image: AssetImage('assets/app_logo/aam/LogoAAM.png'),
                          // fit: BoxFit.cover,
                        ),
                      ),
                    )
                    // Image.asset(
                    //   'assets/app_logo/aam/LogoAAM.png',
                    //   width: 30,
                    // )
                        :appConfigService.countryConfigCollection=="rafco"?
                        Container(
                          height: 30.h,
                          width: 30.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.redAccent,
                            image: DecorationImage(
                              // image: AssetImage('assets/app_logo/rplc/rplc_logo_text.png'),
                              image: AssetImage('assets/app_logo/rafco/Rafcologo.png'),
                              // fit: BoxFit.cover,
                            ),
                          ),
                        )
                    // Image.asset(
                    //   'assets/app_logo/rafco/Rafcologo.png',
                    //   // width: 30,
                    // )
                        : Container(
                      height: 30.h,
                      width: 30.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.yellowAccent.withOpacity(0.3),
                        image: DecorationImage(
                          image: AssetImage('assets/app_logo/rplc/rplc_logo_text.png'),
                          // image: AssetImage('assets/app_logo/rafco/Rafcologo.png'),
                          // fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    // AppWidget.boldText(context, "พูดคุยกับประชากิจฯ", 18,
                    //     const Color(0xFF282828), FontWeight.w400),
                  ],
                ),
                const SizedBox(
                  width: 36,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void coveredUI(context) async {
    // var i = await storage.read(key: SecureData.localChat);
    var i = await box.read("localChatButGST");
    // print("coveredUI");
    // print(i);
    if (i == "true" || i == null) {
      box.write("localChatButGST", "true");
      print("เปิด");
      final timer = Timer(const Duration(seconds: 17), () {
        print("ปิด timer");
        Navigator.pop(context);
        // Get.back(); // ปิด dialog หลัง 20 วินาที
      });
      showDialog(
        context: context,
        useSafeArea: false,
        builder: (_) => const popupCoveredUI(),
      ).then((value) {
        timer.cancel(); // ยกเลิกตัวจับเวลาเมื่อปิด dialog
      });
    }
  }

  void showWaitingGroupAlert(BuildContext context) async {
    GetStorage().write("chkAlertGroup", true);
    chatInAppCtl.chkAlertGroup = true;
    chatInAppCtl.update();


    var box = GetStorage();
    var isLoadingChat = box.read("isLoadingChat");

    if (isLoadingChat == true) {
      print("เคย isLoadingChat แล้ว");

      var timer = Timer(const Duration(seconds: 10), () {
        Navigator.pop(context); // ปิด dialog หลัง 5 วินาที
      });

      showDialog(
        context: context,
        useSafeArea: false,
        builder: (_) => const WaitingGroup(),
      ).then((value) {
        timer.cancel(); // ยกเลิกตัวจับเวลาเมื่อปิด dialog
        var box = GetStorage();
        box.write("isLoadingChat", true);
      });
    } else {
      print("ยังไม่เคย isLoadingChat ");
      var timer = Timer(const Duration(seconds: 15), () {
        Navigator.pop(context); // ปิด dialog หลัง 15 วินาที
      });

      // แสดง dialog แจ้งเตือน
      showDialog(
        context: context,
        useSafeArea: false,
        builder: (_) => const WaitingGroup(),
      ).then((value) {
        timer.cancel(); // ยกเลิกตัวจับเวลาเมื่อปิด dialog
        var box = GetStorage();
        box.write("isLoadingChat", true);
      });
    }
  }
}

class popupCoveredUI extends StatefulWidget {
  const popupCoveredUI({Key? key}) : super(key: key);

  @override
  State<popupCoveredUI> createState() => _popupCoveredUIState();
}

class _popupCoveredUIState extends State<popupCoveredUI> {
  final steamControll = Get.find<SteamEvent>();
  ChatInAppController chatInAppCtl = Get.put(ChatInAppController());
  Stream? stream;

  // final storage = FlutterSecureStorage();
  final box = GetStorage();

  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      startListening();
    });
    // print('homeState');
  }

  void startListening() {
    bool check = steamControll.checkStarter(steamControll.changeStateChatInApp);
    if (!check) {
      stream = steamControll.startListening(steamControll.changeStateChatInApp);
    }

    stream?.listen((value) async {
      // print('activate event');
      // print(value);
      var i = await box.read("localChatButGST");
      // print(i);
      if (box.read("localChatButGST").toString() == "true") {
        print("ปิด");
        box.write("localChatButGST", "false");
        // Navigator.pop(context);

        // Get.back();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     Get.back();
      //   },
      //   child: const Icon(Icons.add),
      // ),
      body: Container(
        color: const Color(0xFFFAF7EC),
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: const Column(
          // alignment: Alignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // S.h(1.h),
            // Column(
            //   mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     Center(
            //       child: Hero(
            //           tag: "myLogo",
            //           child: Image.asset(
            //             'assets/images/intro/logoLonding.png',
            //             width: 155.w,
            //             fit: BoxFit.fill,
            //           )),
            //     ),
            //   ],
            // ),
            // Stack(
            //   alignment: Alignment.center,
            //   children: [
            //     Container(
            //       height: 200.h,
            //       width: MediaQuery.of(context).size.width,
            //       alignment: Alignment.bottomCenter,
            //       child: Lottie.asset(
            //         'assets/images/intro/loading_rplc2.json',
            //         height: 200.h,
            //       ),
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }
}

class WaitingGroup extends StatefulWidget {
  const WaitingGroup({Key? key}) : super(key: key);
  @override
  State<WaitingGroup> createState() => _WaitingGroupState();
}

class _WaitingGroupState extends State<WaitingGroup> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        // floatingActionButton: FloatingActionButton(
        //   onPressed: () {
        //     Get.back();
        //   },
        //   child: const Icon(Icons.add),
        // ),
        body: Stack(
      alignment: Alignment.center,
      children: [
        Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            decoration: BoxDecoration(
                gradient:
                appConfigService.countryConfigCollection.toString() == 'aam'
                    ? AppColorsGradient.appBgAAMGradient
                    : appConfigService.countryConfigCollection.toString() ==
                    'rafco'
                    ? AppColorsGradient.appBgRAFCOGradient
                    : AppColorsGradient.appBgRPLCGradient
            )),
        // Positioned(
        //   bottom: 20.h,
        //   child: SvgPicture.asset('assets/images/SplashScreen/bg_loading.svg'),
        // ),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
              ),
            ),
            // Center(
            //   child: Lottie.asset(
            //     'assets/gif/logo_rafco_animate.json',
            //     height: 500.h,
            //   ),
            // ),
            // SizedBox(
            //   width: 100,
            //   child: Center(
            //     child: LoadingIndicator(
            //       indicatorType: Indicator.ballTrianglePathColored,
            //       colors: [
            //         Color(0xFFFAEBD7),
            //         Color(0xFFFFDAB9),
            //         Color(0xFFFFEBCD),
            //       ],
            //     ),
            //   ),
            // ),
            const SizedBox(
              height: 40,
            ),
            Text(
              chatInAppPleaseWait.tr,
              style: const TextStyle(fontSize: 20, color: Colors.white),
            ),
            // SizedBox(
            //   height: 20,
            // ),
          ],
        ),
        // Positioned(
        //     left: 30,
        //     top: 80,
        //     child: InkWell(
        //       onTap: () {
        //         print("close");
        //         Get.offAll(() => HomePage());
        //         setState(() {
        //           Get.find<ChatInAppController>().chkAlertGroup = true;
        //         });
        //         // Navigator.of(context).pop();
        //       },
        //       child: Container(
        //         height: 40,
        //         width: 40,
        //         decoration: BoxDecoration(
        //           color: Colors.white.withOpacity(0.6),
        //           borderRadius: BorderRadius.circular(20),
        //         ),
        //         child: Center(
        //           child: Icon(
        //             Icons.house,
        //             color: Colors.black.withOpacity(0.5),
        //             size: 20,
        //           ),
        //         ),
        //       ),
        //     ))
      ],
    ));
  }
}
