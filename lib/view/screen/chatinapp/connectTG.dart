import 'package:AAMG/controller/chatinapp/chatinapp.controller.dart';
import 'package:AAMG/controller/chatinapp/register.TG.controller.dart';
import 'package:AAMG/controller/register/registerVerify.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';

class ConnectTG extends StatefulWidget {
  const ConnectTG({super.key});

  @override
  State<ConnectTG> createState() => _ConnectTGState();
}

class _ConnectTGState extends State<ConnectTG> {
  final TextEditingController _pinControllerTG = TextEditingController();
  final TextEditingController _tgPinController = TextEditingController();

  final bool _solidEnable = false;
  // final ColorBuilder _solidColor = PinListenColorBuilder(Colors.grey, Colors.grey);

  RegisterTGController rTGController = Get.put(RegisterTGController());
  final RegisterVerifyController regisVerifyCtl =
      Get.put(RegisterVerifyController());

  bool countResend = false;
  bool stateOTP = true;
  bool email = true;
  final FocusNode focusNode = FocusNode();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    stateOTP = true;
    email = true;
  }

  @override
  Widget build(BuildContext context) {
    final defaultPinTheme = PinTheme(
      width: 56,
      height: 56,
      textStyle: TextStyle(
        color: Color(0xFF1A1818),
        fontSize: 20,
        fontFamily: 'NotoSansThai',
        fontWeight: FontWeight.w400,
        height: 0,
      ),
      decoration: const BoxDecoration(),
    );
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        floatingActionButton: FloatingActionButton(
          backgroundColor: configTheme().colorScheme.onSecondary,
          onPressed: () {
            Get.back();
          },
          child: const Icon(Icons.arrow_back),
        ),
        body: Container(
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
              gradient:
                  appConfigService.countryConfigCollection.toString() == 'aam'
                      ? AppColorsGradient.appBgAAMGradient
                      : appConfigService.countryConfigCollection.toString() ==
                              'rafco'
                          ? AppColorsGradient.appBgRAFCOGradient
                          : AppColorsGradient.appBgRPLCGradient),
          child:
              stateOTP
                  ? Padding(
                      padding: EdgeInsets.only(top: 100.h, left: 20.w, right: 20.w),
                      child: Container(
                        // height: 200.h,
                        // color: Colors.teal,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              chatInAppEnterOtp.tr,
                              style: TextStyle(fontSize: 16, color: Colors.black),
                              // style: TextStyleTheme.text21noToSanRegBlack(context),
                            ),
                            Text(
                              chatInAppOtp.tr +
                                  rTGController.phone.toString().substring(
                                      6, rTGController.phone.toString().length),
                              style: TextStyle(fontSize: 14, color: Colors.black),
                              // style: TextStyleTheme.text14ToSanRegBlack(context),
                            ),
                            Padding(
                              padding:  EdgeInsets.only(top: 20.h,left: 40.w,right: 40.w),
                              child: Column(
                                children: [
                                  SizedBox(
                                    // color: Colors.brown,
                                    width: 242.w,
                                    height: 30.h,
                                    child: GetBuilder<RegisterTGController>(
                                      builder: (controller) {
                                        return Pinput(
                                          androidSmsAutofillMethod:
                                              AndroidSmsAutofillMethod.smsRetrieverApi,
                                          pinAnimationType: PinAnimationType.scale,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.digitsOnly
                                          ],
                                          defaultPinTheme: defaultPinTheme,
                                          // preFilledWidget: preFilledWidget,
                                          length: 5,
                                          controller: _pinControllerTG,
                                          focusNode: focusNode,
                                          listenForMultipleSmsOnAndroid: true,
                                          showCursor: false,
                                          closeKeyboardWhenCompleted: true,
                                          animationCurve: Curves.easeInOut,
                                          separatorBuilder: (index) => SizedBox(width: 22),
                                          hapticFeedbackType: HapticFeedbackType.vibrate,
                                          onCompleted: (pin) async {
                                            print("YourCodeOTP = $pin");
                                          },
                                          onChanged: (value) async {
                                            print("YourCodeOTP = $value");
                                            if (value.length == 5) {
                                              ///แก้อันนี้ไปนะ

                                              String sendData =
                                                  _pinControllerTG.text.toString();

                                              var check =
                                                  await rTGController.verifyOTP_TG_FromHome(
                                                      "otp", sendData, context);

                                              if (check == "2step") {
                                                stateOTP = false;
                                                email = false;
                                                Future.delayed(Duration(microseconds: 750),
                                                    () {
                                                  _pinControllerTG.clear();
                                                  setState(() {});
                                                });
                                              } else if (check == "email") {
                                                stateOTP = false;
                                                email = false;
                                                Future.delayed(Duration(microseconds: 750),
                                                    () {
                                                  _pinControllerTG.clear();
                                                  setState(() {});
                                                });
                                              } else if (check == "false") {
                                                Future.delayed(Duration(microseconds: 500),
                                                    () {
                                                  _pinControllerTG.clear();
                                                });

                                                /// resend register TG

                                                // if(countResend){
                                                //   Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => HomePage()));
                                                //
                                                //   showDialog(
                                                //     context: context,
                                                //     useSafeArea: false,
                                                //     builder: (BuildContext context) => CupertinoAlertDialog(
                                                //       title: Text("drawer_notifications".tr),
                                                //       content: Text("notiBanned".tr),
                                                //       actions: [
                                                //         CupertinoDialogAction(
                                                //           isDefaultAction: true,
                                                //           child: Text("ok".tr),
                                                //           onPressed: () {
                                                //             Navigator.pop(context);
                                                //           },
                                                //         ),
                                                //       ],
                                                //     ),
                                                //   );
                                                // }

                                                // bool forCout = await rTGController.resendRegister();
                                                //
                                                // if(forCout == true){
                                                //   countResend = true;
                                                // }else{
                                                //
                                                // }
                                              }
                                            }
                                            regisVerifyCtl.checkOtpFormat(context,value);
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  Container(
                                    height: 1.h, // Adjust height as needed
                                    width: Get.width,
                                    // color: Colors.red, // Optional for debugging
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: 5,
                                      itemBuilder: (context, index) {
                                        return Row(
                                          children: [
                                            Container(
                                                width: 30.w, // Adjust width as needed
                                                height: 1.h,
                                                color: Colors.white
                                            ),
                                            SizedBox(width: 22.w),
                                          ],
                                        );
                                      },
                                    ),
                                    // _buildResendOTP()
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  /// email
                  :
              email
                  ? Padding(
                      padding:
                          EdgeInsets.only(top: 100.h, left: 20.w, right: 20.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          // _buildBackButton(),
                          // _buildHeaderText(),
                          Text(chatInAppEmail.tr,
                              style: TextStyle(
                                fontSize: 21.sp,
                                color: const Color(0xFF111111),
                              )
                            // style: TextStyleTheme.text21noToSanRegBlack(context),
                          ),
                          Text(chatInAppEmailDes.tr,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: const Color(0xFF111111),
                              )
                            // style: TextStyleTheme.text14ToSanRegBlack(context),
                          ),
                          Center(
                            child: Column(children: [
                              SizedBox(
                                height: 30.h,
                              ),
                              Icon(
                                Icons.email_outlined,
                                size: 50,
                                color: Colors.white,
                              ),
                              SizedBox(
                                height: 30.h,
                              ),
                              Container(
                                width: Get.width,
                                // width: MediaQuery.of(context).size.width * 0.65,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                    color: Colors.white,
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: TextField(
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: const Color(0xFF111111),
                                    fontFamily: 'Noto_Sans_loa',
                                  ),
                                  controller: _tgPinController,
                                  keyboardType: TextInputType.emailAddress,
                                  enableSuggestions: false,
                                  autocorrect: false,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    contentPadding: EdgeInsets.only(
                                        left: 10, top: 10, bottom: 10),
                                    hintText: chatInAppEmailDes.tr,
                                    hintStyle: TextStyle(
                                      fontSize: 16,
                                      color: const Color(0xFF111111),
                                      fontFamily: 'Noto_Sans_loa',
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 50.h,
                              ),
                              InkWell(
                                onTap: () async {
                                  String sendData =
                                      _tgPinController.text.toString();
                                  var check =
                                      await rTGController.verifyOTP_TG_FromHome(
                                          "email", sendData, context);
                                  if (check == "email") {
                                    stateOTP = false;
                                    email = false;
                                    Future.delayed(Duration(microseconds: 750),
                                        () {
                                      _tgPinController.clear();
                                      // Alert.alertPenddingGroup(context);
                                    });
                                  } else if (check == "false") {
                                    Future.delayed(Duration(microseconds: 500),
                                        () {
                                      _tgPinController.clear();
                                    });

                                    /// resend register TG
                                  }
                                },
                                child: Container(
                                  height: MediaQuery.of(context).size.height * 0.05,
                                  width: MediaQuery.of(context).size.width * 0.4,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: configTheme().colorScheme.onSecondary.withOpacity(0.8),
                                  ),
                                  child: Center(
                                    child: Text(
                                      accountOk.tr,
                                      style: TextStyle(
                                        fontSize: 18.sp,
                                        color: Colors.black,
                                        fontFamily: 'Noto_Sans_loa',
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ]),
                          ),
                        ],
                      ),
                    )
                  /// 2 step
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // _buildBackButton(),

                        // _buildHeaderText(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                width: MediaQuery.of(context).size.width * 0.8),
                            Text(chatInAppTowStep.tr,
                                style: TextStyle(
                                  fontSize: 21.sp,
                                  color: const Color(0xFF111111),
                                )
                                // style: TextStyleTheme.text21noToSanRegBlack(context),
                                ),
                            Text(chatInAppTowStepDes.tr,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: const Color(0xFF111111),
                                )
                                // style: TextStyleTheme.text14ToSanRegBlack(context),
                                ),
                          ],
                        ),

                        Center(
                          child: Column(children: [
                            Container(
                              width: MediaQuery.of(context).size.width * 0.65,
                              child: TextField(
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  color: const Color(0xFF111111),
                                  fontFamily: 'Noto_Sans_loa',
                                ),
                                controller: _tgPinController,
                                keyboardType: TextInputType.text,
                                obscureText: true,
                                enableSuggestions: false,
                                autocorrect: false,
                                decoration: InputDecoration(
                                  contentPadding: EdgeInsets.only(bottom: 0),
                                  focusedBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                          color: const Color(0xFFCD5C5C),
                                          width: 1.w)),
                                  enabledBorder: UnderlineInputBorder(
                                      borderSide: BorderSide(
                                          color: const Color(0xFFCD5C5C),
                                          width: 1.w)),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () async {
                                String sendData =
                                    _tgPinController.text.toString();
                                var check =
                                    await rTGController.verifyOTP_TG_FromHome(
                                        "2step", sendData, context);
                                if (check == "2step") {
                                  stateOTP = false;
                                  email = false;
                                  Future.delayed(Duration(microseconds: 750),
                                      () {
                                    _tgPinController.clear();
                                    // Alert.alertPenddingGroup(context);
                                  });
                                } else if (check == "false") {
                                  Future.delayed(Duration(microseconds: 500),
                                      () {
                                    _tgPinController.clear();
                                  });

                                  /// resend register TG
                                }
                              },
                              child: Container(
                                height:
                                    MediaQuery.of(context).size.height * 0.05,
                                width: MediaQuery.of(context).size.width * 0.4,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5.r),
                                  color: const Color(0xFFCD5C5C),
                                ),
                                child: Center(
                                  child: Text(
                                    'ok'.tr,
                                    style: TextStyle(
                                      fontSize: 18.sp,
                                      color: const Color(0xFFFAF0C0),
                                      fontFamily: 'Noto_Sans_loa',
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ]),
                        ),
                      ],
                    ),
        ),
      ),
    );
  }
}
