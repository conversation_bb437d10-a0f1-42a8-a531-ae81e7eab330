
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../controller/profile/profile.controller.dart';

class AAMGService extends StatefulWidget {
  const AAMGService({super.key});

  @override
  State<AAMGService> createState() => _AAMGServiceState();
}

class _AAMGServiceState extends State<AAMGService> {
  final GlobalKey webViewKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    print("WebView AAM Service");
    // print("https://aamg-service.web.app/?memberid=${Get.find<ProfileController>().member_id.value}&memberemail=${Get.find<ProfileController>().member_email.value}");
    requestCameraPermission();
    pullToRefreshController = PullToRefreshController(
      options: PullToRefreshOptions(
        color: Colors.blue,
      ),
      onRefresh: () async {
        if (Platform.isAndroid) {
          webViewController?.reload();
        } else if (Platform.isIOS) {
          // webViewController?.loadUrl(
          //     urlRequest: URLRequest(url: "https://aamg-service.web.app/"));
        }
      },
    );
  }

  requestCameraPermission() async {
    var status = await Permission.camera.request();

    if (status.isGranted) {
      await Permission.audio.request();
      // Have camera permission
      print("Have camera permission");
    } else {
      // Do not have permission
      print("No camera permission");
    }
  }

  InAppWebViewController? webViewController;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        useShouldOverrideUrlLoading: true,
        clearCache: false,
        mediaPlaybackRequiresUserGesture: true,
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
      ),
      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));

  late PullToRefreshController pullToRefreshController;
  String url = "";
  double progress = 0;
  final urlController = TextEditingController();

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: InAppWebView(
          key: webViewKey,
          initialUrlRequest: URLRequest(
              url: Uri.parse( Get.find<ProfileController>().member_id.value.isEmpty || Get.find<ProfileController>().member_email.value.isEmpty ?"https://aamg-service.web.app" :"https://aamg-service.web.app/?memberid=${Get.find<ProfileController>().member_id.value}&memberemail=${Get.find<ProfileController>().member_email.value}")),
          initialOptions: options,
          pullToRefreshController: pullToRefreshController,
          onWebViewCreated: (controller) {
            webViewController = controller;
          },
          onConsoleMessage: (controller, consoleMessage) async {
            print(consoleMessage.message);
            if (consoleMessage.message == "BACK_TO_MAIN") {
              print(consoleMessage.message);
              // await webViewCtl.getPocketBalance();
              Get.back();
            }
          },
          onLoadStart: (controller, url) {
            setState(() {
              this.url = url.toString();
              urlController.text = this.url;
            });
          },
          androidOnPermissionRequest: (controller, origin, resources) async {
            return PermissionRequestResponse(
                resources: resources,
                action: PermissionRequestResponseAction.GRANT);
          },
          shouldOverrideUrlLoading: (controller, navigationAction) async {
            var uri = navigationAction.request.url!;

            if (![
              "http",
              "https",
              "file",
              "chrome",
              "data",
              "javascript",
              "about"
            ].contains(uri.scheme)) {
              // if (await canLaunch(url)) {
              //   // Launch the App
              //   await launch(
              //     url,
              //   );
              //   // and cancel the request
              //   return NavigationActionPolicy.CANCEL;
              // }
            }

            return NavigationActionPolicy.ALLOW;
          },
          onLoadStop: (controller, url) async {
            pullToRefreshController.endRefreshing();
            setState(() {
              this.url = url.toString();
              urlController.text = this.url;
            });
          },
          onLoadError: (controller, url, code, message) {
            pullToRefreshController.endRefreshing();
          },
          onProgressChanged: (controller, progress) {
            if (progress == 100) {
              pullToRefreshController.endRefreshing();
            }
            setState(() {
              this.progress = progress / 100;
              urlController.text = this.url;
            });
          },
          onUpdateVisitedHistory: (controller, url, androidIsReload) {
            setState(() {
              this.url = url.toString();
              urlController.text = this.url;
            });
          },
        )
    );
  }
}
