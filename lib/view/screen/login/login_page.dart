
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/controller/AppConfigService.dart';
import 'package:AAMG/controller/login/login.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import '../../../controller/config/appVersion.controller.dart';
import '../../../controller/social_login/facebook.controller.dart';
import '../../../controller/social_login/facebook_authentication.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/utils/AppImageAssets.dart';

import '../../componance/widgets/app_pop_up/phone_code.dart';
import '../../componance/widgets/app_pop_up/policy_popups/terms_condition.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final LoginController loginCtl = Get.put(LoginController());
  final AppVersionController appVersionCtl = Get.put(AppVersionController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsFlutterBinding.ensureInitialized();
    Future.delayed(Duration.zero, () {
      // set default phone_code RPLC , RAFCO
      loginCtl.setInitPhoneCode();
    });
  }


  @override
  Widget build(BuildContext context) {
    // final shortcuts = Map.of(WidgetsApp.defaultShortcuts);
    // shortcuts[LogicalKeySet(LogicalKeyboardKey.space)] = const ActivateIntent();
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return Scaffold(
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //    print("อยู่ตรงนี้แหละ");
      //   },
      //   child: const Icon(Icons.add),
      // ),
      // backgroundColor: Colors.red,
      // backgroundColor: configTheme().colorScheme.background,
      body: Stack(
        children: [
          //TODO Background ลายน้ำ Rafco
          appConfigService.countryConfigCollection.toString() == 'rafco'
              ? Container(
            margin: EdgeInsets.only(top: 58.h),
            child: Image.asset(AppImageAssets.rafco_bg),
          )
              : Container(),
          Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w),
            child: Stack(
              children: [
                // TODO App bar
                Container(
                    margin: EdgeInsets.only(top: 60.h),
                    height: 46.h,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Get.back();
                          },
                          child: SizedBox(
                              height: 24.h,
                              width: 24.w,
                              child: SvgPicture.string(AppSvgImage.back_btn)
                          )
                        ),
                        SizedBox(
                          width: 72.w,
                          height: 34.h,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Obx(() {
                                return Text(
                                'Version ${appVersionCtl.versionInApp!.value}',
                                  style: TextStyle(
                                    color:
                                    configTheme().textTheme.bodyMedium?.color,
                                    fontSize: 10.sp,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .labelSmall
                                        ?.fontWeight,
                                  ),
                                );
                              }),
                              // Text("อยู่ตรงนี้ไง เป็นรึเปล่าละ")
                            ],
                          ),
                        )
                      ],
                    )),

                //TODO AAM Logo
                Container(
                  margin: EdgeInsets.only(top: 137.h),
                  width: 327.w,
                  height: 134.h,
                  child: Stack(
                    children: [
                      appConfigService.countryConfigCollection.toString() ==
                          "aam"
                          ? AAMLogo(context)
                          : appConfigService.countryConfigCollection
                          .toString() ==
                          "rplc"
                          ? RPLCLogo(context)
                          : RafcoLogo(context),
                      Positioned(
                          left: -0.50.w,
                          top: 112.h,
                          child: Text.rich(
                            TextSpan(
                              children: [
                                TextSpan(
                                    text: signInWelcome.tr,
                                    style: TextStyle(
                                      color: configTheme().primaryColorDark,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontWeight,
                                    )),
                                TextSpan(
                                    text: signInWelcomeDes.tr,
                                    style: TextStyle(
                                      color: configTheme().primaryColorDark,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .labelSmall
                                          ?.fontWeight,
                                    )),
                              ],
                            ),
                          )),
                    ],
                  ),
                ),

                //TODO phone input
                Container(
                  margin: EdgeInsets.only(top: 287.h),
                  width: 327.w,
                  height: 118.h,
                  color: Colors.white,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Obx(() {
                        return Container(
                          // width: 326.w,
                          height: 54.h,
                          decoration: ShapeDecoration(
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                width: 0.50.w,
                                strokeAlign: BorderSide.strokeAlignInside,
                                color: loginCtl.isPhoneValid!.value == false
                                    ? configTheme()
                                    .inputDecorationTheme
                                    .border
                                    ?.borderSide
                                    .color ??
                                    AppColors.textBlackColor
                                        .withOpacity(0.5)
                                    : configTheme()
                                    .inputDecorationTheme
                                    .focusedBorder
                                    ?.borderSide
                                    .color ??
                                    AppColors.textBlackColor
                                        .withOpacity(0.5),
                              ),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              //TODO pop-up เลือก phone code
                              appConfigService.countryConfigCollection != 'aam'
                                  ? Container(
                                // width: 51.w,
                                height: 36.h,
                                // color: Colors.teal,
                                child: Row(
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        PhoneCodeWidget.PhoneCodePopUp(
                                            context, 'login');
                                      },
                                      child: Container(
                                        color: Colors.transparent,
                                        width: 51.w,
                                        // height: 28.h,
                                        // alignment: Alignment.centerLeft,
                                        // margin: EdgeInsets.only(left: 21.w),
                                        child: Obx(() {
                                          return Center(
                                            child: Text(
                                              loginCtl.phone_code!.value,
                                              style: TextStyle(
                                                color:
                                                const Color(0xFF1A1818),
                                                fontSize: 14.sp,
                                                fontFamily:
                                                'NotoSansThai-Bold',
                                                fontWeight: FontWeight.w700,
                                                // height: 0.14.h,
                                              ),
                                            ),
                                          );
                                        }),
                                      ),
                                    ),
                                    VerticalDivider(
                                      color: Color(0xFF1A181880).withOpacity(
                                          0.5),
                                      thickness: 0.5,
                                      endIndent: 1,
                                      indent: 1,
                                      width: 0.5,
                                    )
                                    // Container(
                                    //   alignment: Alignment.center,
                                    //   child: Image.asset(
                                    //       'assets/register/Line.png'),
                                    // ),
                                  ],
                                ),
                              )
                                  : Container(),
                              SizedBox(
                                width: 20.w,
                              ),
                              Expanded(
                                // Make the text field flexible
                                //     child: TextFormField(
                                //   minLines: 1,
                                //   controller: loginCtl.phone_login.value,
                                //   // cursorWidth: 1.5.w,
                                //   keyboardType: TextInputType.number,
                                //   // Set keyboard type
                                //   inputFormatters: [
                                //     LengthLimitingTextInputFormatter(11),
                                //   ],
                                //   autofocus: false,
                                //   focusNode: loginCtl.textFieldFocus,
                                //   onTap: () {
                                //     // Open the keyboard when the TextField is tapped
                                //     FocusScope.of(context)
                                //         .requestFocus(loginCtl.textFieldFocus);
                                //   },
                                //   onChanged: (value) {
                                //     print(value);
                                //     loginCtl.setPhoneLogin(value);
                                //
                                //     if (value.length == 10) {
                                //       FocusScope.of(context).requestFocus(
                                //           FocusNode()); // ปิด keyboard
                                //     }
                                //   },
                                //       showCursor: true,
                                //
                                //       // cursorColor: const Color(0xFF1A1818),
                                //
                                //       // cursorHeight: 20.h,
                                //   decoration: InputDecoration(
                                //       border: InputBorder.none,
                                //       focusedBorder: InputBorder.none,
                                //       // Remove default border
                                //       labelText: signInPhone.tr,
                                //       labelStyle: TextStyle(
                                //         color: const Color(0xFF1A1818)
                                //             .withOpacity(0.3),
                                //         fontSize: 12.sp,
                                //         fontFamily: 'NotoSansThai',
                                //         fontWeight: FontWeight.w400,
                                //         height: 0.14.h,
                                //       ),
                                //       hintText: '080-0000000',
                                //
                                //       // Hint text
                                //       hintStyle: TextStyle(
                                //         color: const Color(0x331A1818)
                                //             .withOpacity(0.2),
                                //         fontSize: 14.sp,
                                //         fontWeight: FontWeight.w400,
                                //         // height: 0.14,
                                //         fontFamily: 'NotoSansThai',
                                //       )),
                                //   style: TextStyle(
                                //     color:
                                //         const Color(0xFF1A1818).withOpacity(1.0),
                                //     fontSize: 14.sp,
                                //     fontFamily: 'NotoSansThai',
                                //     fontWeight: FontWeight.w700,
                                //     // height: 0.14.h,
                                //   ),
                                // )
                                child: TextFormField(
                                  showCursor: true,
                                  // textAlign: registerCtl.isPhoneValid!.value
                                  //     ? TextAlign.start
                                  //     : TextAlign.center,
                                  // cursorWidth: 0.w,
                                  // cursorColor: const Color(0xFFFF9300),
                                  keyboardType: TextInputType.number,
                                  minLines: 1,
                                  autofocus: false,
                                  focusNode: loginCtl.textFieldFocus,
                                  controller:
                                  loginCtl.phone_login.value,
                                  onTap: () {
                                    // Open the keyboard when the TextField is tapped
                                    FocusScope.of(context).requestFocus(
                                        loginCtl.textFieldFocus);
                                    // registerCtl.checkFormatedPhone(
                                    //     registerCtl
                                    //         .phone_regis.value.text);
                                  },
                                  inputFormatters: [
                                    LengthLimitingTextInputFormatter(
                                        13),
                                  ],
                                  decoration: InputDecoration(
                                      border: InputBorder.none,
                                      focusedBorder: InputBorder.none,
                                      // Remove default border
                                      labelText: signInPhone.tr,
                                      labelStyle: TextStyle(
                                        color: const Color(0xFF1A1818)
                                            .withOpacity(0.3),
                                        fontSize: 12.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        height: 0.14.h,
                                      ),
                                      hintText: '080-0000000',

                                      // Hint text
                                      hintStyle: TextStyle(
                                        color: const Color(0x331A1818)
                                            .withOpacity(0.2),
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w400,
                                        // height: 0.14,
                                        fontFamily: 'NotoSansThai',
                                      )),
                                  style: TextStyle(
                                    color:
                                    const Color(0xFF1A1818).withOpacity(1.0),
                                    fontSize: 14.sp,
                                    fontFamily: 'NotoSansThai',
                                    fontWeight: FontWeight.w700,
                                    // height: 0.14.h,
                                  ),
                                  onChanged: (value) {
                                    // print(value);

                                    if (value.length == 10 &&
                                        loginCtl.phone_code!.value == '+66') {
                                      loginCtl.setPhoneLogin(value);
                                      FocusScope.of(context).requestFocus(
                                          FocusNode()); // ปิด keyboard
                                    }
                                  },
                                ),


                              ),
                              // Obx(() {
                              //   return loginCtl.isPhoneValid!.value == true
                              //       ? Container(
                              //           width: 14.w,
                              //           height: 14.h,
                              //           alignment: Alignment.center,
                              //           margin: EdgeInsets.only(right: 20.w),
                              //           child: Center(
                              //             child: CircularProgressIndicator(
                              //               backgroundColor: configTheme()
                              //                   .colorScheme
                              //                   .secondary,
                              //               valueColor:
                              //                   AlwaysStoppedAnimation<Color>(
                              //                       configTheme()
                              //                           .colorScheme
                              //                           .primary
                              //                           .withOpacity(1.0)),
                              //               strokeWidth: 1.0,
                              //             ),
                              //           ),
                              //         )
                              //       : Container();
                              // })
                            ],
                          ),
                        );
                      }),
                      SizedBox(height: 12.h),
                      GestureDetector(
                        onTap: () async {
                          // Get.to(()=>VerifyOTPScreen());
                          // await loginCtl.login();

                          // loginCtl.testFunc();
                          debugPrint('phone: ${loginCtl.phone_login.value
                              .text}');
                          await loginCtl.checkOTPConfig(context, loginCtl
                              .phone_login.value.text);
                        },
                        child: Container(
                          width: 327.w,
                          height: 52.h,
                          decoration: ShapeDecoration(
                            gradient: AppColorsGradient.buttonGradient,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                signIn.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: configTheme().primaryColorLight,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelMedium
                                      ?.fontWeight,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                //TODO Connect Social
                Container(
                  width: 327.w,
                  // height: 342.h,
                  margin: EdgeInsets.only(top: 422.h),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        width: 324.77.w,
                        height: 90.h,
                        child: Stack(
                          children: [
                            Container(
                              alignment: Alignment.center,
                              // width: 87.82,
                              height: 18.h,
                              child: Text.rich(
                                textAlign: TextAlign.center,
                                TextSpan(
                                  children: [
                                    TextSpan(
                                        text: signInOr.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelMedium
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelMedium
                                              ?.fontWeight,
                                        )

                                      // configTheme().primaryTextTheme.labelSmall
                                    ),
                                    const TextSpan(
                                      text: ' ',
                                    ),
                                    TextSpan(
                                        text: signInPass.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(0.75),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                        )),
                                  ],
                                ),
                              ),
                            ),
                            // _buildConnectSocial(context),
                            _buildConnectSocia_uploadAppStore(context),
                          ],
                        ),
                      ),
                      Spacer(),
                      // SizedBox(height: 16.h),
                      GestureDetector(
                        onTap: () {
                          final country = appConfigService
                              .countryConfigCollection.toString();

                          country == 'aam'
                              ? TermsAndConditionWidget
                              .buildTermsAndConditionsAAM(context)
                              : country == 'rplc'
                              ? TermsAndConditionWidget
                              .buildTermsAndConditionsRPLC(
                              context)
                              : country == 'rafco'
                              ? TermsAndConditionWidget
                              .buildTermsAndConditionsRAFCO(context)
                              : Container();
                        },
                        child: SizedBox(
                          child: Container(
                            // color: Colors.red,
                            // width: 327.w,
                            height: 48.h,
                            child: Row(
                              // mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: signInDes.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(0.75),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: ' ',
                                      ),
                                      TextSpan(
                                        text: signInPolicy.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelMedium
                                              ?.fontFamily,
                                          fontWeight: FontWeight.w500,
                                          decoration: TextDecoration.underline,
                                          decorationColor: Colors.black,
                                          height: 0.17.h,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: ' ',
                                      ),
                                      TextSpan(
                                        text: signInDes2.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color
                                              ?.withOpacity(0.75),
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .labelSmall
                                              ?.fontWeight,
                                          height: 0.17.h,
                                        ),
                                      ),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 48.h,
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  AAMLogo(context) {
    return Stack(
      children: [
        Positioned(
          left: 0,
          top: 0,
          child: SizedBox(
            width: 40.w,
            height: 26.17.h,
            child: SvgPicture.string(
              AppSvgImage.aam_logo,
              fit: BoxFit.fill,
            ),
          ),
        ),
        Positioned(
          left: -0.50.w,
          top: 33.h,
          child: SizedBox(
            width: 115.w,
            height: 68.h,
            child: Image.asset(
              'assets/register/aam/aam_thai.png',
              fit: BoxFit.fill,
            ),
          ),
        ),
      ],
    );
  }

  RPLCLogo(context) {
    return Container(
      width: 115.w,
      height: 47.66.h,
      margin: EdgeInsets.only(top: 42.h),
      child: Image.asset(AppImageAssets.rplc_logo_login),
    );
  }

  RafcoLogo(context) {
    return Container(
      width: 114.13,
      height: 101,
      clipBehavior: Clip.antiAlias,
      decoration: const BoxDecoration(),
      child: Image.asset(AppImageAssets.rafco_logo_login),
    );
  }

  _buildConnectSocial(context) {
    return Container(
      width: 324.77.w,
      height: 90.h,
      child: Stack(
        children: [
          Positioned(
            left: 68.19.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child:   GestureDetector(
                      onTap: (){
                        debugPrint("อยู่ระหว่างพัฒนา");
                        Get.snackbar("ขออภัย", "ฟีเจอร์นี้ยังอยู่ระหว่างการพัฒนา กรุณารอติดตามการอัปเดต!");
                      },
                      child: Container(
                        width: 24.w,
                        height: 24.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: const BoxDecoration(),
                        child: Image.asset(
                          'assets/social/apple.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 0.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child: Container(
                      width: 24.w,
                      height: 24.h,
                      padding: EdgeInsets.only(right: 0.48.w),
                      clipBehavior: Clip.antiAlias,
                      decoration: const BoxDecoration(),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 23.52.w,
                            height: 24.h,
                            child: Image.asset(
                              'assets/social/google.png',
                              fit: BoxFit.fill,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 204.58.w,
            top: 38.h,
            child: GestureDetector(
              onTap: () {
                print("facebook function ");
                Get.lazyPut(() => FaceBookController(), fenix: true);
                // Get.find<FaceBookController>().facebookLogin();
                // Get.find<FaceBookController>().loginWithFacebook();
                // Get.find<FaceBookController>().loginWithFacebook_v2();
                // FacebookAuthentication().signOut();
                FacebookAuthentication().signIn(context);
              },
              child: Container(
                width: 52.w,
                height: 52.h,
                child: Stack(
                  children: [
                    Positioned(
                      left: 14.w,
                      top: 14.h,
                      child: Container(
                        width: 24.w,
                        height: 24.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: const BoxDecoration(),
                        child: Image.asset(
                          'assets/social/facebook.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            left: 272.77.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child: Container(
                      width: 24.w,
                      height: 24.h,
                      child: Stack(
                        children: [
                          Positioned(
                            left: 0.w,
                            top: 0.h,
                            child: GestureDetector(
                              onTap: (){
                                debugPrint("อยู่ระหว่างพัฒนา");
                                Get.snackbar("ขออภัย", "ฟีเจอร์นี้ยังอยู่ระหว่างการพัฒนา กรุณารอติดตามการอัปเดต!");
                              },
                              child: Container(
                                width: 24.w,
                                height: 24.h,
                                child: Image.asset(
                                  'assets/social/line.png',
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 136.39.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child: Container(
                      width: 24.w,
                      height: 24.h,
                      clipBehavior: Clip.antiAlias,
                      decoration: const BoxDecoration(),
                      child: Image.asset(
                        'assets/social/telegram.png',
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildConnectSocia_uploadAppStore(context) {
    return Container(
      width: 324.77.w,
      height: 90.h,
      child: Stack(
        children: [
          Positioned(
            left: 68.19.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child:   Container(
                      width: 23.52.w,
                      height: 24.h,
                      child: Image.asset(
                        'assets/social/google.png',
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            left: 204.58.w,
            top: 38.h,
            child: GestureDetector(
              onTap: () {
                print("facebook function ");
                Get.lazyPut(() => FaceBookController(), fenix: true);
                // Get.find<FaceBookController>().facebookLogin();
                // Get.find<FaceBookController>().loginWithFacebook();
                // Get.find<FaceBookController>().loginWithFacebook_v2();
                // FacebookAuthentication().signOut();
                FacebookAuthentication().signIn(context);
              },
              child: Container(
                width: 52.w,
                height: 52.h,
                child: Stack(
                  children: [
                    Positioned(
                      left: 14.w,
                      top: 14.h,
                      child: Container(
                        width: 24.w,
                        height: 24.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: const BoxDecoration(),
                        child: Image.asset(
                          'assets/social/facebook.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            left: 136.39.w,
            top: 38.h,
            child: Container(
              width: 52.w,
              height: 52.h,
              child: Stack(
                children: [
                  Positioned(
                    left: 14.w,
                    top: 14.h,
                    child: Container(
                      width: 24.w,
                      height: 24.h,
                      clipBehavior: Clip.antiAlias,
                      decoration: const BoxDecoration(),
                      child: Image.asset(
                        'assets/social/telegram.png',
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

}
