import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:introduction_screen/introduction_screen.dart';
import 'package:AAMG/app_config.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/intro/intro.controller.dart';
import 'package:AAMG/controller/session/session.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors_gradient.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:AAMG/view/screen/register/register_page.dart';
import '../../../controller/AppConfigService.dart';
import '../../componance/AppBackgound.dart';
import '../login/login_page.dart';

// class IntroPage extends StatefulWidget {
//   const IntroPage({Key? key}) : super(key: key);
//
//   @override
//   State<IntroPage> createState() => _IntroPageState();
// }
//
// class _IntroPageState extends State<IntroPage> {
//   late final SessionController sessionController;
//   // late final NewsController newsController;
//   // late final MRController mrController;
//   // late final RegisterAddressController registerAddressController;
//
//   @override
//   void initState() {
//     super.initState();
//
//     // Initialize controllers using Get.put()
//     sessionController = Get.put(SessionController());
//     // newsController = Get.put(NewsController());
//     // mrController = Get.put(MRController());
//     // registerAddressController = Get.put(RegisterAddressController());
//
//     _loadData();
//
//     print("IntroPage initState");
//   }
//
//   Future<void> _loadData() async {
//     try {
//       // Load session and other data
//
//       // List of futures
//       // final List<Future<dynamic>> futures = [
//       //   newsController.getNewsPromotion(),
//       //   mrController.getMRData(),
//       //   registerAddressController.checkConfigAddress(),
//       // ];
//
//       // Ensure getSession is completed first if needed
//       await sessionController.getSession(context);
//
//       // Wait for all futures to complete
//       // await Future.wait(futures);
//     } catch (e) {
//       print('Error loading data: $e');
//       // handle exception if necessary
//     }
//   }
//
//
//   @override
//   Widget build(BuildContext context) {
//     print('IntroPage');
//     return Scaffold(
//       body: AppBackground.backgroundIntro(context),
//     );
//   }
// }

class IntroPage extends StatefulWidget {
  const IntroPage({Key? key}) : super(key: key);

  @override
  State<IntroPage> createState() => _IntroPageState();
}

class _IntroPageState extends State<IntroPage> {
  final SessionController sessionController = Get.put(SessionController());


  @override
  void initState() {
    super.initState();
    _initializeData();
    debugPrint("IntroPage initialized");
  }

  Future<void> _initializeData() async {
    // await Future.delayed(const Duration(milliseconds: 2000));
    Future.delayed(Duration.zero , () async {
      await sessionController.getSession(context);
    });

  }

  @override
  Widget build(BuildContext context) {
    debugPrint('Building IntroPage');
    return Scaffold(
      body: AppBackground.backgroundIntro(context),
    );
  }
}


class OnBroardingPage extends StatelessWidget {
  OnBroardingPage({Key? key}) : super(key: key);

  final introKey = GlobalKey<IntroductionScreenState>();

  final HomeController homeController = Get.put(HomeController());

  final IntroController introController = Get.find<IntroController>();

  // final MRController mrController = Get.put(MRController());

  @override
  Widget build(BuildContext context) {
    // final AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return PopScope(
      canPop: false, // ป้องกันการ pop อัตโนมัติ
      onPopInvoked: (didPop) async {
        debugPrint('onPopInvoked');
      },
      child: Scaffold(
        body: Container(
          child: GetBuilder<IntroController>(builder: (introController) {
            return Stack(children: [
              AppBackground.backgroundColorOnBoarding(context),
              introController.isFinalPage!.value != true
                  ? Stack(
                      children: [
                        // page
                        Swiper(
                          itemBuilder: (BuildContext context, int index) {
                            return Stack(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(top: 141.h),
                                  height: 270.h,
                                  width: 375.w,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: AssetImage(introController
                                                      .indexPage!.value ==
                                                  0 &&
                                              appConfigService
                                                      .countryConfigCollection ==
                                                  "rafco"
                                          ? AppImageAssets.rafco_bg_intro
                                          : ""),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Container(
                                  width: 375.w,
                                  // height: 690.h,
                                  margin: EdgeInsets.only(top: 122.h),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        // height: 550.h,
                                        // color: Colors.teal,
                                        width: Get.width,
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            //TODO ข้อความ
                                            Padding(
                                              padding: EdgeInsets.only(
                                                  left: 24.w, right: 24.w),
                                              child: Container(
                                                // color: Colors.yellow,
                                                child: Column(
                                                  // mainAxisSize: MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.start,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    SizedBox(
                                                      height: 26.h,
                                                    ),
                                                    //title
                                                    Container(
                                                      height: 70.h,
                                                      child: Column(
                                                        children: [
                                                          Container(
                                                              height: 35.h,
                                                              alignment: Alignment
                                                                  .centerLeft,
                                                              child: Text(
                                                                introController
                                                                            .indexPage!
                                                                            .value ==
                                                                        0
                                                                    ? '${onBoardingTitleEasy.tr}'
                                                                    : introController
                                                                                .indexPage!
                                                                                .value ==
                                                                            1
                                                                        ? onBoardingTitleTrust
                                                                            .tr
                                                                        : onBoardingTitleFast
                                                                            .tr,
                                                                style: TextStyle(
                                                                    color: configTheme()
                                                                        .primaryColor,
                                                                    fontSize: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontSize,
                                                                    fontFamily: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontFamily,
                                                                    fontWeight: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontWeight),
                                                              )),
                                                          Container(
                                                            height: 35.h,
                                                            alignment: Alignment
                                                                .centerLeft,
                                                            child: Text(
                                                                introController
                                                                            .indexPage!
                                                                            .value ==
                                                                        0
                                                                    ? onBoardingTitleConfident
                                                                        .tr
                                                                    : introController
                                                                                .indexPage!
                                                                                .value ==
                                                                            1
                                                                        ? onBoardingTitleReliability
                                                                            .tr
                                                                        : onBoardingTitleHassle
                                                                            .tr,
                                                                style: TextStyle(
                                                                    color: configTheme()
                                                                        .secondaryHeaderColor,
                                                                    fontSize: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontSize,
                                                                    fontFamily: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontFamily,
                                                                    fontWeight: configTheme()
                                                                        .primaryTextTheme
                                                                        .displayLarge
                                                                        ?.fontWeight)),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      height: 16.h,
                                                    ),
                                                    // body รายละเอียด
                                                    Text(
                                                        introController.indexPage!
                                                                    .value ==
                                                                0
                                                            ? onBoardingDescription1
                                                                .tr
                                                            : introController
                                                                        .indexPage!
                                                                        .value ==
                                                                    1
                                                                ? onBoardingDescription3
                                                                    .tr
                                                                : onBoardingDescription5
                                                                    .tr,
                                                        style: TextStyle(
                                                            color: configTheme()
                                                                .dialogBackgroundColor,
                                                            fontSize: configTheme()
                                                                .primaryTextTheme
                                                                .bodyLarge
                                                                ?.fontSize,
                                                            fontFamily: configTheme()
                                                                .primaryTextTheme
                                                                .bodyLarge
                                                                ?.fontFamily,
                                                            fontWeight: configTheme()
                                                                .primaryTextTheme
                                                                .bodyLarge
                                                                ?.fontWeight)),
                                                    Text(
                                                        introController.indexPage!
                                                                    .value ==
                                                                0
                                                            ? onBoardingDescription2
                                                                .tr
                                                            : introController
                                                                        .indexPage!
                                                                        .value ==
                                                                    1
                                                                ? onBoardingDescription4
                                                                    .tr
                                                                : onBoardingDescription6
                                                                    .tr,
                                                        maxLines: 4,
                                                        style: TextStyle(
                                                            color: configTheme()
                                                                .dialogBackgroundColor,
                                                            fontSize: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontSize,
                                                            fontFamily: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontFamily,
                                                            fontWeight: configTheme()
                                                                .primaryTextTheme
                                                                .bodySmall
                                                                ?.fontWeight)),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Spacer(),
                                      //button
                                      introController.indexPage!.value == 2
                                          ? InkWell(
                                              onTap: () {
                                                introController.setFinalPage();
                                              },
                                              child: Container(
                                                width: 327.w,
                                                height: 52.h,
                                                decoration: ShapeDecoration(
                                                  color: configTheme()
                                                      .colorScheme
                                                      .primary,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(12),
                                                  ),
                                                ),
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Container(
                                                      decoration: ShapeDecoration(
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(50),
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Text(onBoardingNext.tr,
                                                              textAlign: TextAlign
                                                                  .center,
                                                              style: TextStyle(
                                                                  color: configTheme()
                                                                      .textTheme
                                                                      .titleMedium
                                                                      ?.color,
                                                                  fontSize: configTheme()
                                                                      .primaryTextTheme
                                                                      .bodyMedium
                                                                      ?.fontSize,
                                                                  fontFamily: configTheme()
                                                                      .primaryTextTheme
                                                                      .bodyMedium
                                                                      ?.fontFamily,
                                                                  fontWeight: configTheme()
                                                                      .primaryTextTheme
                                                                      .bodyMedium
                                                                      ?.fontWeight)),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            )
                                          : InkWell(
                                              onTap: () {
                                                introController.setNextage();
                                              },
                                              child: SvgPicture.string(
                                                AppConfig.of(context)
                                                            .countryConfigCollection
                                                            .toString() ==
                                                        'aam'
                                                    ? AppSvgImage.aam_btn_boarding
                                                    : AppConfig.of(context)
                                                                .countryConfigCollection
                                                                .toString() ==
                                                            'rafco'
                                                        ? AppSvgImage
                                                            .rafco_btn_boarding
                                                        : AppSvgImage
                                                            .rplc_btn_boarding,
                                              ),
                                            ),
                                      SizedBox(
                                        height: 48.h,
                                      ),
                                    ],
                                  ),
                                ),
                                //TODO รูปภาพ
                                Padding(
                                  padding: EdgeInsets.only(bottom: 144.h),
                                  child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: _buildIntroImage(
                                        context, introController),
                                  ),
                                )
                              ],
                            );
                          },
                          itemCount: 3,
                          onIndexChanged: (int index) {
                            print("ลำดับที่ $index");
                            introController.setIndexPage(index);
                          },
                          loop: false,
                        ),
                        //header
                        _buildHeader(context, introController),
                      ],
                    )
                  : Stack(
                      children: [
                        AppConfig.of(context)
                                    .countryConfigCollection
                                    .toString() ==
                                'aam'
                            ? Container(
                                decoration: BoxDecoration(
                                    gradient:
                                        AppColorsGradient.buttonAAMGradient),
                                width: Get.width,
                                height: Get.height,
                                // height: Get.height-Get.height*0.1,
                              )
                            : Container(),
                        Container(
                          width: Get.width,
                          padding: EdgeInsets.only(
                              top: AppConfig.of(context)
                                          .countryConfigCollection
                                          .toString() ==
                                      'aam'
                                  ? 436.h
                                  : 0.h),
                          child: AppBackground.backgroundColorOnBoardingFinal(
                              context),
                        ),
                        Column(
                          children: [
                            Container(
                              margin: EdgeInsets.only(
                                  top: appConfigService.countryConfigCollection ==
                                          'aam'
                                      ? 0
                                      : 44.h),
                              width: Get.width,
                              height: 436.h,
                              child: Image.asset(
                                AppConfig.of(context)
                                            .countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? AppImageAssets.aam_intro_final
                                    : AppConfig.of(context)
                                                .countryConfigCollection
                                                .toString() ==
                                            'rafco'
                                        ? AppImageAssets.rafco_intro_final
                                        : AppImageAssets.rplc_intro_final,
                                fit: BoxFit.fill,
                              ),
                            ),
                            Container(
                              // height: 178.h,
                              width: Get.width,
                              margin: EdgeInsets.only(
                                  left: 24.w,
                                  right: 24.w,
                                  top: AppConfig.of(context)
                                              .countryConfigCollection
                                              .toString() ==
                                          'rafco'
                                      ? 0
                                      : AppConfig.of(context)
                                                  .countryConfigCollection
                                                  .toString() ==
                                              'rplc'
                                          ? 0
                                          : 24.h),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                      alignment: Alignment.centerLeft,
                                      height: 120.17.h,
                                      child: Image.asset(
                                        AppConfig.of(context)
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'aam'
                                            ? AppImageAssets.aam_logo_text
                                            : AppConfig.of(context)
                                                        .countryConfigCollection
                                                        .toString() ==
                                                    'rafco'
                                                ? AppImageAssets.rafco_logo_text
                                                : AppImageAssets.rplc_logo_text,
                                      )),
                                  FittedBox(
                                    child: Text(onBoardingDescription7.tr,
                                        style: TextStyle(
                                            color: configTheme()
                                                .dialogBackgroundColor,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodySmall
                                                ?.fontWeight)),
                                  ),
                                ],
                              ),
                            ),
                            Spacer(),
                            Padding(
                              padding: EdgeInsets.only(left: 24.w, right: 24.w),
                              child: Row(
                                // mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                // crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      print('GUEST USER');
                                      await homeController.setGuestUser();
                                    },
                                    child: Container(
                                        width: 71.w,
                                        height: 52.h,
                                        decoration: ShapeDecoration(
                                          shape: RoundedRectangleBorder(
                                            side: BorderSide(
                                              width: 1.w,
                                              // strokeAlign: BorderSide.strokeAlignOutside,
                                              color: Colors.white,
                                            ),
                                            borderRadius:
                                                BorderRadius.circular(12.r),
                                          ),
                                        ),
                                        child: Center(
                                          child: Text(
                                            onBoardingVisit.tr,
                                            style: TextStyle(
                                              color:
                                                  configTheme().primaryColorLight,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            ),
                                          ),
                                        )),
                                  ),
                                  // SizedBox(width: 12.w),
                                  GestureDetector(
                                    onTap: () {
                                      print('register');
                                      Get.to(const RegisterPage());
                                    },
                                    child: Container(
                                      width: 244.w,
                                      height: 52.h,
                                      clipBehavior: Clip.antiAlias,
                                      decoration: ShapeDecoration(
                                        color: configTheme().colorScheme.primary,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12.r),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          onBoardingRegis.tr,
                                          // textAlign: TextAlign.center,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .titleMedium
                                                ?.color,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 24.h,
                            ),
                            InkWell(
                              onTap: () {
                                // mrController.setShowTutorialMr();
                                Get.to(()=>LoginPage());
                              },
                              child: Container(
                                child: Text.rich(
                                  TextSpan(
                                    children: [
                                      TextSpan(
                                        text: onBoardingAcc.tr,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodySmall
                                              ?.fontWeight,
                                        ),
                                      ),
                                      const TextSpan(
                                        text: ' ',
                                      ),
                                      TextSpan(
                                          text: onBoardingLogin.tr,
                                          style: TextStyle(
                                            color:
                                                configTheme().primaryColorLight,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                            decoration: TextDecoration.underline,
                                          )),
                                    ],
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                            SizedBox(height: 48.h)
                          ],
                        ),
                      ],
                    ),
            ]);
          }),
        ),
      ),
    );
  }

  Widget _buildIntroImage(context, IntroController introController) {
    if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
      return Container(
        width: Get.width,
        // color: Colors.white,
        // height: 447.h,
        child: Stack(
          children: [
            Container(
              width: Get.width,
              // color: Colors.white,
              child: Image.asset(
                introController.indexPage!.value == 0
                    ? AppImageAssets.aam_intro_1
                    : introController.indexPage!.value == 1
                        ? AppImageAssets.aam_intro_2
                        : AppImageAssets.aam_intro_3,
                // width: Get.width
                // fit: BoxFit.fill,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
          // color: Colors.white,
          // height: 329.h,
          width: Get.width,
          margin: EdgeInsets.only(top: 39.h),
          child: introController.indexPage!.value == 0
              ? Image.asset(
                  AppConfig.of(context).countryConfigCollection.toString() ==
                          'rafco'
                      ? AppImageAssets.rafco_intro_1
                      : AppImageAssets.rplc_intro_1,
                  fit: BoxFit.fill,
                )
              : introController.indexPage!.value == 1
                  ? Image.asset(
                      AppConfig.of(context)
                                  .countryConfigCollection
                                  .toString() ==
                              'rafco'
                          ? AppImageAssets.rafco_intro_2
                          : AppImageAssets.rplc_intro_2,
                      fit: BoxFit.fill,
                    )
                  : Image.asset(
                      AppConfig.of(context)
                                  .countryConfigCollection
                                  .toString() ==
                              'rafco'
                          ? AppImageAssets.rafco_intro_3
                          : AppImageAssets.rplc_intro_3,
                      fit: BoxFit.fill,
                    ));
    }
  }

  Widget _buildHeader(context, introController) {
    return Container(
      margin: EdgeInsets.only(top: 74.h, left: 24.w, right: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppConfig.of(context).countryConfigCollection.toString() == 'aam' ||
                  AppConfig.of(context).countryConfigCollection.toString() ==
                      'rplc'
              ? SvgPicture.string(
                  AppConfig.of(context).countryConfigCollection.toString() ==
                          'aam'
                      ? AppSvgImage.aam_logo
                      : AppSvgImage.rplc_logo_boarding,
                )
              : Image.asset(
                  AppImageAssets.rafco_logo_boarding,
                  width: 32.w,
                  height: 28.32.h,
                ),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Obx(() {
                return Opacity(
                  opacity: introController.indexPage!.value == 0 ? 1 : 0.4,
                  child: Container(
                    width: introController.indexPage!.value == 0 ? 15 : 4,
                    height: 4,
                    decoration: ShapeDecoration(
                      color: configTheme().primaryColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8)),
                    ),
                  ),
                );
              }),
              const SizedBox(width: 6),
              Opacity(
                opacity: introController.indexPage!.value == 1 ? 1 : 0.4,
                child: Container(
                  width: introController.indexPage!.value == 1 ? 15 : 4,
                  height: 4,
                  decoration: ShapeDecoration(
                    color: configTheme().primaryColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ),
              const SizedBox(width: 6),
              Opacity(
                opacity: introController.indexPage!.value == 2 ? 1 : 0.4,
                child: Container(
                  width: introController.indexPage!.value == 2 ? 15 : 4,
                  height: 4,
                  decoration: ShapeDecoration(
                    color: configTheme().primaryColor,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ),
            ],
          ),
          InkWell(
            onTap: () {
              introController.setFinalPage();

              print('isFinalPage: ${introController.isFinalPage!.value}');
            },
            child: Container(
              color: Colors.transparent,
              width: 43.w,
              height: 34.h,
              alignment: Alignment.center,
              child: Text(
                introController.indexPage!.value == 2
                    ? onBoardingClose.tr
                    : onBoardingSkip.tr,
                style: TextStyle(
                  color: configTheme().primaryColorLight,
                  fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.bodyMedium?.fontWeight,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
