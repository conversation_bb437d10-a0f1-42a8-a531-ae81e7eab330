import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/likepoint/webview.point.controller.dart';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/screen/mr/add_referralID.dart';
import 'package:AAMG/view/screen/mr/mr_history_loadApp.dart';
import 'package:AAMG/view/screen/mr/mr_history_loan.dart';
import 'package:AAMG/view/screen/mr/mr_register.dart';
import 'package:AAMG/view/screen/mr/qr_referal.dart';
import 'package:AAMG/view/screen/mr/referfriend.dart';
import 'package:AAMG/view/screen/mr/reward_history.dart';
import 'package:AAMG/view/screen/register/register_page.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';

import '../../componance/themes/app_colors.dart';
import '../../componance/utils/AppSvgImage.dart';
import 'mr_popup.dart';

class MRPage extends StatefulWidget {
  const MRPage({super.key});

  @override
  State<MRPage> createState() => _MRPageState();
}

class _MRPageState extends State<MRPage> {
  final HomeController homeController = Get.find<HomeController>();
  final ProfileController profileController = Get.find<ProfileController>();
  final MRController mrController = Get.find<MRController>();
  final NavigationController navigationController =
  Get.find<NavigationController>();
  var isGuest = false;
  final box = GetStorage();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    Future.delayed(Duration.zero, () async {
      print("MRPage");
      print(mrController.showTutorialMr!.value);
      final isPopupShown = box.read("isMRPopupShown") ?? false;
      // if(!isPopupShown){
      //
      //   homeController.speak(mrController
      //       .popUpText[
      //   0]['title'] + (mrController
      //       .indexTutorialMR
      //       .value ==
      //       2
      //       ? "${profileController.profile.value.firstname}"
      //       : "" ) + mrController
      //       .popUpText[
      //   0]
      //   ['content'] + mrController
      //       .popUpText[
      //   0]
      //   ['content1'] + mrController
      //       .popUpText[
      //   0]
      //   ['content2']);
      // }
      // if(mrController.showTutorialMr!.value == false){
      //   buildContainerShowTutorial();
      //   setState(() {
      //
      //   });
      // }

      // if (box.read('isShowMr') == true) {
      //   // show = box.read('isShowTutorial');
      //   showTutorialMR();
      //   print('isShowTutorialHome: ${box.read('isShowTutorial')}');
      //   setState(() {});
      // }();
    });
  }

  // void checkGuestData() async {
  //   GetStorage storage = GetStorage();
  //   isGuest = await storage.read('isGuest');
  //
  //   if (isGuest != true) {
  //     mrController.Loading.value = true;
  //     mrController.getReferFriend();
  //   }else{
  //     mrController.Loading.value = false;
  //   }
  // }
  final WebViewPointController webViewPointCtl =
  Get.put(WebViewPointController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () {
      //     Get.put(NavigationController().showPopupMR());
      //     // mrController.getMRData();
      //   },
      //   child: Icon(Icons.add),
      // ),
        body: GetBuilder<MRController>(
          builder: (mrController) {
            return Stack(
              children: [
                homeController.isGuest!.value
                    ? Container(
                  height: Get.height,
                  width: Get.width,
                  color: Colors.white,
                  child: Padding(
                    padding:
                    EdgeInsets.only(left: 24.w, right: 24.w, top: 104.h),
                    child: SingleChildScrollView(
                      child: Column(
                        // crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                  height: 149.h,
                                  width: 156.w,
                                  decoration: BoxDecoration(
                                      color: const Color(0xFFF9F9F9),
                                      borderRadius:
                                      BorderRadius.circular(16)),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        left: 18.w, right: 18.w, top: 16.h),
                                    child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.start,
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                          children: [
                                            appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                                "aam"
                                                ? Container(
                                              height: 28.h,
                                              width: 28.w,
                                              decoration: BoxDecoration(
                                                  color: const Color(
                                                      0xFF1A181814)
                                                      .withOpacity(
                                                      0.08),
                                                  borderRadius:
                                                  BorderRadius
                                                      .circular(
                                                      10)),
                                              child: Center(
                                                  child: SvgPicture.string(
                                                      '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/> <path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>')),
                                            )
                                                : appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                                "rafco"
                                                ? Container(
                                                height: 28.h,
                                                width: 28.w,
                                                decoration: BoxDecoration(
                                                    color: const Color(
                                                        0xFF1A181814)
                                                        .withOpacity(
                                                        0.08),
                                                    borderRadius:
                                                    BorderRadius.circular(
                                                        10)),
                                                child: Center(
                                                  child: SvgPicture
                                                      .string(
                                                      '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#EA1B23" fill-opacity="0.5"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'),
                                                ))
                                                : Container(
                                                height: 28.h,
                                                width: 28.w,
                                                decoration: BoxDecoration(
                                                    color: const Color(
                                                        0xFF1A181814)
                                                        .withOpacity(
                                                        0.08),
                                                    borderRadius:
                                                    BorderRadius.circular(10)),
                                                child: Center(
                                                  child: SvgPicture
                                                      .string(
                                                    '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>',
                                                  ),
                                                )),
                                            InkWell(
                                              onTap: () {
                                                homeController.isGuest!.value
                                                    ? Get.to(
                                                        () => RegisterPage())
                                                    : Get.to(() =>
                                                const MrHistoryLoan());
                                                // Navigator.push(context, MaterialPageRoute(builder: (context) => page));
                                              },
                                              child: Container(
                                                height: 28.h,
                                                width: 28.w,
                                                child: Center(
                                                    child: SvgPicture.string(
                                                        '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>')),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(
                                          mrLoan.tr,
                                          style: TextStyle(
                                            color: configTheme()
                                                .textTheme
                                                .bodyMedium
                                                ?.color,
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                        const Spacer(),
                                        Text("0",
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: 24.sp,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            )),
                                        SizedBox(height: 16.h),
                                        // SizedBox(height: 16.h),
                                      ],
                                    ),
                                  )),
                              // buildContent("จัดสินเชื่อ",149.h,'<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/> <path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>','<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',"","","0","0","0",MrHistoryLoan()),
                              Container(
                                  height: 149.h,
                                  width: 156.w,
                                  decoration: BoxDecoration(
                                      color: const Color(0xFFF9F9F9),
                                      borderRadius:
                                      BorderRadius.circular(16)),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                        left: 18.w, right: 18.w, top: 16.h),
                                    child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.start,
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                          children: [
                                            appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                                "aam"
                                                ? Container(
                                              height: 28.h,
                                              width: 28.w,
                                              decoration: BoxDecoration(
                                                  color: const Color(
                                                      0xFF1A181814)
                                                      .withOpacity(
                                                      0.08),
                                                  borderRadius:
                                                  BorderRadius
                                                      .circular(
                                                      10)),
                                              child: Center(
                                                  child: SvgPicture.string(
                                                      '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="3" width="12" height="18" rx="2" fill="#792AFF" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/> <path d="M9 3V3C9 3.46499 9 3.69748 9.05111 3.88823C9.18981 4.40587 9.59413 4.81019 10.1118 4.94889C10.3025 5 10.535 5 11 5H13C13.465 5 13.6975 5 13.8882 4.94889C14.4059 4.81019 14.8102 4.40587 14.9489 3.88823C15 3.69748 15 3.46499 15 3V3" stroke="#1A1818" stroke-width="1.2"/> <circle cx="12" cy="18" r="1" fill="white"/></svg>')),
                                            )
                                                : appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                                "rafco"
                                                ? Container(
                                                height: 28.h,
                                                width: 28.w,
                                                decoration: BoxDecoration(
                                                    color: const Color(
                                                        0xFF1A181814)
                                                        .withOpacity(
                                                        0.08),
                                                    borderRadius:
                                                    BorderRadius.circular(
                                                        10)),
                                                child: Center(
                                                    child: SvgPicture.string(
                                                        '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#EA1B23" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>')))
                                                : Container(
                                                height: 28.h,
                                                width: 28.w,
                                                decoration: BoxDecoration(
                                                    color: const Color(
                                                        0xFF1A181814)
                                                        .withOpacity(0.08),
                                                    borderRadius: BorderRadius
                                                        .circular(10)),
                                                child: Center(
                                                    child: SvgPicture.string(
                                                      '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#6A7165" fill-opacity="0.75" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>',
                                                    ))),
                                            InkWell(
                                              onTap: () {
                                                homeController.isGuest!.value
                                                    ? Get.to(
                                                        () => RegisterPage())
                                                    : Get.to(() =>
                                                const MrHistoryLoadApp());
                                              },
                                              child: Container(
                                                height: 28.h,
                                                width: 28.w,
                                                child: Center(
                                                    child: SvgPicture.string(
                                                        '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>')),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 8.h),
                                        Text(mrApp.tr,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.color,
                                              fontSize: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontSize,
                                              fontFamily: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontFamily,
                                              fontWeight: configTheme()
                                                  .primaryTextTheme
                                                  .bodyMedium
                                                  ?.fontWeight,
                                            )),
                                        const Spacer(),
                                        Obx(() {
                                          return Text(
                                              mrController.referral_download
                                                  .length.toString() ?? "0",
                                              style: TextStyle(
                                                color: configTheme()
                                                    .textTheme
                                                    .bodyMedium
                                                    ?.color,
                                                fontSize: 24.sp,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .bodyMedium
                                                    ?.fontWeight,
                                              ));
                                        }),
                                        SizedBox(height: 16.h),
                                      ],
                                    ),
                                  )),
                            ],
                          ),

                          ///สมัครสมาชิกเข้ามาแบบ Guest
                          Container(
                              margin:
                              EdgeInsets.only(top: 16.h, bottom: 16.h),
                              height: 267.h,
                              // width: 156.w,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                    color: appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? Color(0xFF792AFF).withOpacity(0.08)
                                        : Color(0xFFEA1B23).withOpacity(0.05),
                                  ),
                                  borderRadius: BorderRadius.circular(16)),
                              child: Padding(
                                padding: EdgeInsets.only(top: 16.h),
                                child: Column(
                                  crossAxisAlignment:
                                  CrossAxisAlignment.center,
                                  // mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.15" d="M38.6435 66.8377C48.9297 66.8377 57.2684 58.4794 57.2684 48.1688C57.2684 37.8583 48.9297 29.5 38.6435 29.5C28.3572 29.5 20.0186 37.8583 20.0186 48.1688C20.0186 58.4794 28.3572 66.8377 38.6435 66.8377Z" fill="#792AFF" fill-opacity="0.75"/><path opacity="0.5" d="M38.6428 60.3294C45.3431 60.3294 50.7748 54.8849 50.7748 48.1687C50.7748 41.4526 45.3431 36.0081 38.6428 36.0081C31.9424 36.0081 26.5107 41.4526 26.5107 48.1687C26.5107 54.8849 31.9424 60.3294 38.6428 60.3294Z" fill="#792AFF" fill-opacity="0.25"/><path d="M108.011 66.9253C118.343 66.9253 126.719 58.5299 126.719 48.1737C126.719 37.8175 118.343 29.4221 108.011 29.4221C97.6794 29.4221 89.3037 37.8175 89.3037 48.1737C89.3037 58.5299 97.6794 66.9253 108.011 66.9253Z" fill="#FF9300" fill-opacity="0.15"/><path d="M108.01 60.3881C114.74 60.3881 120.196 54.9195 120.196 48.1736C120.196 41.4277 114.74 35.959 108.01 35.959C101.28 35.959 95.8242 41.4277 95.8242 48.1736C95.8242 54.9195 101.28 60.3881 108.01 60.3881Z" fill="#FF9300" fill-opacity="0.2"/><path d="M38.5814 21.0918C41.1334 18.5322 41.1334 14.3822 38.5814 11.8226C36.0293 9.26299 31.8917 9.26299 29.3396 11.8226C26.7876 14.3822 26.7876 18.5322 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3287_12501)"/><path d="M65.4118 33.0401C71.0548 33.0401 75.6293 28.452 75.6293 22.7923C75.6293 17.1326 71.0548 12.5444 65.4118 12.5444C59.7689 12.5444 55.1943 17.1326 55.1943 22.7923C55.1943 28.452 59.7689 33.0401 65.4118 33.0401Z" fill="url(#paint1_linear_3287_12501)"/><path d="M37.0957 18.144C37.0955 18.144 37.0954 18.144 37.0953 18.1438C37.0162 18.0111 35.5472 17.5388 35.1383 17.2602C35.1299 17.2546 35.1292 17.2425 35.1369 17.2359C35.7667 16.6989 36.1853 15.7971 36.1853 15.0008C36.1853 13.7699 35.1869 12.769 33.9597 12.769C32.7325 12.769 31.7345 13.7704 31.7345 15.0008C31.7345 15.7977 32.1537 16.6992 32.7832 17.2362C32.7907 17.2426 32.79 17.2544 32.782 17.2602C32.3729 17.5389 30.9019 18.0115 30.8239 18.144C30.4503 18.7792 30.9864 19.4892 31.7234 19.4892H36.6417C37.1544 19.4892 37.5277 18.9984 37.3053 18.5365C37.2412 18.4033 37.1715 18.2726 37.096 18.1442C37.096 18.1441 37.0958 18.144 37.0957 18.144Z" fill="white"/><path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3287_12501)"/><path d="M94.1137 9.20107C94.0478 9.08959 92.8136 8.69289 92.4704 8.4587C92.4634 8.45382 92.4627 8.444 92.4692 8.43848C92.9981 7.98748 93.3497 7.2304 93.3497 6.56135C93.3497 5.52767 92.5115 4.68695 91.4808 4.68695C90.4502 4.68695 89.612 5.52767 89.612 6.56135C89.612 7.2304 89.9635 7.98748 90.4925 8.43848C90.4989 8.444 90.4983 8.45382 90.4913 8.4587C90.1476 8.69289 88.9125 9.08959 88.8471 9.20107C88.5333 9.73445 88.9836 10.3308 89.6024 10.3308H93.3586C93.9775 10.3308 94.4271 9.73471 94.1137 9.20107Z" fill="white"/><path d="M70.2724 25.3874C70.1515 25.1818 67.8722 24.4493 67.238 24.017C67.2251 24.0084 67.2241 23.9898 67.2359 23.9797C68.2123 23.1468 68.8617 21.7491 68.8617 20.5136C68.8617 18.6055 67.3137 17.0529 65.4107 17.0529C63.5078 17.0529 61.9602 18.605 61.9602 20.5136C61.9602 21.7491 62.6096 23.1468 63.586 23.9797C63.5978 23.9898 63.5967 24.0082 63.5839 24.017C62.9493 24.4493 60.669 25.1818 60.5481 25.3874C59.9691 26.3724 60.8001 27.4731 61.9426 27.4731H68.8784C70.0209 27.4731 70.8519 26.372 70.2724 25.3874Z" fill="white"/><path d="M41.042 18.188L55.0281 22.5872" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/><path d="M74.7189 18.4761L87.7192 11.188" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/><path d="M20.3286 14.693C20.5369 14.9898 20.6918 15.3506 20.8035 15.7532C21.9194 19.7664 16.9092 23.2821 13.5144 20.8708C13.1728 20.6282 12.8854 20.3592 12.6765 20.0614C11.4751 18.3489 11.4246 16.1946 12.385 14.6048C12.6947 14.0917 13.1096 13.6376 13.6253 13.2758C15.7384 11.7933 18.7395 12.4279 20.3286 14.693Z" fill="#FF9300" fill-opacity="0.5"/><path d="M21.2509 22.7215C20.7846 22.6905 20.5819 23.6771 21.0055 23.4788C21.2676 23.3562 21.4619 22.8834 21.3432 22.7531C21.3258 22.7341 21.2935 22.7243 21.2509 22.7215Z" fill="#FF9300" fill-opacity="0.5"/><path d="M20.4968 22.9101L20.3717 22.9978C20.3449 23.0166 20.3076 23.0101 20.2888 22.9834C20.27 22.9566 20.2766 22.9193 20.3034 22.9005L20.4285 22.8127C20.4553 22.7939 20.4926 22.8004 20.5114 22.8272C20.5301 22.8539 20.5235 22.8913 20.4968 22.9101Z" fill="#FF9300" fill-opacity="0.5"/><path d="M25.955 28.7423C25.9221 28.8472 25.7719 28.861 25.6851 28.7938C25.6344 28.7546 25.5662 28.6649 25.5898 28.6052C25.8065 28.0577 26.4894 26.2007 26.1524 25.3343C25.9378 24.7825 24.4818 25.2637 23.312 25.6503C22.2802 25.9914 21.4619 26.4206 21.172 26.0656C20.9029 25.7361 21.3225 24.9288 21.6065 24.2812C21.7738 23.8999 21.9633 23.4676 21.883 23.3821C21.7853 23.278 21.4566 23.4615 21.1927 23.6091C20.8624 23.7936 20.5772 23.9529 20.4382 23.8163C20.207 23.5891 20.3435 23.0585 20.3707 22.9625C20.373 22.9545 20.3813 22.9503 20.3893 22.9527C20.3973 22.9551 20.4026 22.9636 20.4013 22.9719C20.3866 23.0641 20.3211 23.5603 20.5086 23.7446C20.594 23.8286 20.8989 23.6583 21.1438 23.5214C21.4838 23.3314 21.889 23.0812 22.0404 23.2426C22.1601 23.3701 21.9433 23.763 21.6985 24.3214C21.4393 24.9126 21.1167 25.6483 21.3411 25.9372C21.5011 26.1431 22.4058 25.8441 23.2807 25.5549C24.6161 25.1136 26.1147 24.56 26.3639 25.2005C26.5371 25.6458 26.4822 26.3962 26.2883 27.4468C26.1995 27.927 26.0505 28.438 25.955 28.7423Z" fill="#FF9300" fill-opacity="0.5"/><path d="M20.3284 14.6936C20.7731 15.3275 20.8737 16.1743 20.59 16.896C20.0851 18.181 18.6847 19.1051 16.4007 19.2781C10.3634 19.7352 12.383 14.6086 12.3842 14.6057C12.3842 14.6056 12.3841 14.6056 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2762C15.7377 11.7936 18.7391 12.4282 20.3284 14.6936Z" fill="#FF9300" fill-opacity="0.2"/><path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/><path d="M88.5735 38.1365C88.6486 38.8472 89.2536 39.5756 90.2316 40.8388C90.9251 41.7344 91.5834 42.0222 91.7717 42.5911C91.882 42.9927 91.2088 43.5772 90.9064 44.1631C90.6209 44.7153 91.3916 45.2729 91.5656 45.3895C91.5865 45.4035 91.6131 45.4029 91.6342 45.3892C91.6742 45.3633 91.6744 45.3035 91.6361 45.2752C91.5048 45.1781 91.2452 44.9631 91.0471 44.6538C90.7259 44.1528 91.8798 43.1803 91.9 42.7294C91.9006 42.7162 91.9006 42.7036 91.9006 42.6909C91.9002 42.1824 91.3956 41.8503 91.0381 41.4881C90.1963 40.635 88.87 39.1461 88.7547 38.0567C88.6614 37.1765 91.1163 36.9311 92.935 36.6438C94.5392 36.39 95.9203 36.4 96.0619 35.7298C96.1933 35.1077 95.0826 34.3224 94.2616 33.6563C93.7785 33.264 93.2303 32.8193 93.2784 32.6511C93.3366 32.4455 93.8989 32.4645 94.3505 32.4799C94.9158 32.4989 95.4041 32.5149 95.4948 32.2384C95.6137 31.8774 95.3037 31.4232 95.1157 31.192C95.0846 31.1538 95.1104 31.0792 95.1171 31.0304C95.1232 30.9814 95.1164 30.9068 95.1574 30.8792C96.4367 30.0173 102.717 25.6139 103.217 21.9348C103.638 18.836 102.228 15.9386 99.8629 14.4833C99.1172 14.0247 98.2766 13.7091 97.3686 13.5762C97.3475 13.5726 97.3258 13.5696 97.3041 13.5665C93.4815 13.0432 89.9314 15.9415 89.3743 20.0399C89.3013 20.5755 89.3433 21.1592 89.4695 21.7689C89.4707 21.7725 89.4711 21.7761 89.4719 21.7798C90.24 25.452 94.1004 30.0668 94.8161 30.8986C94.8165 30.8989 94.816 30.8993 94.8155 30.8992C94.8154 30.8992 94.8154 30.8992 94.8154 30.8991C94.7672 30.8931 94.7221 30.9268 94.7156 30.9754C94.7111 31.0097 94.6928 31.0645 94.6586 31.0701C94.2733 31.1327 93.6094 31.2655 93.5611 31.4454C93.4927 31.6996 94.074 32.1942 94.5062 32.1777C94.8619 32.164 94.92 31.3939 94.929 31.1235C94.9296 31.1076 94.9437 31.0958 94.9595 31.0979C94.9667 31.0989 94.9731 31.1025 94.9774 31.1085C95.0832 31.256 95.4697 31.8338 95.3521 32.1914C95.2963 32.3615 94.7749 32.3438 94.3558 32.3296C93.7737 32.3106 93.0644 32.2554 92.9737 32.5734C92.9022 32.8252 93.4594 33.1983 94.1672 33.773C94.9166 34.3807 96.052 35.1209 95.9126 35.6499C95.7601 36.2281 94.2719 36.2806 92.9114 36.4954C90.835 36.8239 88.4656 37.1144 88.5735 38.1365Z" fill="#792AFF" fill-opacity="0.25"/><path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#792AFF" fill-opacity="0.25"/><path d="M91.8283 17.5348C92.0697 17.7855 93.2592 17.5764 93.8571 17.0026C94.4551 16.4288 94.4226 15.6906 94.2156 15.4109C93.8193 14.8753 92.9492 15.3435 92.3513 15.9173C91.7533 16.491 91.587 17.284 91.8283 17.5348Z" fill="white"/><g clip-path="url(#clip0_3287_12501)"><path d="M119.681 56.8803C118.333 54.7508 116.217 54.1749 116.217 54.1749L111.147 52.2543H106.307L101.237 54.1749C101.237 54.1749 99.1211 54.7508 97.7735 56.8803H119.681Z" fill="#F9CDB1"/><path d="M102.115 66.9627H94.2816C94.2816 65.5966 94.3681 63.6465 94.3681 63.6465C94.8295 55.4606 100.665 54.1749 100.665 54.1749C105.217 53.0498 102.737 60.242 102.737 60.242C102.855 61.975 102.603 64.3081 102.113 66.96L102.115 66.9627Z" fill="url(#paint3_linear_3287_12501)"/><path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/><path d="M105.406 50.4757V47.3979H112.052V48.9221C110.156 50.5346 107.873 50.7382 105.406 50.4757Z" fill="#EAB894"/><path d="M98.3296 66.9627C98.2012 63.3947 98.2405 62.4144 98.272 61.1956C98.3401 58.4848 98.909 56.1919 100.658 54.1776C100.66 54.1776 100.666 54.1776 100.666 54.1776L105.403 52.5999C105.403 52.5999 104.407 54.3811 108.761 54.3811C108.761 54.3811 111.63 54.266 112.049 52.5999L116.86 54.1776C116.86 54.1776 120.973 55.2115 121.679 59.6339L119.261 64.1769L119.348 66.9654H98.3323L98.3296 66.9627Z" fill="url(#paint4_linear_3287_12501)"/><path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/><path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/><path d="M116.065 31.8619C117.827 32.7297 117.887 34.3342 117.803 36.1611C117.74 37.5781 117.31 38.7835 116.815 40.1576C116.252 41.7183 115.721 42.9478 115.221 43.8461C115.221 43.8461 114.054 45.6997 112.552 46.9694C112.987 45.2068 113.11 43.8916 113.506 42.5362C113.983 40.9103 113.845 38.7352 113.137 37.0665C113.137 37.0665 112.736 35.9548 111.089 35.6387L116.068 31.8592" fill="#1A1818"/><path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.2219C115.325 43.2219 114.18 45.1398 112.806 44.0764L113.17 42.188Z" fill="#F9CDB1"/><path d="M113.312 37.5379C113.312 37.5379 113.307 41.1085 112.664 43.0023L113.346 43.1416C113.346 43.1416 113.705 40.6933 114.938 40.6639L113.312 37.5406V37.5379Z" fill="#1A1818"/><path d="M99.9442 29.8931C94.2812 35.786 111.419 35.7271 111.419 35.7271C111.419 35.7271 113.218 36.5762 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0135C108.727 27.0135 103.371 26.3412 99.9442 29.8931Z" fill="#1A1818"/><path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3287_12501)"/></g><g clip-path="url(#clip1_3287_12501)"><path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1606 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2665 34.7656 34.7422Z" fill="#1A1818"/><path d="M37.0334 32.188C37.0334 32.188 36.2653 33.1206 37.3716 34.1837L36.6245 34.9215C36.6245 34.9215 34.9125 32.8721 37.0334 32.1906V32.188Z" fill="#1A1818"/><path d="M33.8818 57.3224C35.2294 55.2857 37.3451 54.7349 37.3451 54.7349L42.4155 52.8981H47.2552L52.3257 54.7349C52.3257 54.7349 54.4414 55.2857 55.789 57.3224H33.8818Z" fill="#F9CDB1"/><path d="M51.4472 66.9626H59.2809C59.2809 65.6561 59.1944 63.7911 59.1944 63.7911C58.733 55.962 52.897 54.7323 52.897 54.7323C48.3457 53.6564 50.8259 60.535 50.8259 60.535C50.7079 62.1925 50.9596 64.4239 51.4498 66.9601L51.4472 66.9626Z" fill="url(#paint6_linear_3287_12501)"/><path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/><path d="M48.1568 51.1944V48.2509H41.5107V49.7086C43.4062 51.2508 45.6898 51.4455 48.1568 51.1944Z" fill="#EAB894"/><path d="M55.2329 66.9626C55.3613 63.5502 55.322 62.6126 55.2905 61.4469C55.2224 58.8543 54.6535 56.6614 52.9048 54.7349C52.9021 54.7349 52.8969 54.7349 52.8969 54.7349L48.1594 53.226C48.1594 53.226 49.1557 54.9296 44.801 54.9296C44.801 54.9296 41.9329 54.8194 41.5134 53.226L36.7025 54.7349C36.7025 54.7349 32.589 55.7238 31.8838 59.9534L34.301 64.2983L34.2145 66.9652H55.2302L55.2329 66.9626Z" fill="url(#paint7_linear_3287_12501)"/><path d="M53.3222 40.3373C53.5109 38.8821 53.1937 37.4859 53.2094 36.0513C53.2199 35.088 53.0285 33.6355 51.8016 33.5125C50.1027 33.3434 49.5783 36.2076 49.4787 37.4501C49.3398 39.1614 49.775 41.011 50.1132 42.6865C50.2338 43.2911 50.2233 44.5003 50.918 44.7923C51.5132 45.0408 52.124 44.4542 52.4281 44.0161C53.1622 42.9606 53.1648 41.5567 53.3195 40.3373H53.3222Z" fill="#1A1818"/><path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/><path d="M53.7288 30.2411C54.0146 31.7756 53.7472 33.902 52.5307 34.8832C51.2093 35.9463 50.0741 35.6568 48.4827 36.0027C46.8284 36.3613 45.1348 36.5048 43.4726 36.8174C42.3086 37.0377 40.9636 37.1632 40.4288 38.3698C39.7209 39.9659 39.582 42.0461 40.0591 43.6012C40.455 44.8949 40.5782 46.1553 41.0135 47.841C39.5112 46.6267 38.3445 44.8539 38.3445 44.8539C37.8447 43.9948 37.3133 42.8189 36.7505 41.3262C36.255 40.012 35.8277 38.8592 35.7621 37.5039C35.6809 35.7568 36.546 34.9959 37.5003 33.3922C38.397 31.8858 39.6554 30.8405 41.2966 30.364C41.2966 30.364 49.6127 30.8201 49.458 28.9653C49.458 28.9653 51.4558 29.4648 51.0756 30.7611C51.0756 30.7611 53.1651 30.3871 52.7221 28.9653C52.7221 28.9653 53.5663 29.3547 53.734 30.2436L53.7288 30.2411Z" fill="#1A1818"/><path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/><path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/><path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3287_12501)"/></g><g clip-path="url(#clip2_3287_12501)"><path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/><path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/><path d="M81.8041 54.8701H72.3047V59.4332H81.8041V54.8701Z" fill="#F9CDB1"/><path d="M92.2785 66.9626H85.8549L84.7075 62.4622C84.7075 62.4622 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9626H92.2785Z" fill="#F9CDB1"/><path d="M69.9639 66.9627L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6858 64.2944 66.9627H69.9639Z" fill="#F9CDB1"/><path d="M80.6781 45.655H74.8174V53.9815H80.6781V45.655Z" fill="#F9CDB1"/><path d="M80.6781 49.0551V45.6575H74.8174V50.5526C76.7512 50.4951 79.0459 50.1553 80.6781 49.0551Z" fill="#EAB894"/><path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/><path d="M68.2589 63.4057V66.9627H87.2847L87.0612 62.8308C86.8241 54.8701 87.7426 54.8675 87.7426 54.8675C86.8861 54.3134 86.1697 54.1279 86.1697 54.1279L81.3997 52.8107C81.3997 52.8107 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5313 74.2085 52.7689L70.4298 54.1279C70.4298 54.1279 68.9323 54.5722 67.5371 55.8867C67.5371 55.8867 68.2104 56.2866 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3287_12501)"/><path d="M81.3168 46.3187C81.1175 46.7683 80.8374 47.1838 80.4469 47.5497C79.4746 48.4592 78.3353 49.1962 77.0856 49.6927C75.5531 50.3043 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6611 70.0613 45.37C69.2291 42.8611 69.2399 40.1222 69.4499 37.5192C69.4984 36.9259 69.5604 36.33 69.6627 35.742C70.6189 30.0629 75.6285 30.0733 75.6285 30.0733C79.4099 29.9139 82.8628 31.6467 83.4446 35.5146C83.6681 36.9965 83.3745 38.5855 83.2964 40.0673C83.2399 41.1545 83.1025 42.2417 82.7443 43.2793C82.378 44.3377 81.7747 45.2943 81.3195 46.3161L81.3168 46.3187Z" fill="#F9CDB1"/><path d="M81.8064 41.9882C81.8064 41.9882 82.3074 39.968 83.9072 40.8017C83.9072 40.8017 84.629 41.1571 84.1092 42.5449C84.1092 42.5449 83.3497 44.5416 81.8037 43.8124V41.9882H81.8064Z" fill="#F9CDB1"/><path d="M69.0724 36.3928C68.8031 28.5942 76.4279 28.9653 76.4279 28.9653C83.9235 29.2632 84.2979 32.5588 84.2979 32.5588C85.2055 36.7404 83.9073 40.8017 83.9073 40.8017C82.3129 40.4646 82.0435 42.5318 82.0435 42.5318C82.2105 39.2179 81.9304 39.0167 81.9304 39.0167C79.4983 38.9932 77.745 38.7397 77.745 38.7397L77.6372 36.4294L77.0501 38.6195C77.0501 38.6195 73.6026 38.5437 70.0232 37.7204C70.0232 37.7204 69.0697 37.7858 69.0697 40.6005V36.3928H69.0724Z" fill="#1A1818"/></g><g clip-path="url(#clip3_3287_12501)"><path d="M11.9798 51.8379C13.2186 51.3943 13.9996 50.3714 15.4728 50.4989C18.0904 50.7274 19.946 53.6286 22.4667 51.6599C25.5153 49.2742 24.7154 34.9304 23.3931 31.6812C22.2728 28.9262 20.2045 31.6201 17.2583 30.6929C13.8327 29.6169 10.8784 32.2843 9.10631 35.0526C7.26424 37.9352 6.31358 41.6068 6.58558 44.9915C6.83604 48.1158 7.40967 53.4745 11.9825 51.8353L11.9798 51.8379Z" fill="#1A1818"/><path d="M0.283728 67H6.70673L7.85399 62.4251C7.85399 62.4251 8.78849 55.4803 3.95979 55.382C3.95979 55.382 1.45522 57.1142 0.771176 61.1923C0.771176 61.1923 0.229866 64.4495 0.281034 67H0.283728Z" fill="url(#paint10_linear_3287_12501)"/><path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3287_12501)"/><path d="M11.883 45.3395H17.7432V53.8039H11.883V45.3395Z" fill="#F9CDB1"/><path d="M11.883 48.7959V45.3422H17.7432V50.3183C15.8095 50.2598 13.515 49.9144 11.883 48.7959Z" fill="#EAB894"/><path d="M25.5127 56.5058C24.1985 54.4973 22.1302 53.9554 22.1302 53.9554L17.1776 52.1461H12.4512L6.39449 53.9554C6.39449 53.9554 4.3262 54.5 3.01197 56.5058H25.5127Z" fill="#F9CDB1"/><path d="M24.3005 67H4.78371L4.90759 63.3842L2.03677 59.1015C2.67503 54.9304 6.39148 53.9554 6.39148 53.9554L11.1609 52.6164C11.1609 52.6164 10.8755 54.1493 14.8128 54.1493C14.8128 54.1493 17.9744 54.1493 18.3515 52.5765L22.1299 53.958C22.1299 53.958 24.8984 54.7923 26.4092 57.5526L24.2978 63.3868V67.0027L24.3005 67Z" fill="url(#paint12_linear_3287_12501)"/><path d="M11.2449 46.0144C11.4442 46.4713 11.7243 46.8938 12.1148 47.2657C13.087 48.1903 14.2261 48.9395 15.4757 49.4442C17.0081 50.0659 19.3214 50.143 20.5414 48.8996C21.5298 47.89 22.0711 46.3624 22.4993 45.05C23.3315 42.4995 23.3207 39.7152 23.1106 37.0691C23.0621 36.466 23.0002 35.8603 22.8979 35.2625C21.9445 29.4894 16.9354 29.5 16.9354 29.5C13.1543 29.338 9.70175 31.0994 9.12004 35.0314C8.89652 36.5378 9.19007 38.1531 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9246C10.1865 44.0006 10.7898 44.9729 11.2449 46.0117V46.0144Z" fill="#F9CDB1"/><path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/><path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs><linearGradient id="paint0_linear_3287_12501" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint1_linear_3287_12501" x1="65.4118" y1="12.5444" x2="65.4118" y2="33.0401" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint2_linear_3287_12501" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint3_linear_3287_12501" x1="98.8464" y1="54.0554" x2="98.8464" y2="66.9627" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint4_linear_3287_12501" x1="109.96" y1="52.5999" x2="109.96" y2="66.9654" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint5_linear_3287_12501" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint6_linear_3287_12501" x1="54.7161" y1="54.6181" x2="54.7161" y2="66.9626" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint7_linear_3287_12501" x1="43.6028" y1="53.226" x2="43.6028" y2="66.9652" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint8_linear_3287_12501" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint9_linear_3287_12501" x1="77.6398" y1="52.7689" x2="77.6398" y2="66.9627" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint10_linear_3287_12501" x1="4.10219" y1="55.382" x2="4.10219" y2="67" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint11_linear_3287_12501" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint12_linear_3287_12501" x1="14.223" y1="52.5765" x2="14.223" y2="67.0027" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><clipPath id="clip0_3287_12501"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath><clipPath id="clip1_3287_12501"><rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip2_3287_12501"><rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip3_3287_12501"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>'))
                                        : appConfigService
                                        .countryConfigCollection ==
                                        "rafco"
                                        ? Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.15" d="M38.6435 66.8377C48.9297 66.8377 57.2684 58.4794 57.2684 48.1688C57.2684 37.8583 48.9297 29.5 38.6435 29.5C28.3572 29.5 20.0186 37.8583 20.0186 48.1688C20.0186 58.4794 28.3572 66.8377 38.6435 66.8377Z" fill="#EA1B23" fill-opacity="0.75"/> <path opacity="0.5" d="M38.6428 60.3294C45.3431 60.3294 50.7748 54.8849 50.7748 48.1688C50.7748 41.4527 45.3431 36.0082 38.6428 36.0082C31.9424 36.0082 26.5107 41.4527 26.5107 48.1688C26.5107 54.8849 31.9424 60.3294 38.6428 60.3294Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M108.011 66.9254C118.343 66.9254 126.719 58.53 126.719 48.1738C126.719 37.8175 118.343 29.4221 108.011 29.4221C97.6794 29.4221 89.3037 37.8175 89.3037 48.1738C89.3037 58.53 97.6794 66.9254 108.011 66.9254Z" fill="#22409A" fill-opacity="0.15"/> <path d="M108.01 60.3882C114.74 60.3882 120.196 54.9196 120.196 48.1737C120.196 41.4277 114.74 35.9591 108.01 35.9591C101.28 35.9591 95.8242 41.4277 95.8242 48.1737C95.8242 54.9196 101.28 60.3882 108.01 60.3882Z" fill="#22409A" fill-opacity="0.15"/> <path d="M38.5814 21.0918C41.1334 18.5321 41.1334 14.3822 38.5814 11.8226C36.0293 9.26293 31.8917 9.26293 29.3396 11.8226C26.7876 14.3822 26.7876 18.5321 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3898_67412)"/> <path d="M65.4118 33.0401C71.0548 33.0401 75.6293 28.452 75.6293 22.7923C75.6293 17.1326 71.0548 12.5444 65.4118 12.5444C59.7689 12.5444 55.1943 17.1326 55.1943 22.7923C55.1943 28.452 59.7689 33.0401 65.4118 33.0401Z" fill="url(#paint1_linear_3898_67412)"/> <path d="M37.0957 18.144C37.0955 18.144 37.0954 18.144 37.0953 18.1438C37.0162 18.0111 35.5472 17.5388 35.1383 17.2602C35.1299 17.2546 35.1292 17.2425 35.1369 17.2359C35.7667 16.6989 36.1853 15.7971 36.1853 15.0008C36.1853 13.7699 35.1869 12.769 33.9597 12.769C32.7325 12.769 31.7345 13.7704 31.7345 15.0008C31.7345 15.7977 32.1537 16.6992 32.7832 17.2362C32.7907 17.2426 32.79 17.2544 32.782 17.2602C32.3729 17.5389 30.9019 18.0115 30.8239 18.144C30.4503 18.7792 30.9864 19.4892 31.7234 19.4892H36.6417C37.1544 19.4892 37.5277 18.9984 37.3053 18.5365C37.2412 18.4033 37.1715 18.2726 37.096 18.1442C37.096 18.1441 37.0958 18.144 37.0957 18.144Z" fill="white"/> <path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3898_67412)"/> <path d="M94.1137 9.20107C94.0478 9.08959 92.8136 8.69289 92.4704 8.4587C92.4634 8.45382 92.4627 8.444 92.4692 8.43848C92.9981 7.98748 93.3497 7.2304 93.3497 6.56135C93.3497 5.52767 92.5115 4.68695 91.4808 4.68695C90.4502 4.68695 89.612 5.52767 89.612 6.56135C89.612 7.2304 89.9635 7.98748 90.4925 8.43848C90.4989 8.444 90.4983 8.45382 90.4913 8.4587C90.1476 8.69289 88.9125 9.08959 88.8471 9.20107C88.5333 9.73445 88.9836 10.3308 89.6024 10.3308H93.3586C93.9775 10.3308 94.4271 9.73471 94.1137 9.20107Z" fill="white"/> <path d="M70.2724 25.3874C70.1515 25.1818 67.8722 24.4493 67.238 24.017C67.2251 24.0084 67.2241 23.9898 67.2359 23.9797C68.2123 23.1468 68.8617 21.7491 68.8617 20.5136C68.8617 18.6055 67.3137 17.0529 65.4107 17.0529C63.5078 17.0529 61.9602 18.605 61.9602 20.5136C61.9602 21.7491 62.6096 23.1468 63.586 23.9797C63.5978 23.9898 63.5967 24.0082 63.5839 24.017C62.9493 24.4493 60.669 25.1818 60.5481 25.3874C59.9691 26.3724 60.8001 27.4731 61.9426 27.4731H68.8784C70.0209 27.4731 70.8519 26.372 70.2724 25.3874Z" fill="white"/> <path d="M41.042 18.188L55.0281 22.5872" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M74.7189 18.4761L87.7192 11.188" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M20.3286 14.6929C20.5369 14.9898 20.6918 15.3506 20.8035 15.7531C21.9194 19.7664 16.9092 23.2821 13.5144 20.8708C13.1728 20.6281 12.8854 20.3591 12.6765 20.0614C11.4751 18.3488 11.4246 16.1946 12.385 14.6048C12.6947 14.0916 13.1096 13.6375 13.6253 13.2757C15.7384 11.7933 18.7395 12.4278 20.3286 14.6929Z" fill="#22409A" fill-opacity="0.15"/> <path d="M21.2509 22.7214C20.7846 22.6904 20.5819 23.677 21.0055 23.4788C21.2676 23.3561 21.4619 22.8833 21.3432 22.753C21.3258 22.734 21.2935 22.7242 21.2509 22.7214Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.4968 22.9101L20.3717 22.9978C20.3449 23.0166 20.3076 23.0101 20.2888 22.9834C20.27 22.9566 20.2766 22.9193 20.3034 22.9005L20.4285 22.8127C20.4553 22.7939 20.4926 22.8004 20.5114 22.8272C20.5301 22.8539 20.5235 22.8913 20.4968 22.9101Z" fill="#22409A" fill-opacity="0.15"/> <path d="M25.955 28.7423C25.9221 28.8472 25.7719 28.861 25.6851 28.7938C25.6344 28.7546 25.5662 28.6649 25.5898 28.6052C25.8065 28.0577 26.4894 26.2007 26.1524 25.3343C25.9378 24.7825 24.4818 25.2637 23.312 25.6503C22.2802 25.9914 21.4619 26.4206 21.172 26.0656C20.9029 25.7361 21.3225 24.9288 21.6065 24.2812C21.7738 23.8999 21.9633 23.4676 21.883 23.3821C21.7853 23.278 21.4566 23.4615 21.1927 23.6091C20.8624 23.7936 20.5772 23.9529 20.4382 23.8163C20.207 23.5891 20.3435 23.0585 20.3707 22.9625C20.373 22.9545 20.3813 22.9503 20.3893 22.9527C20.3973 22.9551 20.4026 22.9636 20.4013 22.9719C20.3866 23.0641 20.3211 23.5603 20.5086 23.7446C20.594 23.8286 20.8989 23.6583 21.1438 23.5214C21.4838 23.3314 21.889 23.0812 22.0404 23.2426C22.1601 23.3701 21.9433 23.763 21.6985 24.3214C21.4393 24.9126 21.1167 25.6483 21.3411 25.9372C21.5011 26.1431 22.4058 25.8441 23.2807 25.5549C24.6161 25.1136 26.1147 24.56 26.3639 25.2005C26.5371 25.6458 26.4822 26.3962 26.2883 27.4468C26.1995 27.927 26.0505 28.438 25.955 28.7423Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.3284 14.6936C20.7731 15.3276 20.8737 16.1744 20.59 16.8961C20.0851 18.181 18.6847 19.1052 16.4007 19.2781C10.3634 19.7353 12.383 14.6087 12.3842 14.6057C12.3842 14.6056 12.3841 14.6057 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2763C15.7377 11.7936 18.7391 12.4283 20.3284 14.6936Z" fill="#22409A" fill-opacity="0.15"/> <path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/> <path d="M88.5735 38.1365C88.6486 38.8472 89.2536 39.5756 90.2316 40.8388C90.9251 41.7344 91.5834 42.0222 91.7717 42.5911C91.882 42.9927 91.2088 43.5772 90.9064 44.1631C90.6209 44.7153 91.3916 45.2729 91.5656 45.3895C91.5865 45.4035 91.6131 45.4029 91.6342 45.3892C91.6742 45.3633 91.6744 45.3035 91.6361 45.2752C91.5048 45.1781 91.2452 44.9631 91.0471 44.6538C90.7259 44.1528 91.8798 43.1803 91.9 42.7294C91.9006 42.7162 91.9006 42.7036 91.9006 42.6909C91.9002 42.1824 91.3956 41.8503 91.0381 41.4881C90.1963 40.635 88.87 39.1461 88.7547 38.0567C88.6614 37.1765 91.1163 36.9311 92.935 36.6438C94.5392 36.39 95.9203 36.4 96.0619 35.7298C96.1933 35.1077 95.0826 34.3224 94.2616 33.6563C93.7785 33.264 93.2303 32.8193 93.2784 32.6511C93.3366 32.4455 93.8989 32.4645 94.3505 32.4799C94.9158 32.4989 95.4041 32.5149 95.4948 32.2384C95.6137 31.8774 95.3037 31.4232 95.1157 31.192C95.0846 31.1538 95.1104 31.0792 95.1171 31.0304C95.1232 30.9814 95.1164 30.9068 95.1574 30.8792C96.4367 30.0173 102.717 25.6139 103.217 21.9348C103.638 18.836 102.228 15.9386 99.8629 14.4833C99.1172 14.0247 98.2766 13.7091 97.3686 13.5762C97.3475 13.5726 97.3258 13.5696 97.3041 13.5665C93.4815 13.0432 89.9314 15.9415 89.3743 20.0399C89.3013 20.5755 89.3433 21.1592 89.4695 21.7689C89.4707 21.7725 89.4711 21.7761 89.4719 21.7798C90.24 25.452 94.1004 30.0668 94.8161 30.8986C94.8165 30.8989 94.816 30.8993 94.8155 30.8992C94.8154 30.8992 94.8154 30.8992 94.8154 30.8991C94.7672 30.8931 94.7221 30.9268 94.7156 30.9754C94.7111 31.0097 94.6928 31.0645 94.6586 31.0701C94.2733 31.1327 93.6094 31.2655 93.5611 31.4454C93.4927 31.6996 94.074 32.1942 94.5062 32.1777C94.8619 32.164 94.92 31.3939 94.929 31.1235C94.9296 31.1076 94.9437 31.0958 94.9595 31.0979C94.9667 31.0989 94.9731 31.1025 94.9774 31.1085C95.0832 31.256 95.4697 31.8338 95.3521 32.1914C95.2963 32.3615 94.7749 32.3438 94.3558 32.3296C93.7737 32.3106 93.0644 32.2554 92.9737 32.5734C92.9022 32.8252 93.4594 33.1983 94.1672 33.773C94.9166 34.3807 96.052 35.1209 95.9126 35.6499C95.7601 36.2281 94.2719 36.2806 92.9114 36.4954C90.835 36.8239 88.4656 37.1144 88.5735 38.1365Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M91.8283 17.5349C92.0697 17.7856 93.2592 17.5765 93.8571 17.0027C94.4551 16.4289 94.4226 15.6907 94.2156 15.411C93.8193 14.8753 92.9492 15.3435 92.3513 15.9174C91.7533 16.4911 91.587 17.2841 91.8283 17.5349Z" fill="white"/> <g clip-path="url(#clip0_3898_67412)"> <path d="M119.681 56.8803C118.333 54.7508 116.217 54.1749 116.217 54.1749L111.147 52.2543H106.307L101.237 54.1749C101.237 54.1749 99.1211 54.7508 97.7735 56.8803H119.681Z" fill="#F9CDB1"/> <path d="M102.115 66.9627H94.2816C94.2816 65.5966 94.3681 63.6465 94.3681 63.6465C94.8295 55.4606 100.665 54.1749 100.665 54.1749C105.217 53.0498 102.737 60.242 102.737 60.242C102.855 61.975 102.603 64.3081 102.113 66.96L102.115 66.9627Z" fill="url(#paint3_linear_3898_67412)"/> <path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/> <path d="M105.406 50.4757V47.3979H112.052V48.9221C110.156 50.5346 107.873 50.7382 105.406 50.4757Z" fill="#EAB894"/> <path d="M98.3296 66.9627C98.2012 63.3947 98.2405 62.4144 98.272 61.1956C98.3401 58.4848 98.909 56.1919 100.658 54.1776C100.66 54.1776 100.666 54.1776 100.666 54.1776L105.403 52.5999C105.403 52.5999 104.407 54.3811 108.761 54.3811C108.761 54.3811 111.63 54.266 112.049 52.5999L116.86 54.1776C116.86 54.1776 120.973 55.2115 121.679 59.6339L119.261 64.1769L119.348 66.9654H98.3323L98.3296 66.9627Z" fill="url(#paint4_linear_3898_67412)"/> <path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/> <path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/> <path d="M116.065 31.8618C117.827 32.7297 117.887 34.3342 117.803 36.161C117.74 37.578 117.31 38.7834 116.815 40.1575C116.252 41.7183 115.721 42.9478 115.221 43.846C115.221 43.846 114.054 45.6996 112.552 46.9693C112.987 45.2068 113.11 43.8915 113.506 42.5362C113.983 40.9102 113.845 38.7352 113.137 37.0664C113.137 37.0664 112.736 35.9548 111.089 35.6387L116.068 31.8591" fill="#1A1818"/> <path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.222C115.325 43.222 114.18 45.1399 112.806 44.0765L113.17 42.188Z" fill="#F9CDB1"/> <path d="M113.312 37.5378C113.312 37.5378 113.307 41.1085 112.664 43.0022L113.346 43.1415C113.346 43.1415 113.705 40.6933 114.938 40.6638L113.312 37.5405V37.5378Z" fill="#1A1818"/> <path d="M99.9442 29.8931C94.2812 35.7861 111.419 35.7272 111.419 35.7272C111.419 35.7272 113.218 36.5763 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0136C108.727 27.0136 103.371 26.3413 99.9442 29.8931Z" fill="#1A1818"/> <path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3898_67412)"/></g> <g clip-path="url(#clip1_3898_67412)"> <path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1607 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2666 34.7656 34.7422Z" fill="#1A1818"/> <path d="M37.0334 32.188C37.0334 32.188 36.2653 33.1205 37.3716 34.1837L36.6245 34.9215C36.6245 34.9215 34.9125 32.872 37.0334 32.1906V32.188Z" fill="#1A1818"/> <path d="M33.8818 57.3224C35.2294 55.2857 37.3451 54.7349 37.3451 54.7349L42.4155 52.8981H47.2552L52.3257 54.7349C52.3257 54.7349 54.4414 55.2857 55.789 57.3224H33.8818Z" fill="#F9CDB1"/> <path d="M51.4472 66.9627H59.2809C59.2809 65.6562 59.1944 63.7911 59.1944 63.7911C58.733 55.9621 52.897 54.7324 52.897 54.7324C48.3457 53.6564 50.8259 60.535 50.8259 60.535C50.7079 62.1925 50.9596 64.4239 51.4498 66.9601L51.4472 66.9627Z" fill="url(#paint6_linear_3898_67412)"/> <path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/> <path d="M48.1568 51.1944V48.2509H41.5107V49.7086C43.4062 51.2508 45.6898 51.4455 48.1568 51.1944Z" fill="#EAB894"/> <path d="M55.2329 66.9626C55.3613 63.5502 55.322 62.6126 55.2905 61.4469C55.2224 58.8543 54.6535 56.6614 52.9048 54.7349C52.9021 54.7349 52.8969 54.7349 52.8969 54.7349L48.1594 53.226C48.1594 53.226 49.1557 54.9296 44.801 54.9296C44.801 54.9296 41.9329 54.8194 41.5134 53.226L36.7025 54.7349C36.7025 54.7349 32.589 55.7238 31.8838 59.9534L34.301 64.2983L34.2145 66.9652H55.2302L55.2329 66.9626Z" fill="url(#paint7_linear_3898_67412)"/> <path d="M53.3222 40.3373C53.5109 38.8822 53.1937 37.486 53.2094 36.0513C53.2199 35.0881 53.0285 33.6355 51.8016 33.5125C50.1027 33.3435 49.5783 36.2076 49.4787 37.4501C49.3398 39.1614 49.775 41.0111 50.1132 42.6865C50.2338 43.2911 50.2233 44.5003 50.918 44.7924C51.5132 45.0409 52.124 44.4542 52.4281 44.0162C53.1622 42.9607 53.1648 41.5568 53.3195 40.3373H53.3222Z" fill="#1A1818"/> <path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/> <path d="M53.7288 30.241C54.0146 31.7756 53.7472 33.9019 52.5307 34.8831C51.2093 35.9463 50.0741 35.6568 48.4827 36.0026C46.8284 36.3613 45.1348 36.5048 43.4726 36.8173C42.3086 37.0376 40.9636 37.1631 40.4288 38.3698C39.7209 39.9658 39.582 42.046 40.0591 43.6011C40.455 44.8948 40.5782 46.1553 41.0135 47.841C39.5112 46.6266 38.3445 44.8538 38.3445 44.8538C37.8447 43.9948 37.3133 42.8189 36.7505 41.3262C36.255 40.0119 35.8277 38.8591 35.7621 37.5039C35.6809 35.7567 36.546 34.9958 37.5003 33.3921C38.397 31.8857 39.6554 30.8405 41.2966 30.364C41.2966 30.364 49.6127 30.82 49.458 28.9652C49.458 28.9652 51.4558 29.4648 51.0756 30.7611C51.0756 30.7611 53.1651 30.387 52.7221 28.9652C52.7221 28.9652 53.5663 29.3546 53.734 30.2436L53.7288 30.241Z" fill="#1A1818"/> <path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/> <path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/> <path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3898_67412)"/></g> <g clip-path="url(#clip2_3898_67412)"> <path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/> <path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/> <path d="M81.8041 54.8701H72.3047V59.4333H81.8041V54.8701Z" fill="#F9CDB1"/> <path d="M92.2785 66.9627H85.8549L84.7075 62.4623C84.7075 62.4623 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9627H92.2785Z" fill="#F9CDB1"/> <path d="M69.9639 66.9626L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6857 64.2944 66.9626H69.9639Z" fill="#F9CDB1"/> <path d="M80.6781 45.6549H74.8174V53.9815H80.6781V45.6549Z" fill="#F9CDB1"/> <path d="M80.6781 49.0551V45.6576H74.8174V50.5526C76.7512 50.4951 79.0459 50.1554 80.6781 49.0551Z" fill="#EAB894"/> <path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/> <path d="M68.2589 63.4057V66.9626H87.2847L87.0612 62.8307C86.8241 54.87 87.7426 54.8674 87.7426 54.8674C86.8861 54.3134 86.1697 54.1278 86.1697 54.1278L81.3997 52.8106C81.3997 52.8106 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5312 74.2085 52.7688L70.4298 54.1278C70.4298 54.1278 68.9323 54.5721 67.5371 55.8867C67.5371 55.8867 68.2104 56.2865 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3898_67412)"/> <path d="M81.3168 46.3188C81.1175 46.7683 80.8374 47.1839 80.4469 47.5498C79.4746 48.4592 78.3353 49.1962 77.0856 49.6928C75.5531 50.3044 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6612 70.0613 45.3701C69.2291 42.8612 69.2399 40.1222 69.4499 37.5192C69.4984 36.926 69.5604 36.3301 69.6627 35.742C70.6189 30.063 75.6285 30.0734 75.6285 30.0734C79.4099 29.914 82.8628 31.6467 83.4446 35.5147C83.6681 36.9965 83.3745 38.5855 83.2964 40.0674C83.2399 41.1546 83.1025 42.2418 82.7443 43.2793C82.378 44.3378 81.7747 45.2943 81.3195 46.3162L81.3168 46.3188Z" fill="#F9CDB1"/> <path d="M81.8064 41.9882C81.8064 41.9882 82.3074 39.968 83.9072 40.8017C83.9072 40.8017 84.629 41.1571 84.1092 42.5449C84.1092 42.5449 83.3497 44.5416 81.8037 43.8124V41.9882H81.8064Z" fill="#F9CDB1"/> <path d="M69.0724 36.3927C68.8031 28.5941 76.4279 28.9652 76.4279 28.9652C83.9235 29.2631 84.2979 32.5587 84.2979 32.5587C85.2055 36.7403 83.9073 40.8017 83.9073 40.8017C82.3129 40.4645 82.0435 42.5318 82.0435 42.5318C82.2105 39.2179 81.9304 39.0167 81.9304 39.0167C79.4983 38.9931 77.745 38.7396 77.745 38.7396L77.6372 36.4293L77.0501 38.6194C77.0501 38.6194 73.6026 38.5436 70.0232 37.7204C70.0232 37.7204 69.0697 37.7857 69.0697 40.6004V36.3927H69.0724Z" fill="#1A1818"/></g> <g clip-path="url(#clip3_3898_67412)"> <path d="M11.9798 51.8379C13.2186 51.3943 13.9996 50.3714 15.4728 50.4989C18.0904 50.7274 19.946 53.6286 22.4667 51.6599C25.5153 49.2742 24.7154 34.9304 23.3931 31.6812C22.2728 28.9262 20.2045 31.6201 17.2583 30.6929C13.8327 29.6169 10.8784 32.2843 9.10631 35.0526C7.26424 37.9352 6.31358 41.6068 6.58558 44.9915C6.83604 48.1158 7.40967 53.4745 11.9825 51.8353L11.9798 51.8379Z" fill="#1A1818"/> <path d="M0.283728 67H6.70673L7.85399 62.4251C7.85399 62.4251 8.78849 55.4804 3.95979 55.3821C3.95979 55.3821 1.45522 57.1143 0.771176 61.1924C0.771176 61.1924 0.229866 64.4496 0.281034 67H0.283728Z" fill="url(#paint10_linear_3898_67412)"/> <path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3898_67412)"/> <path d="M11.883 45.3395H17.7432V53.8039H11.883V45.3395Z" fill="#F9CDB1"/> <path d="M11.883 48.7959V45.3422H17.7432V50.3183C15.8095 50.2598 13.515 49.9144 11.883 48.7959Z" fill="#EAB894"/> <path d="M25.5127 56.5058C24.1985 54.4973 22.1302 53.9554 22.1302 53.9554L17.1776 52.1461H12.4512L6.39449 53.9554C6.39449 53.9554 4.3262 54.5 3.01197 56.5058H25.5127Z" fill="#F9CDB1"/> <path d="M24.3005 67H4.78371L4.90759 63.3842L2.03677 59.1015C2.67503 54.9304 6.39148 53.9554 6.39148 53.9554L11.1609 52.6164C11.1609 52.6164 10.8755 54.1493 14.8128 54.1493C14.8128 54.1493 17.9744 54.1493 18.3515 52.5765L22.1299 53.958C22.1299 53.958 24.8984 54.7923 26.4092 57.5526L24.2978 63.3868V67.0027L24.3005 67Z" fill="url(#paint12_linear_3898_67412)"/> <path d="M11.2449 46.0143C11.4442 46.4713 11.7243 46.8937 12.1148 47.2656C13.087 48.1902 14.2261 48.9394 15.4757 49.4442C17.0081 50.0659 19.3214 50.1429 20.5414 48.8995C21.5298 47.89 22.0711 46.3623 22.4993 45.0499C23.3315 42.4994 23.3207 39.7152 23.1106 37.069C23.0621 36.466 23.0002 35.8602 22.8979 35.2625C21.9445 29.4893 16.9354 29.5 16.9354 29.5C13.1543 29.3379 9.70175 31.0993 9.12004 35.0313C8.89652 36.5377 9.19007 38.153 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9245C10.1865 44.0005 10.7898 44.9729 11.2449 46.0117V46.0143Z" fill="#F9CDB1"/> <path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/> <path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs> <linearGradient id="paint0_linear_3898_67412" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint1_linear_3898_67412" x1="65.4118" y1="12.5444" x2="65.4118" y2="33.0401" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint2_linear_3898_67412" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint3_linear_3898_67412" x1="98.8464" y1="54.0554" x2="98.8464" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint4_linear_3898_67412" x1="109.96" y1="52.5999" x2="109.96" y2="66.9654" gradientUnits="userSpaceOnUse"><stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint5_linear_3898_67412" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint6_linear_3898_67412" x1="54.7161" y1="54.6182" x2="54.7161" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint7_linear_3898_67412" x1="43.6028" y1="53.226" x2="43.6028" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint8_linear_3898_67412" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint9_linear_3898_67412" x1="77.6398" y1="52.7688" x2="77.6398" y2="66.9626" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint10_linear_3898_67412" x1="4.10219" y1="55.3821" x2="4.10219" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint11_linear_3898_67412" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient><linearGradient id="paint12_linear_3898_67412" x1="14.223" y1="52.5765" x2="14.223" y2="67.0027" gradientUnits="userSpaceOnUse"><stop stop-color="#4A77FF"/><stop offset="1" stop-color="#22409A"/></linearGradient><clipPath id="clip0_3898_67412"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath><clipPath id="clip1_3898_67412"><rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip2_3898_67412"><rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip3_3898_67412"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>'))
                                        : Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.15" d="M38.6445 66.8377C48.9307 66.8377 57.2694 58.4794 57.2694 48.1688C57.2694 37.8583 48.9307 29.5 38.6445 29.5C28.3582 29.5 20.0195 37.8583 20.0195 48.1688C20.0195 58.4794 28.3582 66.8377 38.6445 66.8377Z" fill="#6A7165" fill-opacity="0.75"/> <path opacity="0.5" d="M38.6437 60.3294C45.3441 60.3294 50.7757 54.8849 50.7757 48.1688C50.7757 41.4527 45.3441 36.0082 38.6437 36.0082C31.9434 36.0082 26.5117 41.4527 26.5117 48.1688C26.5117 54.8849 31.9434 60.3294 38.6437 60.3294Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M108.012 66.9254C118.344 66.9254 126.72 58.53 126.72 48.1738C126.72 37.8175 118.344 29.4221 108.012 29.4221C97.6803 29.4221 89.3047 37.8175 89.3047 48.1738C89.3047 58.53 97.6803 66.9254 108.012 66.9254Z" fill="#22409A" fill-opacity="0.15"/> <path d="M108.012 60.3882C114.742 60.3882 120.198 54.9196 120.198 48.1737C120.198 41.4277 114.742 35.9591 108.012 35.9591C101.282 35.9591 95.8262 41.4277 95.8262 48.1737C95.8262 54.9196 101.282 60.3882 108.012 60.3882Z" fill="#22409A" fill-opacity="0.15"/> <path d="M38.5814 21.0918C41.1334 18.5321 41.1334 14.3822 38.5814 11.8226C36.0293 9.26293 31.8917 9.26293 29.3396 11.8226C26.7876 14.3822 26.7876 18.5321 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3696_32998)"/> <path d="M65.4109 33.0401C71.0538 33.0401 75.6284 28.452 75.6284 22.7923C75.6284 17.1326 71.0538 12.5444 65.4109 12.5444C59.7679 12.5444 55.1934 17.1326 55.1934 22.7923C55.1934 28.452 59.7679 33.0401 65.4109 33.0401Z" fill="url(#paint1_linear_3696_32998)"/> <path d="M37.0967 18.144C37.0965 18.144 37.0964 18.144 37.0963 18.1438C37.0172 18.0111 35.5482 17.5388 35.1393 17.2602C35.1309 17.2546 35.1302 17.2425 35.1379 17.2359C35.7677 16.6989 36.1863 15.7971 36.1863 15.0008C36.1863 13.7699 35.1879 12.769 33.9607 12.769C32.7334 12.769 31.7355 13.7704 31.7355 15.0008C31.7355 15.7977 32.1547 16.6992 32.7842 17.2362C32.7917 17.2426 32.791 17.2544 32.7829 17.2602C32.3739 17.5389 30.9029 18.0115 30.8249 18.144C30.4512 18.7792 30.9874 19.4892 31.7244 19.4892H36.6427C37.1553 19.4892 37.5287 18.9984 37.3063 18.5365C37.2422 18.4033 37.1725 18.2726 37.097 18.1442C37.0969 18.1441 37.0968 18.144 37.0967 18.144Z" fill="white"/> <path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3696_32998)"/><path d="M94.1146 9.201C94.0488 9.08953 92.8146 8.69283 92.4713 8.45864C92.4644 8.45376 92.4637 8.44394 92.4702 8.43842C92.9991 7.98742 93.3507 7.23034 93.3507 6.56129C93.3507 5.52761 92.5124 4.68689 91.4818 4.68689C90.4512 4.68689 89.613 5.52761 89.613 6.56129C89.613 7.23034 89.9645 7.98742 90.4934 8.43842C90.4999 8.44394 90.4992 8.45376 90.4923 8.45864C90.1486 8.69283 88.9134 9.08953 88.848 9.201C88.5343 9.73439 88.9846 10.3307 89.6034 10.3307H93.3596C93.9785 10.3307 94.4281 9.73465 94.1146 9.201Z" fill="white"/> <path d="M70.2734 25.3874C70.1525 25.1818 67.8731 24.4493 67.239 24.017C67.2261 24.0084 67.2251 23.9898 67.2369 23.9797C68.2133 23.1468 68.8627 21.7491 68.8627 20.5136C68.8627 18.6055 67.3146 17.0529 65.4117 17.0529C63.5087 17.0529 61.9612 18.605 61.9612 20.5136C61.9612 21.7491 62.6105 23.1468 63.5869 23.9797C63.5987 23.9898 63.5976 24.0082 63.5849 24.017C62.9502 24.4493 60.67 25.1818 60.549 25.3874C59.9701 26.3724 60.8011 27.4731 61.9436 27.4731H68.8794C70.0218 27.4731 70.8528 26.372 70.2734 25.3874Z" fill="white"/> <path d="M41.043 18.188L55.029 22.5872" stroke="#6A7165" stroke-opacity="0.75" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M74.7189 18.4761L87.7192 11.188" stroke="#6A7165" stroke-opacity="0.75" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M20.3277 14.6929C20.5359 14.9898 20.6908 15.3506 20.8025 15.7531C21.9184 19.7664 16.9082 23.2821 13.5134 20.8708C13.1718 20.6281 12.8844 20.3591 12.6755 20.0614C11.4741 18.3488 11.4236 16.1946 12.384 14.6048C12.6937 14.0916 13.1086 13.6375 13.6243 13.2757C15.7374 11.7933 18.7385 12.4278 20.3277 14.6929Z" fill="#22409A" fill-opacity="0.15"/> <path d="M21.2509 22.7214C20.7846 22.6904 20.5819 23.677 21.0055 23.4788C21.2676 23.3561 21.4619 22.8833 21.3432 22.753C21.3258 22.734 21.2935 22.7242 21.2509 22.7214Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.4977 22.9101L20.3727 22.9978C20.3459 23.0166 20.3086 23.0101 20.2898 22.9834C20.271 22.9566 20.2776 22.9193 20.3044 22.9005L20.4295 22.8127C20.4563 22.7939 20.4936 22.8004 20.5124 22.8272C20.5311 22.8539 20.5245 22.8913 20.4977 22.9101Z" fill="#22409A" fill-opacity="0.15"/> <path d="M25.953 28.7423C25.9201 28.8472 25.7699 28.861 25.6831 28.7938C25.6325 28.7546 25.5642 28.6649 25.5879 28.6052C25.8046 28.0577 26.4875 26.2007 26.1505 25.3343C25.9358 24.7825 24.4799 25.2637 23.3101 25.6503C22.2782 25.9914 21.46 26.4206 21.17 26.0656C20.9009 25.7361 21.3206 24.9288 21.6046 24.2812C21.7718 23.8999 21.9613 23.4676 21.8811 23.3821C21.7833 23.278 21.4547 23.4615 21.1907 23.6091C20.8605 23.7936 20.5752 23.9529 20.4362 23.8163C20.205 23.5891 20.3416 23.0585 20.3688 22.9625C20.371 22.9545 20.3794 22.9503 20.3874 22.9527C20.3954 22.9551 20.4006 22.9636 20.3993 22.9719C20.3847 23.0641 20.3192 23.5603 20.5067 23.7446C20.5921 23.8286 20.8969 23.6583 21.1418 23.5214C21.4819 23.3314 21.887 23.0812 22.0384 23.2426C22.1581 23.3701 21.9414 23.763 21.6966 24.3214C21.4374 24.9126 21.1148 25.6483 21.3392 25.9372C21.4992 26.1431 22.4039 25.8441 23.2788 25.5549C24.6141 25.1136 26.1127 24.56 26.362 25.2005C26.5352 25.6458 26.4802 26.3962 26.2863 27.4468C26.1976 27.927 26.0485 28.438 25.953 28.7423Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.3284 14.6936C20.7731 15.3276 20.8737 16.1744 20.59 16.8961C20.0851 18.181 18.6847 19.1052 16.4007 19.2781C10.3634 19.7353 12.383 14.6087 12.3842 14.6057C12.3842 14.6056 12.3841 14.6057 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2763C15.7377 11.7936 18.7391 12.4283 20.3284 14.6936Z" fill="#22409A" fill-opacity="0.15"/> <path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/> <path d="M88.5745 38.1365C88.6496 38.8472 89.2546 39.5756 90.2326 40.8388C90.9261 41.7344 91.5844 42.0222 91.7726 42.5911C91.883 42.9927 91.2098 43.5772 90.9074 44.1631C90.6218 44.7153 91.3926 45.2729 91.5666 45.3895C91.5875 45.4035 91.6141 45.4029 91.6352 45.3892C91.6752 45.3633 91.6754 45.3035 91.6371 45.2752C91.5057 45.1781 91.2462 44.9631 91.048 44.6538C90.7268 44.1528 91.8808 43.1803 91.901 42.7294C91.9016 42.7162 91.9016 42.7036 91.9016 42.6909C91.9012 42.1824 91.3965 41.8503 91.0391 41.4881C90.1973 40.635 88.871 39.1461 88.7557 38.0567C88.6624 37.1765 91.1173 36.9311 92.9359 36.6438C94.5402 36.39 95.9212 36.4 96.0629 35.7298C96.1943 35.1077 95.0836 34.3224 94.2625 33.6563C93.7794 33.264 93.2313 32.8193 93.2793 32.6511C93.3376 32.4455 93.8999 32.4645 94.3514 32.4799C94.9168 32.4989 95.405 32.5149 95.4958 32.2384C95.6147 31.8774 95.3047 31.4232 95.1167 31.192C95.0856 31.1538 95.1114 31.0792 95.118 31.0304C95.1242 30.9814 95.1174 30.9068 95.1583 30.8792C96.4377 30.0173 102.718 25.6139 103.218 21.9348C103.639 18.836 102.229 15.9386 99.8638 14.4833C99.1182 14.0247 98.2775 13.7091 97.3696 13.5762C97.3484 13.5726 97.3268 13.5696 97.3051 13.5665C93.4824 13.0432 89.9324 15.9415 89.3753 20.0399C89.3023 20.5755 89.3442 21.1592 89.4705 21.7689C89.4716 21.7725 89.4721 21.7761 89.4728 21.7798C90.241 25.452 94.1014 30.0668 94.8171 30.8986C94.8175 30.8989 94.8169 30.8993 94.8165 30.8992C94.8164 30.8992 94.8164 30.8992 94.8164 30.8991C94.7682 30.8931 94.7231 30.9268 94.7166 30.9754C94.712 31.0097 94.6937 31.0645 94.6596 31.0701C94.2743 31.1327 93.6104 31.2655 93.5621 31.4454C93.4936 31.6996 94.075 32.1942 94.5072 32.1777C94.8628 32.164 94.921 31.3939 94.93 31.1235C94.9306 31.1076 94.9446 31.0958 94.9604 31.0979C94.9677 31.0989 94.9741 31.1025 94.9784 31.1085C95.0842 31.256 95.4707 31.8338 95.3531 32.1914C95.2972 32.3615 94.7759 32.3438 94.3568 32.3296C93.7747 32.3106 93.0654 32.2554 92.9747 32.5734C92.9032 32.8252 93.4604 33.1983 94.1682 33.773C94.9176 34.3807 96.053 35.1209 95.9136 35.6499C95.7611 36.2281 94.2729 36.2806 92.9124 36.4954C90.836 36.8239 88.4665 37.1144 88.5745 38.1365Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M91.8293 17.5349C92.0707 17.7856 93.2602 17.5765 93.8581 17.0027C94.4561 16.4289 94.4235 15.6907 94.2166 15.411C93.8202 14.8753 92.9502 15.3435 92.3522 15.9174C91.7543 16.4911 91.5879 17.2841 91.8293 17.5349Z" fill="white"/> <g clip-path="url(#clip0_3696_32998)"> <path d="M119.68 56.8803C118.332 54.7508 116.216 54.1749 116.216 54.1749L111.146 52.2543H106.306L101.236 54.1749C101.236 54.1749 99.1201 54.7508 97.7726 56.8803H119.68Z" fill="#F9CDB1"/> <path d="M102.114 66.9627H94.2806C94.2806 65.5966 94.3671 63.6465 94.3671 63.6465C94.8285 55.4606 100.664 54.1749 100.664 54.1749C105.216 53.0498 102.736 60.242 102.736 60.242C102.854 61.975 102.602 64.3081 102.112 66.96L102.114 66.9627Z" fill="url(#paint3_linear_3696_32998)"/> <path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/> <path d="M105.405 50.4757V47.3979H112.051V48.9221C110.155 50.5346 107.872 50.7382 105.405 50.4757Z" fill="#EAB894"/> <path d="M98.3287 66.9627C98.2002 63.3947 98.2395 62.4144 98.271 61.1956C98.3392 58.4848 98.9081 56.1919 100.657 54.1776C100.659 54.1776 100.665 54.1776 100.665 54.1776L105.402 52.5999C105.402 52.5999 104.406 54.3811 108.761 54.3811C108.761 54.3811 111.629 54.266 112.048 52.5999L116.859 54.1776C116.859 54.1776 120.972 55.2115 121.678 59.6339L119.261 64.1769L119.347 66.9654H98.3313L98.3287 66.9627Z" fill="url(#paint4_linear_3696_32998)"/> <path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/> <path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/> <path d="M116.065 31.8618C117.827 32.7297 117.887 34.3342 117.803 36.161C117.74 37.578 117.31 38.7834 116.815 40.1575C116.252 41.7183 115.721 42.9478 115.221 43.846C115.221 43.846 114.054 45.6996 112.552 46.9693C112.987 45.2068 113.11 43.8915 113.506 42.5362C113.983 40.9102 113.845 38.7352 113.137 37.0664C113.137 37.0664 112.736 35.9548 111.089 35.6387L116.068 31.8591" fill="#1A1818"/> <path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.222C115.325 43.222 114.18 45.1399 112.806 44.0765L113.17 42.188Z" fill="#F9CDB1"/> <path d="M113.312 37.5378C113.312 37.5378 113.307 41.1085 112.664 43.0022L113.346 43.1415C113.346 43.1415 113.705 40.6933 114.938 40.6638L113.312 37.5405V37.5378Z" fill="#1A1818"/> <path d="M99.9442 29.8931C94.2812 35.7861 111.419 35.7272 111.419 35.7272C111.419 35.7272 113.218 36.5763 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0136C108.727 27.0136 103.371 26.3413 99.9442 29.8931Z" fill="#1A1818"/> <path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3696_32998)"/></g> <g clip-path="url(#clip1_3696_32998)"> <path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1607 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2666 34.7656 34.7422Z" fill="#1A1818"/> <path d="M37.0325 32.188C37.0325 32.188 36.2643 33.1205 37.3707 34.1837L36.6235 34.9215C36.6235 34.9215 34.9115 32.872 37.0325 32.1906V32.188Z" fill="#1A1818"/> <path d="M33.8828 57.3224C35.2304 55.2857 37.3461 54.7349 37.3461 54.7349L42.4165 52.8981H47.2562L52.3266 54.7349C52.3266 54.7349 54.4424 55.2857 55.7899 57.3224H33.8828Z" fill="#F9CDB1"/> <path d="M51.4482 66.9627H59.2819C59.2819 65.6562 59.1954 63.7911 59.1954 63.7911C58.734 55.9621 52.898 54.7324 52.898 54.7324C48.3467 53.6564 50.8269 60.535 50.8269 60.535C50.7089 62.1925 50.9606 64.4239 51.4508 66.9601L51.4482 66.9627Z" fill="url(#paint6_linear_3696_32998)"/> <path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/> <path d="M48.1578 51.1944V48.2509H41.5117V49.7086C43.4072 51.2508 45.6907 51.4455 48.1578 51.1944Z" fill="#EAB894"/> <path d="M55.2338 66.9626C55.3623 63.5502 55.323 62.6126 55.2915 61.4469C55.2233 58.8543 54.6544 56.6614 52.9057 54.7349C52.9031 54.7349 52.8979 54.7349 52.8979 54.7349L48.1604 53.226C48.1604 53.226 49.1567 54.9296 44.802 54.9296C44.802 54.9296 41.9338 54.8194 41.5144 53.226L36.7035 54.7349C36.7035 54.7349 32.59 55.7238 31.8848 59.9534L34.302 64.2983L34.2155 66.9652H55.2312L55.2338 66.9626Z" fill="url(#paint7_linear_3696_32998)"/> <path d="M53.3212 40.3373C53.5099 38.8822 53.1927 37.486 53.2084 36.0513C53.2189 35.0881 53.0275 33.6355 51.8006 33.5125C50.1017 33.3435 49.5774 36.2076 49.4777 37.4501C49.3388 39.1614 49.774 41.0111 50.1122 42.6865C50.2328 43.2911 50.2223 44.5003 50.9171 44.7924C51.5122 45.0409 52.123 44.4542 52.4272 44.0162C53.1612 42.9607 53.1639 41.5568 53.3186 40.3373H53.3212Z" fill="#1A1818"/> <path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/> <path d="M53.7278 30.241C54.0136 31.7756 53.7462 33.9019 52.5297 34.8831C51.2084 35.9463 50.0731 35.6568 48.4818 36.0026C46.8275 36.3613 45.1338 36.5048 43.4717 36.8173C42.3076 37.0376 40.9627 37.1631 40.4278 38.3698C39.72 39.9658 39.581 42.046 40.0582 43.6011C40.454 44.8948 40.5773 46.1553 41.0125 47.841C39.5102 46.6266 38.3436 44.8538 38.3436 44.8538C37.8437 43.9948 37.3123 42.8189 36.7496 41.3262C36.254 40.0119 35.8267 38.8591 35.7612 37.5039C35.6799 35.7567 36.5451 34.9958 37.4994 33.3921C38.396 31.8857 39.6544 30.8405 41.2956 30.364C41.2956 30.364 49.6117 30.82 49.457 28.9652C49.457 28.9652 51.4548 29.4648 51.0746 30.7611C51.0746 30.7611 53.1642 30.387 52.7211 28.9652C52.7211 28.9652 53.5653 29.3546 53.7331 30.2436L53.7278 30.241Z" fill="#1A1818"/> <path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/> <path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/> <path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3696_32998)"/></g> <g clip-path="url(#clip2_3696_32998)"> <path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/> <path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/> <path d="M81.8041 54.8701H72.3047V59.4333H81.8041V54.8701Z" fill="#F9CDB1"/> <path d="M92.2785 66.9627H85.8549L84.7075 62.4623C84.7075 62.4623 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9627H92.2785Z" fill="#F9CDB1"/> <path d="M69.9639 66.9626L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6857 64.2944 66.9626H69.9639Z" fill="#F9CDB1"/> <path d="M80.6791 45.6549H74.8184V53.9815H80.6791V45.6549Z" fill="#F9CDB1"/> <path d="M80.6791 49.0551V45.6576H74.8184V50.5526C76.7522 50.4951 79.0469 50.1554 80.6791 49.0551Z" fill="#EAB894"/> <path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/> <path d="M68.2589 63.4057V66.9626H87.2847L87.0612 62.8307C86.8241 54.87 87.7426 54.8674 87.7426 54.8674C86.8861 54.3134 86.1697 54.1278 86.1697 54.1278L81.3997 52.8106C81.3997 52.8106 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5312 74.2085 52.7688L70.4298 54.1278C70.4298 54.1278 68.9323 54.5721 67.5371 55.8867C67.5371 55.8867 68.2104 56.2865 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3696_32998)"/> <path d="M81.3168 46.3188C81.1175 46.7683 80.8374 47.1839 80.4469 47.5498C79.4746 48.4592 78.3353 49.1962 77.0856 49.6928C75.5531 50.3044 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6612 70.0613 45.3701C69.2291 42.8612 69.2399 40.1222 69.4499 37.5192C69.4984 36.926 69.5604 36.3301 69.6627 35.742C70.6189 30.063 75.6285 30.0734 75.6285 30.0734C79.4099 29.914 82.8628 31.6467 83.4446 35.5147C83.6681 36.9965 83.3745 38.5855 83.2964 40.0674C83.2399 41.1546 83.1025 42.2418 82.7443 43.2793C82.378 44.3378 81.7747 45.2943 81.3195 46.3162L81.3168 46.3188Z" fill="#F9CDB1"/> <path d="M81.8074 41.9882C81.8074 41.9882 82.3083 39.968 83.9082 40.8017C83.9082 40.8017 84.63 41.1571 84.1102 42.5449C84.1102 42.5449 83.3507 44.5416 81.8047 43.8124V41.9882H81.8074Z" fill="#F9CDB1"/> <path d="M69.0734 36.3927C68.804 28.5941 76.4289 28.9652 76.4289 28.9652C83.9245 29.2631 84.2988 32.5587 84.2988 32.5587C85.2065 36.7403 83.9083 40.8017 83.9083 40.8017C82.3139 40.4645 82.0445 42.5318 82.0445 42.5318C82.2115 39.2179 81.9314 39.0167 81.9314 39.0167C79.4993 38.9931 77.7459 38.7396 77.7459 38.7396L77.6382 36.4293L77.0511 38.6194C77.0511 38.6194 73.6036 38.5436 70.0241 37.7204C70.0241 37.7204 69.0707 37.7857 69.0707 40.6004V36.3927H69.0734Z" fill="#1A1818"/></g> <g clip-path="url(#clip3_3696_32998)"> <path d="M11.9808 51.8379C13.2196 51.3943 14.0006 50.3714 15.4737 50.4989C18.0914 50.7274 19.947 53.6286 22.4677 51.6599C25.5163 49.2742 24.7164 34.9304 23.3941 31.6812C22.2738 28.9262 20.2055 31.6201 17.2593 30.6929C13.8336 29.6169 10.8793 32.2843 9.10729 35.0526C7.26522 37.9352 6.31456 41.6068 6.58656 44.9915C6.83702 48.1158 7.41064 53.4745 11.9835 51.8353L11.9808 51.8379Z" fill="#1A1818"/> <path d="M0.284705 67H6.70771L7.85497 62.4251C7.85497 62.4251 8.78947 55.4804 3.96077 55.3821C3.96077 55.3821 1.4562 57.1143 0.772152 61.1924C0.772152 61.1924 0.230843 64.4496 0.282011 67H0.284705Z" fill="url(#paint10_linear_3696_32998)"/> <path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3696_32998)"/> <path d="M11.884 45.3395H17.7441V53.8039H11.884V45.3395Z" fill="#F9CDB1"/> <path d="M11.884 48.7959V45.3422H17.7441V50.3183C15.8105 50.2598 13.516 49.9144 11.884 48.7959Z" fill="#EAB894"/> <path d="M25.5137 56.5058C24.1994 54.4973 22.1312 53.9554 22.1312 53.9554L17.1786 52.1461H12.4522L6.39546 53.9554C6.39546 53.9554 4.32717 54.5 3.01295 56.5058H25.5137Z" fill="#F9CDB1"/> <path d="M24.3015 67H4.78469L4.90857 63.3842L2.03774 59.1015C2.676 54.9304 6.39246 53.9554 6.39246 53.9554L11.1619 52.6164C11.1619 52.6164 10.8764 54.1493 14.8137 54.1493C14.8137 54.1493 17.9754 54.1493 18.3524 52.5765L22.1308 53.958C22.1308 53.958 24.8993 54.7923 26.4102 57.5526L24.2988 63.3868V67.0027L24.3015 67Z" fill="url(#paint12_linear_3696_32998)"/> <path d="M11.2449 46.0143C11.4442 46.4713 11.7243 46.8937 12.1148 47.2656C13.087 48.1902 14.2261 48.9394 15.4757 49.4442C17.0081 50.0659 19.3214 50.1429 20.5414 48.8995C21.5298 47.89 22.0711 46.3623 22.4993 45.0499C23.3315 42.4994 23.3207 39.7152 23.1106 37.069C23.0621 36.466 23.0002 35.8602 22.8979 35.2625C21.9445 29.4893 16.9354 29.5 16.9354 29.5C13.1543 29.3379 9.70175 31.0993 9.12004 35.0313C8.89652 36.5377 9.19007 38.153 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9245C10.1865 44.0005 10.7898 44.9729 11.2449 46.0117V46.0143Z" fill="#F9CDB1"/> <path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/> <path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs> <linearGradient id="paint0_linear_3696_32998" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint1_linear_3696_32998" x1="65.4109" y1="12.5444" x2="65.4109" y2="33.0401" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint2_linear_3696_32998" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint3_linear_3696_32998" x1="98.8454" y1="54.0554" x2="98.8454" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint4_linear_3696_32998" x1="109.959" y1="52.5999" x2="109.959" y2="66.9654" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint5_linear_3696_32998" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint6_linear_3696_32998" x1="54.7171" y1="54.6182" x2="54.7171" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint7_linear_3696_32998" x1="43.6038" y1="53.226" x2="43.6038" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint8_linear_3696_32998" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint9_linear_3696_32998" x1="77.6398" y1="52.7688" x2="77.6398" y2="66.9626" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint10_linear_3696_32998" x1="4.10317" y1="55.3821" x2="4.10317" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint11_linear_3696_32998" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint12_linear_3696_32998" x1="14.2239" y1="52.5765" x2="14.2239" y2="67.0027" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <clipPath id="clip0_3696_32998"> <rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath> <clipPath id="clip1_3696_32998"> <rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath> <clipPath id="clip2_3696_32998"> <rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath> <clipPath id="clip3_3696_32998"> <rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>')),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    Text(
                                      mrJoin.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontWeight,
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    Text(
                                      mrJoinDes.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(0.5),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    // SizedBox(height: 20.h,),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 24.w, right: 24.w),
                                      child: InkWell(
                                        onTap: () {
                                          Get.to(() => RegisterPage());
                                        },
                                        child: Container(
                                          height: 48.h,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                              color: configTheme()
                                                  .colorScheme
                                                  .onPrimary
                                                  .withOpacity(0.05),
                                              borderRadius:
                                              BorderRadius.circular(16)),
                                          child: Center(
                                            child: Text(
                                              mrSign.tr,
                                              style: TextStyle(
                                                color: appConfigService
                                                    .countryConfigCollection ==
                                                    "aam"
                                                    ? Color(0xFF792AFF)
                                                    .withOpacity(0.75)
                                                    : appConfigService
                                                    .countryConfigCollection ==
                                                    "rafco"
                                                    ? configTheme()
                                                    .colorScheme
                                                    .onPrimary
                                                    : Color(0xFF6A7165),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              )),
                          buildContainerContent(186.h, mrSignDes1.tr,
                              mrSignDes2.tr, mrSignDes3.tr),
                        ],
                      ),
                    ),
                  ),
                )
                    : mrController.mrData.value.mr_id == null

                /// ไม่ได้สมัคร MR
                    ? Container(
                  height: Get.height,
                  width: Get.width,
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 24.w, right: 24.w, top: 104.h),
                    child: SingleChildScrollView(
                      child: Column(
                        // crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          buildHead(false
                            // profileController.profile.value.ref_code
                            //     .toString(),
                            // '${profileController.profile.value.firstname} ${profileController.profile.value.lastname}',
                            // ""
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                onTap: () {
                                  Get.to(() => const MrHistoryLoan());
                                },
                                child: buildContent(
                                    mrLoan.tr,
                                    205.h,
                                    appConfigService
                                        .countryConfigCollection
                                        .toString() ==
                                        "aam"
                                        ? '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/> <path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                        : appConfigService
                                        .countryConfigCollection
                                        .toString() ==
                                        "rafco"
                                        ? '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#EA1B23" fill-opacity="0.5"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                        : '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>',
                                    '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',
                                    mrWait.tr,
                                    mrSuccess.tr,
                                    mrController.close?.value.toString(),
                                    mrController.pending?.value
                                        .toString(),
                                    // mrController.mrReferral[0].transection_pending.toString(),
                                    // mrController.mrReferral[0].transection_close.toString(),
                                    mrController.mrReferral.length
                                        .toString()
                                  // MrHistoryLoan()
                                ),
                              ),

                              /// โหลดแอพ
                              InkWell(
                                onTap: () {
                                  Get.to(() => const MrHistoryLoadApp());
                                },
                                child: buildContent(
                                  mrApp.tr,
                                  205.h,
                                  appConfigService.countryConfigCollection
                                      .toString() ==
                                      "aam"
                                      ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="3" width="12" height="18" rx="2" fill="#792AFF" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/> <path d="M9 3V3C9 3.46499 9 3.69748 9.05111 3.88823C9.18981 4.40587 9.59413 4.81019 10.1118 4.94889C10.3025 5 10.535 5 11 5H13C13.465 5 13.6975 5 13.8882 4.94889C14.4059 4.81019 14.8102 4.40587 14.9489 3.88823C15 3.69748 15 3.46499 15 3V3" stroke="#1A1818" stroke-width="1.2"/> <circle cx="12" cy="18" r="1" fill="white"/></svg>'
                                      : appConfigService
                                      .countryConfigCollection
                                      .toString() ==
                                      "rafco"
                                      ? '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#EA1B23" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>'
                                      : '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#6A7165" fill-opacity="0.75" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>',
                                  '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',
                                  "",
                                  "",
                                  "",
                                  "",
                                  mrController.referral_download.isEmpty
                                      ? "0"
                                      : mrController.referral_download.length
                                      .toString(),
                                  // MrHistoryLoan()
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 16.h),
                          InkWell(
                            onTap: () {
                              Get.to(() =>
                                  RewardHistory(
                                      point: mrController
                                          .totalPointReferal!.value
                                          .toString()));
                            },
                            child: buildPoint(mrController
                                .totalPointReferal!.value
                                .toString()),
                          ),
                          SizedBox(height: 20.h),
                          buildMenu(mrRecommend.tr, mrReferLoan.tr,
                              '${mrPointReceive.tr} ${webViewPointCtl.namePoint
                                  .value}'),
                          // '<svg width="120" height="79" viewBox="0 0 120 79" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3430_36568)"><path opacity="0.15" d="M60.4642 79.5C82.3185 79.5 100.035 61.8152 100.035 40C100.035 18.1848 82.3185 0.5 60.4642 0.5C38.6099 0.5 20.8936 18.1848 20.8936 40C20.8936 61.8152 38.6099 79.5 60.4642 79.5Z" fill="#FF9300"/><path opacity="0.5" d="M60.4633 65.7295C74.6988 65.7295 86.239 54.21 86.239 39.9998C86.239 25.7897 74.6988 14.2701 60.4633 14.2701C46.2277 14.2701 34.6875 25.7897 34.6875 39.9998C34.6875 54.21 46.2277 65.7295 60.4633 65.7295Z" fill="url(#paint0_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M27.6287 7.4359C27.4981 7.78338 27.4255 8.16498 27.3705 8.61276C27.3436 8.83717 27.3177 9.14845 27.2886 9.5135C27.1694 11.0316 26.9683 13.5384 26.2218 14.1992C25.9501 14.4412 25.5178 14.3244 24.8418 13.5663C24.0912 12.7245 23.4546 11.5528 22.79 10.3346C22.2581 9.35631 21.7096 8.34802 21.0647 7.43694H20.3203V6.70166H28.8324V7.43694H27.6287V7.4359Z" fill="#792AFF" fill-opacity="0.1"/><path d="M27.6287 7.39046L27.3947 7.34793C27.2538 7.72259 27.1783 8.12684 27.1224 8.58231L27.1223 8.58295C27.0947 8.81329 27.0684 9.12981 27.0394 9.49369L27.0394 9.49393C26.9796 10.2549 26.9003 11.2492 26.7442 12.1344C26.6662 12.5771 26.5703 12.9846 26.4512 13.3176C26.3298 13.6573 26.1947 13.8893 26.0561 14.012L26.0555 14.0126C26.0214 14.0429 25.9895 14.0567 25.9554 14.061C25.919 14.0656 25.8625 14.0613 25.779 14.0268C25.605 13.9548 25.3583 13.7699 25.0284 13.3999C24.3035 12.5871 23.6829 11.4495 23.0167 10.2282L23.0096 10.2152L23.0094 10.2149L23.009 10.2141C22.4786 9.23845 21.9234 8.21734 21.2688 7.2925L21.1941 7.18694H21.0647H20.5703V6.95166H28.5824V7.18694H27.6287V7.39046Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M32.5251 6.6139C32.6889 6.73076 32.9201 6.69249 33.0383 6.52599C33.1555 6.36053 33.1182 6.12992 32.9523 6.01306C32.4505 5.65628 31.7465 5.49495 31.0466 5.50012C30.3157 5.50633 29.5713 5.69557 29.055 6.03064C28.4982 6.39569 28.1509 6.76591 27.9197 7.20646C27.6926 7.63873 27.59 8.11444 27.5174 8.70701C27.4904 8.93142 27.4645 9.24269 27.4355 9.60775C27.3162 11.1259 27.1151 13.6326 26.3686 14.2935C26.097 14.5355 25.6646 14.4186 24.9886 13.6606C24.238 12.8188 23.6014 11.6471 22.9368 10.4289C22.2805 9.22201 21.6004 7.97069 20.744 6.90863C20.6154 6.7504 20.3842 6.72558 20.2245 6.85175C20.0659 6.97998 20.041 7.21267 20.1675 7.36986C20.9824 8.38332 21.647 9.60361 22.2878 10.7805C22.9731 12.039 23.6315 13.2479 24.435 14.1508C25.4707 15.31 26.2566 15.3804 26.8601 14.8447C27.8295 13.9863 28.0441 11.2955 28.1726 9.66566C28.1996 9.31302 28.2255 9.01001 28.2514 8.79801C28.3157 8.28197 28.3976 7.88072 28.5718 7.5467C28.7439 7.22301 29.0135 6.94069 29.4583 6.64802C29.8605 6.38845 30.4546 6.24057 31.0508 6.23643C31.6076 6.2323 32.1508 6.34916 32.522 6.61286L32.5251 6.6139Z" fill="#1A1818"/><path d="M32.6397 6.38853L32.6703 6.41038C32.7212 6.44665 32.7961 6.43538 32.8345 6.38128L33.0383 6.52598L32.8343 6.38153C32.8732 6.3266 32.8588 6.25301 32.8083 6.21744L32.8074 6.21681C32.3622 5.90028 31.7157 5.7452 31.0486 5.75011C30.3528 5.75605 29.6584 5.93726 29.1916 6.24C28.6623 6.58714 28.3483 6.92779 28.141 7.32264L28.141 7.32272C27.9351 7.71463 27.837 8.15442 27.7656 8.73703C27.7393 8.9555 27.7138 9.26146 27.6847 9.62757C27.6252 10.3847 27.5444 11.4028 27.3835 12.3155C27.303 12.7719 27.2012 13.2099 27.0689 13.5802C26.9389 13.9438 26.7689 14.273 26.5343 14.4807L26.3686 14.2935M32.6397 6.38853L31.0489 5.98644C30.42 5.99082 29.7756 6.14573 29.3227 6.43796L29.3227 6.43795L29.3209 6.43918C28.8521 6.74759 28.5482 7.0585 28.3511 7.42932L28.3501 7.4311C28.154 7.80727 28.0682 8.24658 28.0034 8.76711L28.0033 8.76767C27.9766 8.98592 27.9503 9.29403 27.9234 9.64642C27.8589 10.4634 27.774 11.5294 27.5862 12.4968C27.4924 12.9803 27.3743 13.4315 27.2237 13.8101C27.0719 14.192 26.8948 14.48 26.6943 14.6575L26.6941 14.6577C26.4429 14.8807 26.1769 14.9637 25.8675 14.8901C25.5387 14.8118 25.1253 14.5481 24.6217 13.9845C23.8411 13.1074 23.1965 11.9265 22.5073 10.6609L22.503 10.6529C21.8658 9.48255 21.1919 8.2449 20.3623 7.2132L20.3623 7.21313C20.3231 7.1644 20.3295 7.08937 20.3806 7.04704C20.4335 7.00621 20.5083 7.01507 20.55 7.06628L20.744 6.90863M32.6397 6.38853L32.6349 6.38693M32.6397 6.38853L32.6349 6.38693M26.3686 14.2935L26.5349 14.4801C26.4332 14.5708 26.3091 14.6331 26.1646 14.6513C26.0225 14.6692 25.8778 14.6422 25.7347 14.583C25.4556 14.4676 25.1481 14.215 24.8021 13.827L24.802 13.827C24.0387 12.9709 23.3952 11.7912 22.7442 10.5978L22.7173 10.5486L22.7172 10.5483C22.0588 9.33755 21.3891 8.10692 20.5494 7.06555L20.744 6.90863M26.3686 14.2935C26.097 14.5355 25.6646 14.4186 24.9886 13.6606C24.2446 12.8262 23.6126 11.6677 22.9543 10.461L22.9368 10.4289L22.9345 10.4245C22.2789 9.21903 21.5994 7.96943 20.744 6.90863M26.3686 14.2935C27.1151 13.6326 27.3162 11.1259 27.4355 9.60775L20.2245 6.85175C20.3842 6.72558 20.6154 6.7504 20.744 6.90863M32.6349 6.38693C32.2067 6.09726 31.619 5.98222 31.0491 5.98644L32.6349 6.38693Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28.1742 9.66591C28.2011 9.31326 28.2271 9.01026 28.253 8.79826C28.3173 8.28222 28.3992 7.88097 28.5734 7.54694C28.6304 7.44146 28.6967 7.34011 28.7745 7.24083C28.9404 7.03814 29.1612 6.84476 29.4609 6.64827C29.8631 6.3887 30.4572 6.24081 31.0534 6.23668C31.6101 6.23254 32.1534 6.3494 32.5246 6.61311C32.5401 6.62448 32.5598 6.63689 32.5775 6.64413L32.6169 6.66585L32.7734 6.75789C33.7978 7.20567 34.6708 7.95026 35.2783 8.87582C35.8662 9.77036 36.2104 10.8428 36.2104 11.9927V14.7384C33.4432 14.7808 30.2602 14.8697 26.6553 15C26.7278 14.9556 26.7963 14.9028 26.8626 14.8439C27.832 13.9855 28.0467 11.2947 28.1752 9.66487L28.1742 9.66591Z" fill="url(#paint1_linear_3430_36568)"/><path d="M26.9906 14.988C27.0199 14.9869 27.0492 14.9859 27.0784 14.9849C27.1584 14.9078 27.2325 14.8212 27.3014 14.7268M26.9906 14.988L27.3014 14.7268M26.9906 14.988L26.8626 14.8439L26.9902 14.988L26.9906 14.988ZM27.3014 14.7268C30.5344 14.6129 33.4192 14.5334 35.9604 14.4923V11.9927C35.9604 10.8933 35.6314 9.86835 35.0694 9.01312L35.0693 9.01301C34.488 8.1274 33.6526 7.41505 32.6733 6.98696L32.6596 6.98097L32.6467 6.9734L32.4931 6.88312L32.4662 6.86826C32.43 6.85148 32.3989 6.83084 32.3786 6.81609C32.0669 6.5952 31.5839 6.48274 31.0552 6.48667L31.0551 6.48667C30.4922 6.49058 29.9488 6.63124 29.5971 6.85785C29.3147 7.04317 29.1154 7.21955 28.9696 7.39711C28.9015 7.48433 28.8438 7.57267 28.7942 7.66418C28.6425 7.95574 28.5647 8.31843 28.5011 8.82887C28.4872 8.94238 28.4732 9.08391 28.4589 9.24723L28.4244 9.68453C28.3603 10.4973 28.2737 11.5915 28.0797 12.5913C27.9826 13.0915 27.8573 13.5759 27.6909 13.9941C27.5836 14.2641 27.4558 14.5151 27.3014 14.7268Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M39.793 75.3084C39.9337 75.0047 40.297 74.7509 40.3547 74.3565C40.3841 74.161 40.4125 73.8864 40.4419 73.5669C40.5721 72.2356 40.7884 70.0382 41.5853 69.4582C41.8782 69.2469 42.6951 69.2169 43.4196 69.8817C44.6029 70.4242 127.868 71.0058 91.5717 72.848C92.1302 73.6859 94.7257 73.5378 95.3956 74.3224C95.4114 74.3399 96.1831 74.5845 96.1978 74.6028C94.7919 75.8883 94.8213 75.9549 92.4484 76.6372L37.2448 76.5415L36.4814 76.5082L39.0895 75.4956L39.792 75.3092L39.793 75.3084Z" fill="url(#paint2_linear_3430_36568)"/><path d="M39.9949 75.4674L39.9483 75.5043L39.9472 75.5051L39.9064 75.5375L39.8561 75.5508L39.1671 75.7337L37.7282 76.2923L92.4133 76.3871C93.5836 76.0502 94.1325 75.8715 94.5628 75.6285C94.9286 75.4219 95.212 75.1677 95.7213 74.7005C95.7202 74.7002 95.7191 74.6998 95.718 74.6994L95.7037 74.6947C95.6105 74.6636 95.5167 74.6323 95.4444 74.6075C95.4065 74.5945 95.3727 74.5826 95.3471 74.5731C95.3392 74.5702 95.3311 74.5671 95.3232 74.564C95.3186 74.5622 95.314 74.5603 95.3097 74.5585C95.3043 74.5562 95.2956 74.5524 95.2861 74.5476C95.2815 74.5453 95.2735 74.5411 95.2641 74.5352L95.2637 74.5349C95.258 74.5314 95.2337 74.5162 95.2099 74.4898L95.2054 74.4848L95.2055 74.4847C95.0862 74.345 94.8569 74.2242 94.5187 74.1172C94.2045 74.0178 93.8372 73.942 93.4538 73.8629L93.3913 73.85C92.9931 73.7677 92.5788 73.6801 92.2281 73.5575C91.8852 73.4376 91.5508 73.2674 91.3637 72.9867L91.1196 72.6206L91.559 72.5983C96.0071 72.3726 98.6451 72.1663 99.8585 71.9787C99.7494 71.9631 99.6289 71.9475 99.4973 71.9319C96.791 71.6115 89.7627 71.3493 81.5436 71.1274C75.2524 70.9576 68.2742 70.8115 62.024 70.6808C60.1151 70.6408 58.2741 70.6023 56.5413 70.565C52.8387 70.4852 49.6298 70.4107 47.3093 70.3393C46.1494 70.3036 45.2099 70.2685 44.5412 70.2339C44.2071 70.2166 43.9384 70.1992 43.7426 70.1818C43.645 70.173 43.5626 70.164 43.498 70.1545C43.4458 70.1469 43.3716 70.1348 43.3154 70.109L43.2796 70.0926L43.2505 70.0659C42.925 69.7672 42.5857 69.6305 42.3039 69.5887C42.0109 69.5452 41.8075 69.6065 41.7319 69.6607C41.5853 69.7676 41.4448 69.9663 41.3179 70.2575C41.1927 70.5448 41.0911 70.8977 41.0079 71.2835C40.8416 72.0551 40.7561 72.9226 40.6908 73.5905C40.6907 73.5907 40.6907 73.591 40.6907 73.5912L40.4419 73.5669C40.4125 73.8864 40.3841 74.161 40.3547 74.3565L39.9949 75.4674ZM39.9949 75.4674L40.0198 75.4135C40.0605 75.3258 40.1225 75.2477 40.2066 75.1418C40.2362 75.1047 40.2684 75.0642 40.3033 75.0186C40.4238 74.8617 40.5631 74.6571 40.602 74.3937L39.9949 75.4674Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M34.4091 65.1283H88.8409V9.83698C88.8409 7.75972 86.706 6.06165 84.0989 6.06165H30.7949V6.4638C32.9185 7.20655 34.4091 8.89807 34.4091 10.8555V65.1291V65.1283Z" fill="white"/><path d="M88.8409 65.3783C88.979 65.3783 89.0909 65.2664 89.0909 65.1283V9.83698C89.0909 7.57144 86.7878 5.81165 84.0989 5.81165H30.7949C30.6569 5.81165 30.5449 5.92357 30.5449 6.06165V6.4638C30.5449 6.57005 30.6121 6.6647 30.7124 6.69978C32.7687 7.419 34.1591 9.03551 34.1591 10.8555V65.1283V65.1291C34.1591 65.2672 34.2711 65.3791 34.4091 65.3791C34.416 65.3791 34.4228 65.3789 34.4295 65.3783H88.8409Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.8414 65.1291H34.1768V73.4919C34.1768 74.4669 34.6807 75.3556 35.4938 75.9998C35.7924 76.2361 36.1298 76.4359 36.5007 76.5984C36.6076 76.5752 36.7259 76.5876 36.8244 76.6423C37.1974 76.8521 37.7465 76.9466 38.3091 76.9425C38.9116 76.94 39.514 76.8222 39.9185 76.6125C40.3669 76.3787 40.6435 76.1532 40.8154 75.8937C40.9914 75.6267 41.0742 75.3051 41.1391 74.8905L41.2177 74.1933C41.3476 72.8875 41.5676 70.7294 42.5431 70.0421C42.9182 69.781 43.3562 69.6997 43.8821 69.8863H88.8414V65.1299V65.1291Z" fill="white"/><path d="M36.5007 76.5984C36.6076 76.5752 36.7259 76.5876 36.8244 76.6423C37.1974 76.8521 37.7465 76.9466 38.3091 76.9425C38.9116 76.94 39.514 76.8222 39.9185 76.6125M36.5007 76.5984L36.601 76.3694C36.5793 76.3599 36.5577 76.3502 36.5362 76.3405M36.5007 76.5984C36.1298 76.4359 35.7924 76.2361 35.4938 75.9998M36.5007 76.5984L36.4476 76.3541C36.4765 76.3478 36.5061 76.3432 36.5362 76.3405M36.5362 76.3405C36.6719 76.3279 36.8169 76.3521 36.9459 76.4238L36.947 76.4244C37.2658 76.6037 37.7648 76.6965 38.3073 76.6925L38.3081 76.6925C38.8861 76.6901 39.4453 76.5761 39.8029 76.3908M36.5362 76.3405C36.209 76.1914 35.9122 76.0121 35.649 75.8039M39.8029 76.3908L39.9185 76.6125M39.8029 76.3908C39.8031 76.3907 39.8032 76.3906 39.8034 76.3906L39.9185 76.6125M39.8029 76.3908C40.2325 76.1668 40.4678 75.9657 40.6068 75.7558M39.9185 76.6125C40.3669 76.3787 40.6435 76.1532 40.8154 75.8937M40.8154 75.8937L40.6066 75.7561C40.6067 75.756 40.6067 75.7559 40.6068 75.7558M40.8154 75.8937C40.9914 75.6267 41.0742 75.3051 41.1391 74.8905M40.8154 75.8937L40.6069 75.7557C40.6069 75.7557 40.6068 75.7558 40.6068 75.7558M40.6068 75.7558C40.7512 75.5367 40.8277 75.2613 40.8913 74.8571M41.1391 74.8905L40.8907 74.8625L40.8913 74.8571M41.1391 74.8905L41.2177 74.1933M41.1391 74.8905L40.8921 74.8518C40.8918 74.8536 40.8916 74.8553 40.8913 74.8571M40.8913 74.8571L40.9691 74.1669M41.2177 74.1933L40.9689 74.1685C40.969 74.168 40.969 74.1674 40.9691 74.1669M41.2177 74.1933C41.3476 72.8875 41.5676 70.7294 42.5431 70.0421C42.9182 69.781 43.3562 69.6997 43.8821 69.8863M41.2177 74.1933L40.9693 74.1653L40.9691 74.1669M40.9691 74.1669C41.0338 73.5165 41.1226 72.6341 41.3205 71.8263C41.5146 71.0337 41.83 70.2388 42.3991 69.8378L42.4003 69.837C42.8316 69.5367 43.3399 69.4417 43.9239 69.6363M43.9239 69.6363H43.8821V69.8863M43.9239 69.6363C43.9378 69.6409 43.9517 69.6457 43.9657 69.6507L43.8821 69.8863M43.9239 69.6363H88.5914M43.8821 69.8863H88.5914V69.6363M88.5914 69.6363V65.3791M88.5914 69.6363H88.8414M88.8414 69.6363V65.3791H88.5914M88.8414 69.6363L35.649 75.8039M88.8414 69.6363L35.6489 75.8038M88.5914 65.3791V65.1299V65.1291H34.4268V65.3791M88.5914 65.3791H34.4268M34.4268 65.3791H34.1768V73.4919C34.1768 74.4669 34.6807 75.3556 35.4938 75.9998M34.4268 65.3791V73.4919C34.4268 74.3757 34.8832 75.197 35.6489 75.8038M35.4938 75.9998L35.6489 75.8038M35.4938 75.9998L35.649 75.8039M35.649 75.8039L35.6489 75.8038" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M36.4481 76.0387C36.283 75.9418 36.0517 75.9738 35.934 76.1086C35.8174 76.2434 35.8546 76.4321 36.0197 76.5273C36.5214 76.8162 37.2223 76.9484 37.916 76.9434C38.6459 76.9383 39.385 76.7867 39.8991 76.5112C40.4534 76.2156 40.8003 75.914 41.0294 75.5551C41.2555 75.203 41.3587 74.8138 41.43 74.3311C41.4568 74.15 41.4826 73.8965 41.5094 73.5974C41.6302 72.3608 41.8305 70.3188 42.5737 69.7805C42.8442 69.5851 43.2736 69.6803 43.9457 70.296C44.6951 70.9834 45.3289 71.9362 45.9906 72.9285C46.6441 73.9125 47.3212 74.9326 48.1729 75.7952C48.3019 75.9258 48.5332 75.9468 48.6911 75.8415C48.849 75.7388 48.8738 75.5492 48.7468 75.4195C47.9355 74.5956 47.2727 73.5999 46.6337 72.6421C45.9524 71.6161 45.2969 70.6313 44.4979 69.8967C43.4667 68.9507 42.6842 68.8935 42.0834 69.3298C41.1203 70.029 40.9045 72.2209 40.7755 73.5477C40.7486 73.8366 40.7228 74.0826 40.6981 74.2561C40.6341 74.6773 40.5515 75.0042 40.3791 75.2754C40.2098 75.5391 39.9393 75.7699 39.4954 76.0041C39.0959 76.2181 38.5034 76.3377 37.9098 76.3402C37.3576 76.3453 36.8146 76.2484 36.4471 76.0353L36.4481 76.0387Z" fill="#792AFF" fill-opacity="0.75"/><path d="M36.8737 76.5781L36.3216 76.2543C36.2507 76.2127 36.1546 76.2367 36.1227 76.2726C36.1174 76.2789 36.1164 76.2826 36.1163 76.2829L36.1163 76.2829C36.1162 76.2831 36.1162 76.2831 36.1162 76.2831C36.1162 76.2832 36.1162 76.2831 36.1162 76.2832C36.1163 76.2834 36.1168 76.2861 36.1202 76.2906L36.8737 76.5781ZM36.8737 76.5781L36.8384 76.462L36.8737 76.5781ZM48.7468 75.4195L48.5682 75.5944C48.5756 75.6019 48.5774 75.6068 48.5777 75.6077C48.5781 75.6087 48.5779 75.6085 48.578 75.608C48.578 75.6076 48.578 75.6093 48.5755 75.613C48.573 75.6168 48.5672 75.6239 48.5548 75.632L48.5547 75.632L48.5524 75.6335C48.4898 75.6753 48.392 75.6612 48.3507 75.6195C47.5189 74.777 46.8546 73.7776 46.1989 72.7902L46.1986 72.7898L46.1781 72.7591C45.5281 71.7842 44.8827 70.8163 44.1146 70.1118L44.1145 70.1117C43.7678 69.794 43.4623 69.5898 43.1894 69.4973C42.9058 69.4012 42.6423 69.4225 42.4273 69.5779L42.4271 69.578C42.1887 69.7507 42.0144 70.0265 41.8818 70.33C41.7474 70.6376 41.6446 70.9997 41.5638 71.3743C41.4023 72.1225 41.3209 72.9559 41.2608 73.5716L41.2606 73.5731L41.2606 73.5731L41.2604 73.5751C41.2336 73.8742 41.2083 74.1213 41.1827 74.2945L41.1826 74.2946C41.1134 74.7635 41.017 75.1118 40.8191 75.4201L40.8187 75.4206C40.6197 75.7323 40.3113 76.008 39.7814 76.2907L39.781 76.2909C39.3134 76.5415 38.6176 76.6885 37.9143 76.6934L37.9142 76.6934C37.2463 76.6983 36.5943 76.5696 36.1446 76.3107L37.9098 76.3402L37.9109 76.5902C37.9111 76.5902 37.9113 76.5902 37.9115 76.5902C38.5299 76.5875 39.1659 76.464 39.6128 76.2249C40.078 75.9793 40.3887 75.7232 40.5895 75.4105L40.5901 75.4095C40.7916 75.0925 40.8801 74.7224 40.9452 74.2937L40.9452 74.2937L40.9456 74.2915C40.9713 74.1115 40.9975 73.8604 41.0244 73.5713C41.0891 72.9055 41.1741 72.043 41.3599 71.2612C41.4527 70.8708 41.5688 70.5088 41.7157 70.2065C41.8633 69.9027 42.0348 69.674 42.2303 69.5321L42.2303 69.5321C42.479 69.3514 42.7551 69.2766 43.0813 69.3403C43.4185 69.4061 43.8314 69.6246 44.3288 70.0809C45.1013 70.7911 45.7402 71.7484 46.4255 72.7804L46.4258 72.7809L46.4433 72.8071C47.0745 73.7533 47.7453 74.7589 48.5687 75.5949L48.7468 75.4195ZM48.7468 75.4195C48.8738 75.5492 48.849 75.7388 48.6911 75.8415C48.5332 75.9468 48.3019 75.9258 48.1729 75.7952C47.3222 74.9336 46.6456 73.9147 45.9928 72.9318L45.9906 72.9285L45.9807 72.9136C45.3226 71.9267 44.6913 70.98 43.9457 70.296C43.2736 69.6803 42.8442 69.5851 42.5737 69.7805M48.7468 75.4195C47.9402 74.6005 47.2805 73.6116 46.645 72.659L46.6337 72.6421C45.9524 71.6161 45.2969 70.6313 44.4979 69.8967C43.4667 68.9507 42.6842 68.8935 42.0834 69.3298C41.1203 70.029 40.9045 72.2209 40.7755 73.5477C40.7486 73.8366 40.7228 74.0826 40.6981 74.2561C40.6341 74.6773 40.5515 75.0042 40.3791 75.2754C40.2098 75.5391 39.9393 75.7699 39.4954 76.0041L42.5737 69.7805M42.5737 69.7805C41.8305 70.3188 41.6302 72.3608 41.5094 73.5974C41.4826 73.8965 41.4568 74.15 41.43 74.3311L42.5737 69.7805Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.4611 69.0679H43.7559C44.0903 69.1903 44.4591 69.4242 44.8634 69.7949C45.6708 70.532 46.3324 71.5182 47.0211 72.5466C47.6629 73.5066 48.3329 74.5038 49.1518 75.3304C49.2789 75.4604 49.2539 75.6504 49.0955 75.7534C49.0382 75.7914 48.9715 75.8125 48.9028 75.8184H104.62C104.526 75.8108 104.433 75.7728 104.369 75.707C103.512 74.8415 102.824 73.8199 102.165 72.8337C101.804 72.2924 101.447 71.7622 101.077 71.2809C100.544 70.5877 99.3258 69.2038 98.3246 69.0687H88.4611V69.0679Z" fill="white" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M61.5319 69.0687H43.7559C44.0959 69.1902 44.4732 69.4248 44.8855 69.7952C45.7111 70.5317 46.3859 71.5189 47.0883 72.5457C47.7451 73.5058 48.4262 74.5031 49.2636 75.3282C49.3932 75.459 49.3677 75.6472 49.2051 75.7518C49.1478 75.7906 49.0787 75.8125 49.0075 75.8184H69.6793C67.7889 74.2314 66.1226 72.4284 63.9707 70.7216C63.1621 70.0778 62.3523 69.5336 61.5319 69.0679V69.0687Z" fill="#792AFF" fill-opacity="0.15"/><path d="M61.4672 69.3187C62.2561 69.7705 63.0357 70.2968 63.815 70.9171L63.8154 70.9174C64.8828 71.7641 65.831 72.6356 66.7523 73.4975C66.8701 73.6077 66.9875 73.7178 67.1047 73.8277C67.7355 74.4193 68.3598 75.0047 69.0039 75.5684H49.5935C49.6098 75.4141 49.5525 75.2646 49.4411 75.1522L49.4412 75.1522L49.4391 75.1502C48.6219 74.3449 47.954 73.3684 47.2947 72.4045L47.2947 72.4045L47.2811 72.3847C46.5876 71.3708 45.8993 70.3646 45.0522 69.6089C44.9336 69.5023 44.8167 69.4057 44.7017 69.3187H61.4672Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.7766 70.8327V10.1814C88.7766 7.93905 86.4687 6.10345 83.6507 6.10345H64.6658C87.6257 24.6104 52.4555 21.9316 52.4555 57.8524C52.4555 63.6223 51.8966 66.075 53.2787 70.8335H88.7776L88.7766 70.8327Z" fill="#FF9300" fill-opacity="0.2"/><mask id="path-21-inside-1_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7333 28.2639H83.0877C83.3552 28.2639 83.577 28.3766 83.577 28.51C83.577 28.6424 83.3562 28.7531 83.0877 28.7531H50.7333C50.4669 28.7531 50.2471 28.6434 50.2471 28.51C50.2471 28.3756 50.4658 28.2639 50.7333 28.2639Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7333 28.2639H83.0877C83.3552 28.2639 83.577 28.3766 83.577 28.51C83.577 28.6424 83.3562 28.7531 83.0877 28.7531H50.7333C50.4669 28.7531 50.2471 28.6434 50.2471 28.51C50.2471 28.3756 50.4658 28.2639 50.7333 28.2639Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7333 28.7639H83.0877V27.7639H50.7333V28.7639ZM83.0877 28.7639C83.1199 28.7639 83.1485 28.7674 83.1711 28.7723C83.1823 28.7747 83.1912 28.7773 83.1977 28.7795C83.2009 28.7806 83.2033 28.7816 83.205 28.7823C83.2067 28.783 83.2075 28.7834 83.2074 28.7834C83.2074 28.7833 83.2063 28.7828 83.2043 28.7816C83.2023 28.7804 83.1991 28.7783 83.195 28.7753C83.1872 28.7695 83.1734 28.7581 83.1575 28.7395C83.1255 28.7019 83.077 28.6232 83.077 28.51H84.077C84.077 28.1497 83.7949 27.9597 83.6579 27.8906C83.4872 27.8044 83.2851 27.7639 83.0877 27.7639V28.7639ZM83.077 28.51C83.077 28.3959 83.1262 28.3163 83.159 28.2781C83.1752 28.2592 83.1894 28.2477 83.1974 28.2419C83.2016 28.2388 83.2048 28.2367 83.2069 28.2355C83.209 28.2342 83.2101 28.2336 83.2102 28.2336C83.2102 28.2336 83.2094 28.234 83.2077 28.2347C83.206 28.2354 83.2036 28.2363 83.2003 28.2374C83.1937 28.2397 83.1847 28.2422 83.1733 28.2447C83.1502 28.2496 83.121 28.2531 83.0877 28.2531V29.2531C83.284 29.2531 83.4854 29.2137 83.6559 29.1288C83.7923 29.0609 84.077 28.8719 84.077 28.51H83.077ZM83.0877 28.2531H50.7333V29.2531H83.0877V28.2531ZM50.7333 28.2531C50.7005 28.2531 50.6717 28.2496 50.649 28.2448C50.6379 28.2424 50.6291 28.2399 50.6228 28.2378C50.6163 28.2356 50.6135 28.2342 50.6137 28.2343C50.6138 28.2343 50.6188 28.2368 50.6272 28.243C50.6355 28.2491 50.6497 28.2608 50.666 28.2798C50.6992 28.3187 50.7471 28.3978 50.7471 28.51H49.7471C49.7471 28.8706 50.0288 29.0599 50.1669 29.1289C50.3373 29.214 50.5382 29.2531 50.7333 29.2531V28.2531ZM50.7471 28.51C50.7471 28.6199 50.7008 28.6978 50.6682 28.7366C50.6523 28.7555 50.6382 28.7673 50.6299 28.7734C50.6215 28.7797 50.6164 28.7823 50.6162 28.7824C50.6158 28.7826 50.6185 28.7813 50.6246 28.7791C50.6307 28.777 50.6393 28.7745 50.6502 28.7722C50.6724 28.7674 50.7007 28.7639 50.7333 28.7639V27.7639C50.5366 27.7639 50.3346 27.8039 50.1636 27.8907C50.0231 27.962 49.7471 28.1529 49.7471 28.51H50.7471Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-21-inside-1_3430_36568)"/><mask id="path-23-inside-2_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 30.5434H61.5784C61.8459 30.5434 62.0667 30.654 62.0667 30.7864C62.0667 30.9188 61.8459 31.0305 61.5784 31.0305H50.7335C50.466 31.0305 50.2451 30.9209 50.2451 30.7864C50.2451 30.652 50.4639 30.5434 50.7335 30.5434Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 30.5434H61.5784C61.8459 30.5434 62.0667 30.654 62.0667 30.7864C62.0667 30.9188 61.8459 31.0305 61.5784 31.0305H50.7335C50.466 31.0305 50.2451 30.9209 50.2451 30.7864C50.2451 30.652 50.4639 30.5434 50.7335 30.5434Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7335 31.0434H61.5784V30.0434H50.7335V31.0434ZM61.5784 31.0434C61.6113 31.0434 61.6403 31.0469 61.6632 31.0517C61.6745 31.0542 61.6834 31.0567 61.69 31.0589C61.6932 31.06 61.6956 31.061 61.6973 31.0617C61.699 31.0624 61.6998 31.0628 61.6997 31.0627C61.6996 31.0627 61.6985 31.0621 61.6964 31.0609C61.6944 31.0596 61.6911 31.0575 61.6869 31.0545C61.6789 31.0486 61.6648 31.0371 61.6486 31.0183C61.6159 30.9801 61.5667 30.9005 61.5667 30.7864H62.5667C62.5667 30.4247 62.2822 30.2357 62.146 30.1678C61.9757 30.0829 61.7744 30.0434 61.5784 30.0434V31.0434ZM61.5667 30.7864C61.5667 30.6725 61.6157 30.5932 61.6481 30.5554C61.6641 30.5366 61.6781 30.5252 61.686 30.5194C61.6902 30.5163 61.6934 30.5142 61.6954 30.513C61.6975 30.5118 61.6986 30.5112 61.6987 30.5112C61.6987 30.5111 61.698 30.5115 61.6963 30.5122C61.6946 30.5129 61.6922 30.5138 61.689 30.5149C61.6825 30.5172 61.6736 30.5197 61.6624 30.5221C61.6397 30.527 61.611 30.5305 61.5784 30.5305V31.5305C61.7752 31.5305 61.9767 31.4905 62.147 31.405C62.2832 31.3367 62.5667 31.1473 62.5667 30.7864H61.5667ZM61.5784 30.5305H50.7335V31.5305H61.5784V30.5305ZM50.7335 30.5305C50.7002 30.5305 50.6711 30.527 50.6481 30.5221C50.6368 30.5197 50.6279 30.5172 50.6215 30.515C50.6149 30.5128 50.612 30.5113 50.6121 30.5114C50.6121 30.5114 50.6171 30.5139 50.6255 30.5201C50.6338 30.5261 50.648 30.5378 50.6643 30.5568C50.6975 30.5959 50.7451 30.6748 50.7451 30.7864H49.7451C49.7451 31.1473 50.0268 31.337 50.1658 31.4063C50.3367 31.4915 50.5382 31.5305 50.7335 31.5305V30.5305ZM50.7451 30.7864C50.7451 30.8969 50.6984 30.9757 50.6645 31.0155C50.6481 31.0349 50.6335 31.0468 50.625 31.0531C50.6163 31.0595 50.6111 31.0621 50.6108 31.0622C50.6104 31.0624 50.6132 31.061 50.6197 31.0588C50.626 31.0567 50.6349 31.0542 50.6463 31.0518C50.6694 31.0469 50.6991 31.0434 50.7335 31.0434V30.0434C50.5383 30.0434 50.3368 30.0816 50.1656 30.1668C50.0247 30.2368 49.7451 30.4266 49.7451 30.7864H50.7451Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-23-inside-2_3430_36568)"/><mask id="path-25-inside-3_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 32.8149H54.3747C54.6443 32.8149 54.863 32.9266 54.863 33.059C54.863 33.1934 54.6443 33.3041 54.3747 33.3041H50.7335C50.466 33.3041 50.2451 33.1934 50.2451 33.059C50.2451 32.9266 50.4639 32.8149 50.7335 32.8149Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 32.8149H54.3747C54.6443 32.8149 54.863 32.9266 54.863 33.059C54.863 33.1934 54.6443 33.3041 54.3747 33.3041H50.7335C50.466 33.3041 50.2451 33.1934 50.2451 33.059C50.2451 32.9266 50.4639 32.8149 50.7335 32.8149Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7335 33.3149H54.3747V32.3149H50.7335V33.3149ZM54.3747 33.3149C54.4081 33.3149 54.4371 33.3184 54.4598 33.3233C54.471 33.3257 54.4798 33.3283 54.4861 33.3304C54.4925 33.3326 54.4954 33.3341 54.4952 33.334C54.4952 33.334 54.4902 33.3315 54.4819 33.3254C54.4738 33.3193 54.4597 33.3077 54.4437 33.2888C54.4111 33.2505 54.363 33.1716 54.363 33.059H55.363C55.363 32.6999 55.0827 32.5101 54.9447 32.4407C54.7739 32.3548 54.572 32.3149 54.3747 32.3149V33.3149ZM54.363 33.059C54.363 32.9489 54.4095 32.8706 54.4426 32.8314C54.4587 32.8123 54.473 32.8005 54.4813 32.7943C54.4899 32.788 54.495 32.7854 54.4952 32.7853C54.4956 32.7851 54.4928 32.7865 54.4865 32.7887C54.4802 32.7908 54.4715 32.7933 54.4603 32.7957C54.4376 32.8006 54.4084 32.8041 54.3747 32.8041V33.8041C54.5712 33.8041 54.7733 33.7648 54.9447 33.6786C55.0855 33.6077 55.363 33.4172 55.363 33.059H54.363ZM54.3747 32.8041H50.7335V33.8041H54.3747V32.8041ZM50.7335 32.8041C50.7005 32.8041 50.6717 32.8006 50.6489 32.7958C50.6377 32.7934 50.6288 32.7908 50.6224 32.7886C50.616 32.7864 50.613 32.785 50.6132 32.785C50.6132 32.785 50.6181 32.7875 50.6264 32.7936C50.6346 32.7997 50.6487 32.8113 50.6648 32.8302C50.6977 32.8689 50.7451 32.9475 50.7451 33.059H49.7451C49.7451 33.4191 50.0257 33.6091 50.1648 33.6789C50.3357 33.7646 50.5375 33.8041 50.7335 33.8041V32.8041ZM50.7451 33.059C50.7451 33.1716 50.6971 33.2505 50.6645 33.2888C50.6484 33.3077 50.6343 33.3193 50.6262 33.3254C50.6179 33.3315 50.613 33.334 50.6129 33.334C50.6127 33.3341 50.6156 33.3326 50.622 33.3304C50.6284 33.3283 50.6372 33.3257 50.6484 33.3233C50.6711 33.3184 50.7001 33.3149 50.7335 33.3149V32.3149C50.5361 32.3149 50.3342 32.3548 50.1635 32.4407C50.0254 32.5101 49.7451 32.6999 49.7451 33.059H50.7451Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-25-inside-3_3430_36568)"/><mask id="path-27-inside-4_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M56.003 32.8149H83.0873C83.3548 32.8149 83.5767 32.9266 83.5767 33.059C83.5767 33.1934 83.3559 33.3041 83.0873 33.3041H56.003C55.7355 33.3041 55.5137 33.1934 55.5137 33.059C55.5137 32.9266 55.7345 32.8149 56.003 32.8149Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M56.003 32.8149H83.0873C83.3548 32.8149 83.5767 32.9266 83.5767 33.059C83.5767 33.1934 83.3559 33.3041 83.0873 33.3041H56.003C55.7355 33.3041 55.5137 33.1934 55.5137 33.059C55.5137 32.9266 55.7345 32.8149 56.003 32.8149Z" fill="#FF9300" fill-opacity="0.5"/><path d="M56.003 33.3149H83.0873V32.3149H56.003V33.3149ZM83.0873 33.3149C83.1199 33.3149 83.1487 33.3184 83.1717 33.3233C83.183 33.3258 83.192 33.3283 83.1986 33.3306C83.2019 33.3317 83.2043 33.3327 83.2061 33.3334C83.2078 33.3341 83.2087 33.3345 83.2086 33.3345C83.2086 33.3345 83.2076 33.334 83.2056 33.3328C83.2036 33.3316 83.2005 33.3296 83.1963 33.3265C83.1885 33.3208 83.1746 33.3094 83.1585 33.2908C83.1262 33.2532 83.0767 33.1736 83.0767 33.059H84.0767C84.0767 32.697 83.7915 32.5079 83.656 32.4401C83.4857 32.3549 83.2841 32.3149 83.0873 32.3149V33.3149ZM83.0767 33.059C83.0767 32.9475 83.1241 32.8688 83.1571 32.8301C83.1732 32.8111 83.1874 32.7995 83.1955 32.7935C83.1998 32.7903 83.2031 32.7882 83.2053 32.7869C83.2074 32.7856 83.2086 32.785 83.2088 32.7849C83.2089 32.7848 83.2059 32.7863 83.1994 32.7885C83.1929 32.7907 83.1841 32.7933 83.1727 32.7957C83.1498 32.8006 83.1207 32.8041 83.0873 32.8041V33.8041C83.2836 33.8041 83.4855 33.7647 83.6566 33.679C83.7959 33.6093 84.0767 33.4192 84.0767 33.059H83.0767ZM83.0873 32.8041H56.003V33.8041H83.0873V32.8041ZM56.003 32.8041C55.9701 32.8041 55.9411 32.8006 55.9181 32.7957C55.9068 32.7933 55.8979 32.7907 55.8914 32.7885C55.8848 32.7863 55.8817 32.7848 55.8817 32.7848C55.8818 32.7848 55.883 32.7854 55.885 32.7867C55.8871 32.7879 55.8904 32.79 55.8946 32.7931C55.9027 32.7991 55.9168 32.8106 55.9329 32.8295C55.9657 32.868 56.0137 32.9468 56.0137 33.059H55.0137C55.0137 33.4201 55.296 33.6099 55.4344 33.6791C55.6053 33.7646 55.8071 33.8041 56.003 33.8041V32.8041ZM56.0137 33.059C56.0137 33.173 55.9646 33.2523 55.9322 33.2902C55.9162 33.3089 55.9022 33.3203 55.8942 33.3262C55.8901 33.3293 55.8868 33.3313 55.8848 33.3326C55.8828 33.3338 55.8816 33.3344 55.8816 33.3344C55.8815 33.3344 55.8823 33.334 55.884 33.3333C55.8857 33.3326 55.8881 33.3317 55.8914 33.3306C55.8979 33.3283 55.9068 33.3258 55.9181 33.3233C55.9411 33.3184 55.97 33.3149 56.003 33.3149V32.3149C55.806 32.3149 55.6043 32.3549 55.4337 32.4403C55.2973 32.5086 55.0137 32.6979 55.0137 33.059H56.0137Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-27-inside-4_3430_36568)"/><mask id="path-29-inside-5_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M63.7784 30.5434H83.0878C83.3573 30.5434 83.5771 30.654 83.5771 30.7864C83.5771 30.9188 83.3542 31.0305 83.0878 31.0305H63.7784C63.513 31.0305 63.2891 30.9209 63.2891 30.7864C63.2891 30.652 63.5099 30.5434 63.7784 30.5434Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M63.7784 30.5434H83.0878C83.3573 30.5434 83.5771 30.654 83.5771 30.7864C83.5771 30.9188 83.3542 31.0305 83.0878 31.0305H63.7784C63.513 31.0305 63.2891 30.9209 63.2891 30.7864C63.2891 30.652 63.5099 30.5434 63.7784 30.5434Z" fill="#FF9300" fill-opacity="0.5"/><path d="M63.7784 31.0434H83.0878V30.0434H63.7784V31.0434ZM83.0878 31.0434C83.1215 31.0434 83.1509 31.0469 83.174 31.0518C83.1854 31.0542 83.1943 31.0568 83.2008 31.059C83.2074 31.0612 83.2105 31.0628 83.2104 31.0627C83.2103 31.0627 83.2091 31.0621 83.207 31.0608C83.2049 31.0595 83.2016 31.0574 83.1973 31.0543C83.1892 31.0483 83.175 31.0367 83.1588 31.0178C83.1258 30.9793 83.0771 30.8999 83.0771 30.7864H84.0771C84.0771 30.4254 83.794 30.2363 83.6567 30.1678C83.486 30.0827 83.2844 30.0434 83.0878 30.0434V31.0434ZM83.0771 30.7864C83.0771 30.6711 83.1272 30.5914 83.1594 30.554C83.1894 30.5191 83.213 30.5088 83.209 30.5108C83.201 30.5147 83.1577 30.5305 83.0878 30.5305V31.5305C83.2843 31.5305 83.4856 31.4904 83.6558 31.4054C83.7904 31.3382 84.0771 31.1493 84.0771 30.7864H83.0771ZM83.0878 30.5305H63.7784V31.5305H83.0878V30.5305ZM63.7784 30.5305C63.7459 30.5305 63.717 30.5271 63.6938 30.5221C63.6824 30.5197 63.6733 30.5171 63.6666 30.5149C63.6633 30.5137 63.6607 30.5128 63.659 30.5121C63.6581 30.5117 63.6574 30.5114 63.657 30.5112C63.6565 30.511 63.6563 30.5109 63.6563 30.5109C63.6563 30.5109 63.6566 30.511 63.6571 30.5113C63.6576 30.5116 63.6584 30.512 63.6594 30.5126C63.6614 30.5138 63.6646 30.5158 63.6688 30.5189C63.6767 30.5247 63.6908 30.5361 63.707 30.5549C63.74 30.5932 63.7891 30.6727 63.7891 30.7864H62.7891C62.7891 31.1501 63.0756 31.3392 63.2121 31.4068C63.3826 31.4914 63.5837 31.5305 63.7784 31.5305V30.5305ZM63.7891 30.7864C63.7891 30.8982 63.7413 30.9775 63.7076 31.0169C63.6912 31.0361 63.6767 31.0478 63.6684 31.0539C63.6599 31.0601 63.6549 31.0626 63.6549 31.0626C63.6547 31.0627 63.6577 31.0612 63.6644 31.059C63.6709 31.0568 63.68 31.0542 63.6915 31.0518C63.7148 31.0469 63.7445 31.0434 63.7784 31.0434V30.0434C63.5836 30.0434 63.3823 30.0817 63.2112 30.1664C63.072 30.2353 62.7891 30.4246 62.7891 30.7864H63.7891Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-29-inside-5_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.8383 27.3766H47.1452C47.8139 27.3766 48.3624 27.9226 48.3624 28.5907V32.9796C48.3624 33.6487 47.8129 34.1937 47.1452 34.1937H42.8383C42.1664 34.1937 41.6211 33.6476 41.6211 32.9796V28.5907C41.6211 27.9226 42.1664 27.3766 42.8383 27.3766Z" fill="#792AFF" fill-opacity="0.1"/><path d="M42.8383 27.6266H47.1452C47.6766 27.6266 48.1124 28.0614 48.1124 28.5907V32.9796C48.1124 33.5096 47.6758 33.9437 47.1452 33.9437H42.8383C42.3042 33.9437 41.8711 33.5093 41.8711 32.9796V28.5907C41.8711 28.061 42.3042 27.6266 42.8383 27.6266Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.4221 37.085H70.9786C71.2575 37.085 71.4846 37.1987 71.4846 37.3383C71.4846 37.4748 71.2544 37.5907 70.9786 37.5907H42.4221C42.1411 37.5907 41.9141 37.4779 41.9141 37.3383C41.9141 37.1987 42.1411 37.085 42.4221 37.085Z" fill="#FF9300" fill-opacity="0.5"/><path d="M42.4221 37.335H70.9786C71.0013 37.335 71.0231 37.3359 71.0437 37.3377C71.023 37.3396 71.0012 37.3407 70.9786 37.3407H42.4221C42.3988 37.3407 42.3766 37.3397 42.3555 37.3378C42.3766 37.336 42.3988 37.335 42.4221 37.335Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.42 39.4481H62.3203C62.5992 39.4481 62.8284 39.5639 62.8284 39.7014C62.8284 39.8379 62.5972 39.9548 62.3203 39.9548H42.42C42.1401 39.9548 41.9141 39.84 41.9141 39.7014C41.9141 39.5608 42.1411 39.4481 42.42 39.4481Z" fill="#FF9300" fill-opacity="0.5"/><path d="M42.42 39.6981H62.3203C62.3449 39.6981 62.3684 39.6992 62.3906 39.7014C62.3683 39.7036 62.3448 39.7048 62.3203 39.7048H42.42C42.3949 39.7048 42.371 39.7036 42.3484 39.7014C42.371 39.6993 42.3949 39.6981 42.42 39.6981Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M52.2911 23.1545H48.9236V15.2878H47.1123V24.6799H52.2901V23.1535L52.2911 23.1545ZM57.0573 15.1399C54.5949 15.1399 53.0397 17.2061 53.0397 20.0138C53.0397 22.8215 54.4466 24.8143 56.9349 24.8143C59.274 24.8143 60.939 23.1421 60.939 19.868C60.939 17.3633 59.6648 15.1399 57.0583 15.1399H57.0573ZM57.0054 16.6187C58.3906 16.6187 59.0407 18.2196 59.0407 19.9404C59.0407 21.835 58.3408 23.3355 56.994 23.3355C55.6472 23.3355 54.937 21.8236 54.937 19.9776C54.937 18.2547 55.585 16.6187 57.0054 16.6187ZM66.5844 15.2878H64.3066L61.6368 24.6799H63.474L64.1231 22.201H66.6456L67.3195 24.6799H69.2158L66.5844 15.2878ZM64.3802 20.8329L64.9432 18.6829C65.0769 18.1337 65.2366 17.3633 65.36 16.7769H65.3973C65.5186 17.3499 65.6783 18.1079 65.8255 18.6943L66.3885 20.8329H64.3812H64.3802ZM77.3008 15.2878H75.6585V18.1079C75.6585 19.6343 75.7062 20.9673 75.8171 22.1638H75.7933C75.4128 21.1876 74.8871 20.0397 74.3853 19.0976L72.3988 15.2867H70.4413V24.6789H72.0826V21.7967C72.0826 20.1224 72.0463 18.8049 71.9716 17.6436H72.0204C72.4123 18.6684 72.9638 19.8297 73.4667 20.7946L75.4864 24.6789H77.2997V15.2867L77.3008 15.2878Z" fill="url(#paint3_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M85.1315 36.4293C75.5497 46.9839 61.7623 55.2913 45.1461 58.6872C42.308 53.9535 39.9951 49.0813 38.2168 44.1548C52.7508 42.2015 64.788 35.6809 73.0511 27.25C76.6396 30.6385 80.6764 33.7275 85.1315 36.4293Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M83.7105 36.5811C74.4327 46.4658 61.3524 54.2512 45.6629 57.6022C43.1962 53.4653 41.1271 49.2199 39.4727 44.9278C53.4195 42.8023 65.0328 36.5174 73.1833 28.4065C76.3554 31.3833 79.8726 34.13 83.7105 36.5811Z" fill="url(#paint4_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M71.1197 34.6132C70.1237 35.4271 68.8181 35.7639 67.6157 35.618C62.4721 39.3451 56.4676 42.423 49.7614 44.5391C49.3581 45.628 48.3264 46.5636 46.8126 46.9546C45.2951 47.3494 43.7475 47.6956 42.1699 47.9912C43.1135 50.068 44.1564 52.128 45.2951 54.1619C46.9646 53.7989 48.604 53.3854 50.2135 52.9176C51.7686 52.4704 53.4475 52.5079 54.9087 52.9251C62.262 50.3075 68.8481 46.6552 74.5019 42.2939C74.6013 41.218 75.1903 40.1216 76.2201 39.2459C77.5876 38.0859 78.8895 36.8809 80.1294 35.6348C78.2592 34.2764 76.4734 32.8488 74.7795 31.3539C73.6202 32.4784 72.3972 33.5655 71.1179 34.6132H71.1197Z" fill="#F9F9F9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M67.2153 41.6077C69.3406 43.881 69.1455 47.0917 66.2961 48.6952C63.4542 50.2987 59.2936 49.3725 57.4815 46.7587C55.6657 44.1486 56.613 41.1062 59.1323 39.8302C61.6647 38.5598 65.0712 39.3231 67.2153 41.6077Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M54.1556 48.5475C54.8422 49.5766 54.2794 50.8395 52.82 51.3297C51.3644 51.82 49.6104 51.3017 48.9877 50.2184C48.3649 49.135 49.0589 47.9432 50.4508 47.5072C51.8464 47.0731 53.469 47.5203 54.1556 48.5475Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M74.0348 37.1284C74.9296 37.9255 74.8395 39.2782 73.744 40.1408C72.6523 41.0052 71.0034 40.994 70.1631 40.1333C69.3114 39.2726 69.5422 37.9442 70.5833 37.1471C71.6262 36.35 73.1325 36.3369 74.0329 37.1303L74.0348 37.1284Z" fill="#792AFF" fill-opacity="0.5"/><path d="M64.5389 43.3175C64.8489 43.4128 65.0991 43.6135 65.2897 43.9197C65.5998 44.4178 65.6834 44.9051 65.5408 45.382C65.3981 45.8588 65.0491 46.2694 64.4936 46.614L64.2972 46.7358L64.7719 47.4984L64.3788 47.7422L63.9041 46.9797L62.4263 47.8965L59.5292 43.2432C59.4679 43.1447 59.4095 43.0803 59.3543 43.0496C59.299 43.0194 59.2333 42.9953 59.157 42.978C59.1095 42.9717 59.071 42.9595 59.0417 42.9418C59.0124 42.9239 58.9918 42.8938 58.9796 42.8509C58.9392 42.7394 58.933 42.6157 58.9611 42.4796C58.9889 42.3435 59.0501 42.2462 59.1444 42.1877C59.2439 42.126 59.3622 42.1175 59.4987 42.1619L60.5126 41.5329L60.0329 40.7625L60.426 40.5186L60.9057 41.289L61.0785 41.1818C61.5867 40.8666 62.0713 40.7493 62.5322 40.8302C62.9928 40.9112 63.3573 41.167 63.6254 41.5975C63.8223 41.9138 63.9032 42.2071 63.8679 42.4771C63.8327 42.7472 63.7079 43.0134 63.4934 43.2756L63.508 43.299C63.8849 43.2164 64.2285 43.2226 64.5384 43.3177L64.5389 43.3175ZM61.2507 44.2571L61.9424 43.828L60.9057 42.1628L60.214 42.5919L61.2507 44.2571ZM63.5119 46.3493L62.3394 44.4662L61.6477 44.8953L62.8202 46.7784L63.5119 46.3493ZM61.2986 41.9191L62.3353 43.5843L62.3431 43.5794C62.6522 43.3877 62.8515 43.1652 62.9409 42.9121C63.0301 42.6591 62.9861 42.3898 62.8083 42.1044C62.6566 41.8606 62.4585 41.7118 62.2147 41.6583C61.9705 41.6047 61.7044 41.6673 61.4163 41.846L61.2984 41.9192L61.2986 41.9191ZM64.6627 45.3012C64.7327 45.0349 64.6693 44.7434 64.4722 44.4268C64.2882 44.1312 64.0504 43.9623 63.7589 43.9201C63.4675 43.878 63.1489 43.964 62.803 44.1786L62.7323 44.2224L63.9048 46.1056L64.062 46.008C64.3922 45.8032 64.5922 45.5677 64.6623 45.3011L64.6627 45.3012Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M77.5629 66.5975C78.4275 65.3943 79.6811 64.3733 79.989 62.9259C80.3549 61.2151 79.2708 59.4921 79.4273 57.7515C79.6251 55.5595 81.6532 54.0424 82.695 52.1046C83.7177 50.2075 83.1642 49.6136 83.1534 47.4596C83.1414 44.2148 85.5921 39.9902 89.1965 40.9053C92.8547 41.8352 92.4377 45.2035 91.9351 47.7501C91.5753 49.563 91.8842 49.8057 92.4132 51.5748C93.055 53.7132 94.8721 55.7172 94.2667 57.8657C93.8926 59.1947 92.6538 60.1408 92.2555 61.464C91.8052 62.9628 92.5244 64.5244 92.8482 66.0542C93.5334 69.2769 92.9301 72.0158 90.7026 74.8176C90.4307 75.1051 90.1452 75.3686 89.8445 75.6135C86.5642 75.6148 82.9062 75.6514 78.9285 75.7184C78.8709 75.6666 78.8151 75.613 78.7582 75.5577C76.472 73.3094 75.618 69.2992 77.5638 66.5975L77.5629 66.5975Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M87.888 43.9303C87.9352 43.7813 88.0965 43.6998 88.2477 43.7486C88.3971 43.7974 88.4788 43.9583 88.4298 44.1073C88.186 44.855 87.9898 45.6347 87.828 46.4453C87.9054 46.3681 87.9865 46.2925 88.0702 46.2176C88.44 45.8879 88.8674 45.5951 89.3445 45.3394C89.483 45.2634 89.6548 45.3173 89.7309 45.4546C89.8044 45.5938 89.7512 45.7642 89.6136 45.8401C89.1735 46.0754 88.7855 46.3433 88.4487 46.6421C88.1306 46.9268 87.8564 47.2391 87.6272 47.5828C87.3527 49.333 87.2111 51.2088 87.0976 53.1838C87.4005 52.6545 87.73 52.1787 88.0834 51.7546C88.6967 51.0161 89.3878 50.4308 90.1568 50.0013C90.2943 49.9228 90.4676 49.9721 90.5446 50.1094C90.6206 50.245 90.5738 50.4186 90.4361 50.4945C89.7308 50.8899 89.0933 51.4318 88.5215 52.1178C87.9548 52.7991 87.4548 53.6253 87.0232 54.5918L86.995 55.1273C86.9343 56.3043 86.8707 57.5124 86.7882 58.7461C86.7888 58.7575 86.7885 58.769 86.7847 58.7816C86.6979 60.0703 86.5852 61.3885 86.4277 62.7303C86.9941 61.7506 87.613 60.8813 88.2852 60.1169C89.2682 58.9968 90.364 58.1074 91.5734 57.4496C91.7111 57.3737 91.8837 57.4258 91.9597 57.5622C92.0341 57.7005 91.9836 57.8726 91.8459 57.9476C90.6969 58.5758 89.6511 59.4229 88.7132 60.4904C87.777 61.5569 86.9449 62.8432 86.2153 64.3475C85.9565 66.1296 85.6031 67.9474 85.1043 69.7939L85.102 69.8002C84.8575 70.7059 84.5779 71.6188 84.2569 72.5355C85.0804 71.7545 85.9602 71.0704 86.9017 70.4838C88.2125 69.6646 89.6307 69.0376 91.1595 68.6018C91.3103 68.5579 91.4679 68.6451 91.5111 68.7965C91.556 68.9478 91.4676 69.105 91.3159 69.1489C89.8368 69.5707 88.4637 70.1769 87.2033 70.9635C85.9558 71.7423 84.8132 72.7021 83.7736 73.8401C83.5343 74.4517 83.2765 75.066 82.9975 75.6805L82.3669 75.6872C83.3035 73.6638 84.0082 71.6559 84.5454 69.6786C84.58 68.2876 84.3447 66.9122 83.8375 65.5515C83.3233 64.1727 82.5307 62.802 81.4567 61.4475C81.3601 61.3244 81.3809 61.1442 81.5025 61.0488C81.6258 60.9507 81.8047 60.9715 81.9031 61.0945C83.0115 62.4984 83.834 63.9162 84.3688 65.3522C84.6727 66.168 84.8849 66.9895 85.0034 67.8131C85.6987 64.6953 86.0162 61.6648 86.2147 58.779C85.8194 57.5795 85.3941 56.5704 84.9377 55.7499C84.4851 54.9329 84.0061 54.3087 83.501 53.8818C83.3825 53.7793 83.368 53.6009 83.4707 53.4808C83.5725 53.3626 83.7522 53.3464 83.8699 53.4489C84.4306 53.9242 84.952 54.5991 85.4368 55.4746C85.7392 56.0196 86.0279 56.6438 86.3058 57.3498C86.3498 56.5876 86.3881 55.8362 86.427 55.0964L86.4577 54.5229C86.4571 54.5114 86.4576 54.5034 86.4588 54.4919C86.6624 50.5714 86.8895 47.0036 87.8906 43.9302L87.888 43.9303Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M87.3449 67.6517C88.3061 67.0694 89.4822 66.7284 90.1253 65.8111C90.8863 64.727 90.6453 63.2062 91.2702 62.0409C92.0592 60.5715 93.9214 60.1142 95.2262 59.0729C96.501 58.0507 96.2891 57.4764 96.9223 55.9717C97.8783 53.7064 100.842 51.4892 103.081 53.1909C105.353 54.9209 104.061 57.1443 102.954 58.7686C102.165 59.9247 102.308 60.1857 102.153 61.5741C101.964 63.2537 102.634 65.1876 101.573 66.5042C100.919 67.3197 99.7749 67.6123 99.1039 68.4163C98.3454 69.3263 98.384 70.6271 98.1553 71.7898C97.8473 73.3546 97.2305 74.6263 96.2041 75.6781C93.3531 75.6291 90.0485 75.6211 86.358 75.6469C86.0115 75.2243 85.7244 74.7526 85.5152 74.2461C84.5911 72.0048 85.1884 68.9582 87.3458 67.6517L87.3449 67.6517Z" fill="url(#paint5_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M101.154 54.8489C101.231 54.7585 101.367 54.7497 101.456 54.8281C101.546 54.9047 101.555 55.0402 101.478 55.1315C101.086 55.5788 100.717 56.0655 100.364 56.5823C100.441 56.5519 100.521 56.5231 100.602 56.496C100.956 56.3763 101.34 56.2966 101.75 56.2588C101.867 56.2475 101.97 56.3358 101.983 56.4552C101.992 56.5721 101.906 56.6763 101.788 56.6876C101.41 56.7229 101.06 56.7937 100.737 56.903C100.43 57.0052 100.147 57.1424 99.8855 57.3147C99.1732 58.4518 98.518 59.7167 97.8534 61.0579C98.2214 60.7814 98.5907 60.5462 98.9631 60.3541C99.6114 60.0217 100.266 59.8186 100.93 59.7458C101.048 59.7336 101.154 59.8199 101.166 59.9358C101.181 60.0542 101.095 60.1601 100.978 60.1749C100.369 60.2397 99.7625 60.4288 99.1625 60.7375C98.5642 61.0462 97.972 61.472 97.3842 62.0176L97.2047 62.3825C96.8141 63.1845 96.4104 64.0066 95.9871 64.8421C95.9822 64.8503 95.979 64.8576 95.975 64.8657C95.5314 65.7384 95.061 66.6222 94.5536 67.5105C95.2377 66.9962 95.9283 66.5733 96.6218 66.2404C97.6381 65.7505 98.6671 65.455 99.7042 65.3524C99.824 65.341 99.9285 65.4292 99.9408 65.5469C99.9503 65.6638 99.8637 65.7697 99.7466 65.781C98.7614 65.8783 97.7814 66.1615 96.808 66.6271C95.84 67.0933 94.8798 67.7445 93.9227 68.5782C93.2144 69.7408 92.4291 70.9037 91.531 72.0407L91.5278 72.0461L91.526 72.0462C91.0884 72.6044 90.623 73.157 90.1254 73.7015C90.9319 73.3989 91.7483 73.184 92.5785 73.0515C93.7328 72.8696 94.908 72.8526 96.1014 73.0007C96.2207 73.0148 96.3041 73.1218 96.2907 73.2399C96.2763 73.3571 96.1674 73.4412 96.0481 73.4271C94.8912 73.2833 93.7567 73.3 92.6447 73.477C91.5449 73.6517 90.4628 73.9816 89.402 74.4676C89.0126 74.8618 88.6088 75.2514 88.1853 75.6368L87.5341 75.6393C88.9313 74.4167 90.1284 73.1232 91.1751 71.795C91.6125 70.8379 91.8587 69.8085 91.9085 68.7115C91.9618 67.5975 91.813 66.4083 91.4685 65.147C91.4378 65.032 91.5052 64.9138 91.6204 64.8832C91.7356 64.8517 91.8541 64.9189 91.8848 65.0338C92.243 66.3377 92.393 67.5692 92.3386 68.73C92.3078 69.3874 92.2112 70.0208 92.0497 70.631C93.4617 68.6636 94.5835 66.6467 95.5786 64.6945C95.658 63.7442 95.6614 62.9144 95.5878 62.2067C95.5145 61.5035 95.3681 60.9293 95.1425 60.4819C95.0901 60.3761 95.1339 60.2458 95.2409 60.1936C95.3461 60.1405 95.4758 60.1833 95.529 60.2891C95.7766 60.7848 95.9407 61.4111 96.0175 62.1644C96.0673 62.6341 96.0816 63.1541 96.0668 63.7268C96.3234 63.2097 96.5741 62.6981 96.8198 62.1939L97.012 61.8019C97.0144 61.7956 97.0193 61.7883 97.0216 61.782C98.3284 59.1112 99.5446 56.6914 101.157 54.8479L101.154 54.8489Z" fill="white"/><path d="M22.8188 58.0477C22.4203 58.9984 21.9771 59.9743 21.4899 60.9572C21.0356 61.8707 20.5614 62.7503 20.0797 63.5864L15.5098 63.1148C16.37 62.9511 17.6331 61.5484 18.6044 59.601C19.6155 57.5723 19.9581 55.6268 19.4901 54.8883L22.8188 58.0477Z" fill="url(#paint6_linear_3430_36568)"/><path d="M47.0415 69.0195C47.0415 69.0195 45.7257 74.5444 42.7527 77.3487C41.7497 76.8387 40.7659 76.3671 39.8194 75.9377C39.3607 75.728 38.9132 75.5253 38.4707 75.3358C41.4437 72.5309 42.991 66.6421 42.991 66.6421C44.2454 67.4285 45.5898 68.2205 47.0421 69.0195H47.0415Z" fill="#792AFF" fill-opacity="0.75"/><path d="M43.2326 66.7049C43.2604 66.5991 43.2162 66.4876 43.1235 66.4295C36.22 62.1048 32.2342 58.0229 29.9768 55.0319C28.8479 53.5362 28.1506 52.3125 27.7365 51.4664C27.5294 51.0433 27.3931 50.7146 27.3091 50.4933C27.2671 50.3827 27.2381 50.2989 27.2199 50.2437C27.2108 50.2161 27.2044 50.1956 27.2004 50.1824L27.1961 50.1682L27.1952 50.1652L27.1952 50.1651L27.1951 50.1649C27.1627 50.0504 27.0541 49.9746 26.9354 49.9838C26.8167 49.9929 26.7209 50.0845 26.7064 50.2027L26.6985 50.2676C26.6985 50.2678 26.6985 50.268 26.6984 50.2682C26.2881 53.4913 25.0289 57.5136 22.9934 61.6273C22.9839 61.6416 22.9753 61.6575 22.9683 61.6752C22.9681 61.6757 22.9679 61.6761 22.9677 61.6766C22.9618 61.6894 22.9564 61.7013 22.951 61.7126C21.014 65.6182 18.7147 68.9047 16.5083 71.1715C16.4312 71.2508 16.4153 71.3713 16.4693 71.4678C16.5232 71.5644 16.6342 71.614 16.7421 71.5898C18.461 71.2048 24.0077 70.4497 33.3912 73.6358L33.3919 73.636C33.9353 73.8189 34.489 74.0155 35.0585 74.2321L35.0604 74.2328C36.1213 74.6267 37.2251 75.0683 38.3711 75.5645C38.4628 75.6042 38.5693 75.5855 38.642 75.517C40.1685 74.0771 41.3149 71.863 42.0766 70.0324C42.459 69.1132 42.7476 68.2827 42.9407 67.6818C43.0372 67.3813 43.11 67.1379 43.1587 66.9693C43.1831 66.885 43.2014 66.8193 43.2137 66.7745L43.2277 66.7233L43.2313 66.7099L43.2322 66.7063L43.2325 66.7053L43.2326 66.7051C43.2326 66.705 43.2326 66.7049 42.9908 66.6414L43.2326 66.7049ZM23.0555 61.5626L23.055 61.5629L23.0555 61.5626Z" fill="url(#paint7_linear_3430_36568)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><mask id="mask0_3430_36568" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="42" y="69" width="11" height="11"><path style="mix-blend-mode:multiply" d="M51.3557 76.876C49.8792 79.5588 46.239 79.2018 46.239 79.2018C45.0429 78.5325 43.8835 77.9174 42.7539 77.3482C45.7269 74.5433 47.0427 69.019 47.0427 69.019C48.15 69.6222 49.3106 70.2317 50.5377 70.8367C51.4203 71.4978 52.8385 74.1945 51.3557 76.876Z" fill="url(#paint8_linear_3430_36568)"/></mask><g mask="url(#mask0_3430_36568)"><path d="M51.3557 76.8761C49.8792 79.5588 46.239 79.2018 46.239 79.2018C45.0429 78.5326 43.8835 77.9174 42.7539 77.3483C45.7269 74.5433 47.0427 69.0191 47.0427 69.0191C48.15 69.6223 49.3106 70.2317 50.5377 70.8368C51.4203 71.4979 52.8385 74.1945 51.3557 76.8761Z" fill="url(#paint9_linear_3430_36568)"/></g><path d="M23.2138 61.7242L23.1797 61.7767L23.1911 61.7844L23.2252 61.7318L23.2138 61.7242Z" fill="url(#paint10_linear_3430_36568)"/><path d="M19.2614 54.6248L19.2188 54.6705L19.4681 54.9098L19.5108 54.864L19.2614 54.6248Z" fill="url(#paint11_linear_3430_36568)"/><path d="M39.8206 75.9372L32.3776 86.3617C32.0002 86.8856 31.2926 87.038 30.7396 86.7055L26.6178 84.2645C25.9009 83.8389 25.7861 82.8309 26.3993 82.256L31.6576 77.2915L35.1489 73.9987C36.2139 74.3941 37.3224 74.8373 38.4719 75.3353C38.9151 75.5248 39.3626 75.7276 39.8206 75.9372Z" fill="#792AFF"/><path d="M35.1465 73.9984L31.6553 77.2913L30.9757 76.7536C30.6771 76.5207 30.6579 76.0762 30.931 75.8155L31.2183 75.5385L32.9587 73.8883L33.4707 73.399C34.0169 73.5829 34.5743 73.7806 35.1465 73.9984Z" fill="url(#paint12_linear_3430_36568)"/><path d="M32.9599 73.8885L31.2195 75.5394L30.9297 75.3379L32.675 73.694L32.9599 73.8885Z" fill="#792AFF"/><path d="M25.2603 43.4349C22.1551 41.8432 16.1793 47.4984 11.9221 56.0649C7.65937 64.6308 6.72712 72.8586 9.83791 74.4515C11.5187 75.3153 14.0467 74.0504 16.6876 71.3463C18.9158 69.057 21.2297 65.7471 23.1761 61.8221C23.1841 61.8051 23.1922 61.7875 23.2003 61.7698C23.2009 61.7635 23.2071 61.7648 23.2077 61.7585C25.2584 57.6188 26.5314 53.5616 26.9466 50.2995L26.9547 50.2334C27.3848 46.7957 26.8504 44.2445 25.2603 43.4349ZM21.4897 60.957C21.0354 61.8706 20.5612 62.7501 20.0796 63.5863L15.5096 63.1147C16.3698 62.951 17.6329 61.5482 18.6042 59.6008C19.6153 57.5722 19.9579 55.6267 19.4899 54.8882L22.8186 58.0476C22.4201 58.9983 21.9769 59.9742 21.4897 60.957Z" fill="url(#paint13_linear_3430_36568)"/><path d="M22.8187 58.0485L19.4901 54.8891C19.9581 55.6277 19.6155 57.5732 18.6044 59.6018C17.6331 61.5492 16.37 62.952 15.5098 63.1157L20.0797 63.5872C16.5444 69.7153 12.4325 73.5943 10.347 72.5271C7.97671 71.3145 9.11874 64.1671 12.8967 56.5664C16.6816 48.9601 21.6693 43.7777 24.039 44.9966C26.1015 46.055 25.5019 51.6126 22.8187 58.0485Z" fill="#1A1818" fill-opacity="0.35"/><path d="M18.6033 59.6011C17.632 61.5485 16.3689 62.9513 15.5087 63.115C15.3355 63.149 15.1865 63.1307 15.055 63.0652C14.2456 62.6522 14.5299 60.436 15.6849 58.1121C16.8406 55.7888 18.4295 54.2362 19.2389 54.6492C19.3419 54.6989 19.4282 54.7827 19.4884 54.8884C19.9564 55.627 19.6138 57.5725 18.6027 59.6011H18.6033Z" fill="url(#paint14_linear_3430_36568)"/><path d="M17.9864 59.2868C18.8726 57.5048 19.1737 55.8466 18.6589 55.5832C18.1442 55.3197 17.0084 56.5508 16.1222 58.3328C15.236 60.1148 14.9349 61.773 15.4497 62.0364C15.9644 62.2999 17.1001 61.0688 17.9864 59.2868Z" fill="white"/><path d="M77.7408 52.5636L77.7407 52.5636C77.6191 52.5482 77.4841 52.5016 77.3273 52.4476C76.8659 52.2883 76.2167 52.0643 75.1708 52.3835C74.5758 51.9993 74.0093 51.8549 73.4609 51.8547C72.8889 51.8546 72.3505 52.0118 71.8442 52.1872C71.7179 52.2309 71.594 52.2755 71.4718 52.3193C70.586 52.6375 69.7931 52.9223 68.9007 52.6343C68.8059 52.6037 68.702 52.6323 68.6362 52.7071C68.5704 52.782 68.5553 52.8887 68.5978 52.9788C69.0647 53.9686 69.7128 54.6663 70.4066 55.2605C70.7415 55.5473 71.0893 55.8119 71.4305 56.0714L71.4625 56.0958C71.8159 56.3646 72.1619 56.6289 72.492 56.9156C72.5134 56.9342 72.5378 56.9489 72.5641 56.9594C74.2088 57.6092 75.7996 57.5518 77.3387 56.9602C77.3528 56.9548 77.3664 56.9481 77.3793 56.9402C78.0311 56.5418 78.6376 55.9746 79.2097 55.4397C79.3481 55.3103 79.4844 55.1828 79.6188 55.0601C80.3301 54.4108 80.9843 53.8977 81.6685 53.7647C81.7606 53.7468 81.8349 53.6789 81.861 53.5887C81.887 53.4985 81.8604 53.4014 81.792 53.3371C81.3802 52.9503 80.9994 52.7118 80.6311 52.5762C80.2615 52.44 79.9186 52.4126 79.5917 52.4287C79.3632 52.44 79.1313 52.474 78.91 52.5065C78.8253 52.5189 78.7422 52.5311 78.6614 52.5417C78.3597 52.5813 78.0619 52.6041 77.7408 52.5636ZM77.7144 57.4021C77.65 57.3619 77.5709 57.3531 77.4992 57.3782C75.8368 57.9608 74.1532 58.0495 72.4138 57.3808C72.3405 57.3526 72.2582 57.3605 72.1916 57.4021C68.408 59.7662 66.0514 62.6188 65.0093 65.547C63.9881 68.4164 64.2359 71.3417 65.6186 73.9123L63.7372 75.7952C63.6658 75.8667 63.6444 75.9742 63.6831 76.0676C63.7218 76.161 63.813 76.2219 63.9141 76.2219C65.9043 76.2219 67.6344 76.5454 69.3876 76.8739L69.3955 76.8753C71.1427 77.2026 72.9134 77.5343 74.9532 77.5343C76.993 77.5343 78.7635 77.2026 80.5104 76.8753L80.5184 76.8739C82.2715 76.5454 84.0013 76.2219 85.9916 76.2219C86.0927 76.2219 86.1838 76.161 86.2225 76.0676C86.2612 75.9742 86.2399 75.8667 86.1684 75.7952L84.2871 73.9123C85.6697 71.3419 85.9176 68.4165 84.8964 65.5471C83.8544 62.6189 81.4978 59.7662 77.7144 57.4021Z" fill="url(#paint15_linear_3430_36568)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M68.9531 52.9713C69.5301 53.7221 71.8141 53.7068 72.5515 53.6986C72.9252 53.6947 73.3293 53.7274 73.6733 53.8832C73.8916 53.9826 74.0316 54.1109 74.2228 54.2409C74.6711 54.5453 75.2349 54.4123 75.7236 54.2841C76.4085 54.1045 77.0716 53.9434 77.7711 53.8233C78.988 53.6138 80.2234 53.5165 81.4549 53.4969C81.4692 53.493 81.4838 53.4887 81.4974 53.4855C80.899 52.9449 80.3206 52.6755 79.4874 52.7439C78.8661 52.7956 78.3413 52.9502 77.7011 52.8693C77.3656 52.8269 77.0344 52.6591 76.6986 52.5839C76.1667 52.4628 75.6629 52.5422 75.1506 52.7136L75.1231 52.7229L75.0988 52.7061C74.4396 52.2521 73.769 52.0797 72.9759 52.2023C71.588 52.4171 70.4104 53.3568 68.9535 52.9706L68.9531 52.9713Z" fill="url(#paint16_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M72.6566 56.7265C72.4444 57.0811 72.3279 57.3797 72.3086 57.6231C72.314 57.6206 72.3186 57.617 72.3243 57.6138C73.036 57.8878 73.7387 58.0382 74.4336 58.0838C75.5 58.1537 76.5479 57.9755 77.5822 57.6138C77.5404 57.3319 77.43 57.0358 77.2496 56.7269C76.3278 57.0807 75.3904 57.2364 74.4336 57.1538C73.8491 57.1035 73.2575 56.9638 72.6566 56.7269V56.7265Z" fill="#F9F9F9"/><path d="M77.7785 68.2428C78.0682 68.5777 78.2129 68.9922 78.2129 69.4864C78.2129 70.2904 77.9589 70.9182 77.4509 71.3705C76.9428 71.8227 76.244 72.0486 75.3541 72.0486H75.0393V73.2794H74.4095V72.0486H72.0419V64.5374C72.0419 64.3785 72.0208 64.2613 71.979 64.1857C71.9368 64.1105 71.8783 64.035 71.8025 63.9598C71.7521 63.9181 71.7164 63.8761 71.6953 63.8344C71.6743 63.7923 71.6721 63.7424 71.6889 63.6836C71.7225 63.5247 71.8043 63.3761 71.9347 63.2378C72.0647 63.0996 72.2055 63.0304 72.3566 63.0304C72.516 63.0304 72.6589 63.106 72.7846 63.2564H74.4091V62.0127H75.039V63.2564H75.3158C76.1301 63.2564 76.7746 63.4698 77.249 63.8971C77.7231 64.324 77.9603 64.8852 77.9603 65.5801C77.9603 66.0908 77.8428 66.4906 77.6077 66.7796C77.3726 67.0686 77.0368 67.2885 76.6002 67.4388V67.4766C77.0954 67.6527 77.488 67.9082 77.7778 68.2428H77.7785ZM73.302 66.9617H74.4102V64.2737H73.302V66.9617ZM74.4098 71.0312V67.9915H73.3016V71.0312H74.4098ZM75.0397 64.2737V66.9617H75.0522C75.5474 66.9617 75.9378 66.8466 76.2233 66.6164C76.5084 66.3862 76.6513 66.0409 76.6513 65.5801C76.6513 65.1867 76.5295 64.8703 76.2862 64.6319C76.0425 64.3931 75.6899 64.2737 75.2283 64.2737H75.0393H75.0397ZM76.4941 70.6421C76.7667 70.3827 76.9032 69.9974 76.9032 69.4864C76.9032 69.0093 76.7499 68.6408 76.4434 68.381C76.1368 68.1216 75.7067 67.9915 75.1526 67.9915H75.0393V71.0312H75.2912C75.8203 71.0312 76.2208 70.9015 76.4938 70.6417L76.4941 70.6421Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.2092 32.2286C34.2813 33.0819 25.4338 31.4377 17.233 26.694C17.8098 23.6653 18.6573 20.7736 19.7482 18.0575C26.5428 22.6313 33.9947 24.3726 40.5818 23.9169C40.819 26.6621 41.3547 29.4486 42.2092 32.2286Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M41.5594 31.7649C34.0075 32.4513 25.6495 30.8513 17.8524 26.4325C18.362 23.7907 19.0781 21.2511 19.9847 18.8457C26.5982 23.13 33.7854 24.8125 40.2065 24.4486C40.423 26.8676 40.8692 29.3185 41.5594 31.7649Z" fill="url(#paint17_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M37.0353 26.274C36.3165 26.2445 35.6462 25.9009 35.1985 25.3942C31.6641 25.0429 28.0119 24.1013 24.4248 22.4981C23.8511 22.8031 23.0723 22.8111 22.2948 22.4131C21.5143 22.0152 20.7394 21.5858 19.9708 21.1243C19.5918 22.3412 19.2606 23.588 18.9791 24.8593C19.8112 25.3268 20.6495 25.7621 21.4955 26.1636C22.3112 26.5536 22.9981 27.1917 23.4529 27.9078C27.4969 29.5416 31.6056 30.459 35.589 30.7347C36.0309 30.3224 36.6848 30.0831 37.4406 30.0994C38.4432 30.1221 39.4351 30.1018 40.4165 30.0414C40.1412 28.7808 39.9269 27.5226 39.7761 26.2704C38.8736 26.31 37.9585 26.3103 37.0345 26.2733L37.0353 26.274Z" fill="#F9F9F9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M32.8024 27.7464C32.8437 29.4835 31.5674 30.7516 29.7811 30.3644C27.998 29.9801 26.6056 28.0506 26.8218 26.2875C27.0351 24.5245 28.5627 23.6056 30.0893 24.007C31.6193 24.4157 32.7574 25.9976 32.8024 27.7464Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M24.7678 25.8019C24.6715 26.4861 23.9665 26.8047 23.1748 26.4682C22.3847 26.1331 21.8453 25.2664 21.9885 24.5832C22.1316 23.9 22.8649 23.6597 23.6082 23.9939C24.3524 24.3301 24.8634 25.1185 24.7678 25.8019Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M37.3167 28.4066C37.3937 29.0712 36.8527 29.6026 36.0743 29.5565C35.2969 29.5125 34.6127 28.8964 34.5821 28.2255C34.5469 27.5504 35.1375 27.0813 35.8688 27.1346C36.6008 27.1886 37.2346 27.7417 37.3152 28.4067L37.3167 28.4066Z" fill="#792AFF" fill-opacity="0.5"/><path d="M31.0493 27.4666C31.1433 27.6213 31.173 27.7979 31.1387 27.9964C31.0828 28.3193 30.9363 28.5538 30.6993 28.7C30.4623 28.8462 30.1638 28.8881 29.8037 28.826L29.6763 28.8041L29.5908 29.2985L29.3359 29.2545L29.4214 28.7601L28.4634 28.5949L28.9855 25.578C28.9966 25.5141 28.9962 25.4656 28.9845 25.4323C28.9727 25.3992 28.9542 25.3647 28.9288 25.3292C28.9113 25.309 28.8998 25.2896 28.8942 25.2714C28.8886 25.253 28.8912 25.2328 28.902 25.2104C28.9267 25.1489 28.9701 25.0949 29.0325 25.0485C29.0947 25.002 29.1565 24.9841 29.2176 24.9946C29.2821 25.0057 29.3347 25.0461 29.3751 25.1152L30.0325 25.2286L30.119 24.7291L30.3738 24.773L30.2874 25.2726L30.3994 25.2919C30.7289 25.3487 30.9749 25.4794 31.1372 25.6841C31.2993 25.8887 31.3563 26.1307 31.308 26.4098C31.2725 26.6149 31.1972 26.7673 31.0819 26.867C30.9667 26.9666 30.8155 27.0315 30.6284 27.0614L30.6258 27.0766C30.8139 27.1819 30.955 27.3119 31.049 27.4665L31.0493 27.4666ZM29.3269 26.6396L29.7754 26.717L29.9622 25.6373L29.5138 25.56L29.3269 26.6396ZM29.4923 28.3515L29.7036 27.1306L29.2552 27.0532L29.0439 28.2742L29.4923 28.3515ZM30.2169 25.6812L30.0301 26.7609L30.0352 26.7618C30.2355 26.7963 30.4015 26.7773 30.5331 26.7048C30.6644 26.6322 30.7463 26.5035 30.7783 26.3184C30.8056 26.1604 30.7783 26.0248 30.6965 25.9121C30.6145 25.7992 30.4801 25.7266 30.2933 25.6944L30.2168 25.6812L30.2169 25.6812ZM30.3628 28.3407C30.4911 28.2555 30.5731 28.1103 30.6087 27.905C30.6418 27.7134 30.6054 27.5547 30.4994 27.4289C30.3934 27.3034 30.2284 27.2211 30.0042 27.1824L29.9584 27.1745L29.747 28.3954L29.849 28.413C30.0631 28.4499 30.2342 28.4258 30.3627 28.3405L30.3628 28.3407Z" fill="white"/></g><defs><linearGradient id="paint0_linear_3430_36568" x1="60.4633" y1="14.2701" x2="60.4633" y2="65.7295" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint1_linear_3430_36568" x1="31.4328" y1="6.23657" x2="31.4328" y2="15" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint2_linear_3430_36568" x1="68.604" y1="69.324" x2="68.604" y2="76.6372" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint3_linear_3430_36568" x1="62.2065" y1="15.1399" x2="62.2065" y2="24.8143" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9300" stop-opacity="0.15"/><stop offset="1" stop-color="#FF9300" stop-opacity="0.4"/></linearGradient><linearGradient id="paint4_linear_3430_36568" x1="61.5916" y1="28.4065" x2="61.5916" y2="57.6022" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><linearGradient id="paint5_linear_3430_36568" x1="94.1577" y1="52.9524" x2="95.3247" y2="75.7232" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint6_linear_3430_36568" x1="19.1643" y1="54.8883" x2="19.1643" y2="63.5864" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint7_linear_3430_36568" x1="17.9321" y1="60.9593" x2="43.3052" y2="64.0039" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.37" stop-color="#F2F2FF"/><stop offset="1" stop-color="#D2D2FF"/></linearGradient><linearGradient id="paint8_linear_3430_36568" x1="40.5734" y1="71.1969" x2="49.269" y2="75.2563" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.17" stop-color="#C5C5C5"/><stop offset="0.4" stop-color="#808080"/><stop offset="0.6" stop-color="#494949"/><stop offset="0.78" stop-color="#212121"/><stop offset="0.92" stop-color="#080808"/><stop offset="1"/></linearGradient><linearGradient id="paint9_linear_3430_36568" x1="40.5734" y1="71.1969" x2="49.269" y2="75.2564" gradientUnits="userSpaceOnUse"><stop stop-color="#FFD571"/><stop offset="0.28" stop-color="#FFAC54"/><stop offset="0.59" stop-color="#FF8538"/><stop offset="0.85" stop-color="#FF6C27"/><stop offset="1" stop-color="#FF6421"/></linearGradient><linearGradient id="paint10_linear_3430_36568" x1="23.1821" y1="61.7513" x2="23.2216" y2="61.756" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint11_linear_3430_36568" x1="19.2101" y1="54.7472" x2="19.5208" y2="54.7844" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint12_linear_3430_36568" x1="32.9424" y1="73.399" x2="32.9424" y2="77.2913" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint13_linear_3430_36568" x1="17.5488" y1="43.1666" x2="17.5488" y2="74.7205" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint14_linear_3430_36568" x1="17.1442" y1="54.5826" x2="17.1442" y2="63.1317" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint15_linear_3430_36568" x1="74.9528" y1="52.1047" x2="74.9528" y2="77.2843" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint16_linear_3430_36568" x1="75.2253" y1="52.1639" x2="75.2253" y2="54.4264" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint17_linear_3430_36568" x1="35.3671" y1="20.1503" x2="24.524" y2="32.3582" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><clipPath id="clip0_3430_36568"><rect width="120" height="78" fill="white" transform="translate(0 0.5)"/></clipPath></defs></svg>'),
                          Container(
                              margin: EdgeInsets.only(
                                  top: 16.h, bottom: 16.h),
                              height: 267.h,
                              // width: 156.w,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                    color: appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? Color(0xFF792AFF)
                                        .withOpacity(0.08)
                                        : Color(0xFFEA1B23)
                                        .withOpacity(0.05),
                                  ),
                                  borderRadius:
                                  BorderRadius.circular(16)),
                              child: Padding(
                                padding: EdgeInsets.only(top: 16.h),
                                child: Column(
                                  crossAxisAlignment:
                                  CrossAxisAlignment.center,
                                  // mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    appConfigService
                                        .countryConfigCollection ==
                                        "aam"
                                        ? Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"><path opacity="0.15" d="M38.6435 66.8377C48.9297 66.8377 57.2684 58.4794 57.2684 48.1688C57.2684 37.8583 48.9297 29.5 38.6435 29.5C28.3572 29.5 20.0186 37.8583 20.0186 48.1688C20.0186 58.4794 28.3572 66.8377 38.6435 66.8377Z" fill="#792AFF" fill-opacity="0.75"/><path opacity="0.5" d="M38.6428 60.3294C45.3431 60.3294 50.7748 54.8849 50.7748 48.1687C50.7748 41.4526 45.3431 36.0081 38.6428 36.0081C31.9424 36.0081 26.5107 41.4526 26.5107 48.1687C26.5107 54.8849 31.9424 60.3294 38.6428 60.3294Z" fill="#792AFF" fill-opacity="0.25"/><path d="M108.011 66.9253C118.343 66.9253 126.719 58.5299 126.719 48.1737C126.719 37.8175 118.343 29.4221 108.011 29.4221C97.6794 29.4221 89.3037 37.8175 89.3037 48.1737C89.3037 58.5299 97.6794 66.9253 108.011 66.9253Z" fill="#FF9300" fill-opacity="0.15"/><path d="M108.01 60.3881C114.74 60.3881 120.196 54.9195 120.196 48.1736C120.196 41.4277 114.74 35.959 108.01 35.959C101.28 35.959 95.8242 41.4277 95.8242 48.1736C95.8242 54.9195 101.28 60.3881 108.01 60.3881Z" fill="#FF9300" fill-opacity="0.2"/><path d="M38.5814 21.0918C41.1334 18.5322 41.1334 14.3822 38.5814 11.8226C36.0293 9.26299 31.8917 9.26299 29.3396 11.8226C26.7876 14.3822 26.7876 18.5322 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3287_12501)"/><path d="M65.4118 33.0401C71.0548 33.0401 75.6293 28.452 75.6293 22.7923C75.6293 17.1326 71.0548 12.5444 65.4118 12.5444C59.7689 12.5444 55.1943 17.1326 55.1943 22.7923C55.1943 28.452 59.7689 33.0401 65.4118 33.0401Z" fill="url(#paint1_linear_3287_12501)"/><path d="M37.0957 18.144C37.0955 18.144 37.0954 18.144 37.0953 18.1438C37.0162 18.0111 35.5472 17.5388 35.1383 17.2602C35.1299 17.2546 35.1292 17.2425 35.1369 17.2359C35.7667 16.6989 36.1853 15.7971 36.1853 15.0008C36.1853 13.7699 35.1869 12.769 33.9597 12.769C32.7325 12.769 31.7345 13.7704 31.7345 15.0008C31.7345 15.7977 32.1537 16.6992 32.7832 17.2362C32.7907 17.2426 32.79 17.2544 32.782 17.2602C32.3729 17.5389 30.9019 18.0115 30.8239 18.144C30.4503 18.7792 30.9864 19.4892 31.7234 19.4892H36.6417C37.1544 19.4892 37.5277 18.9984 37.3053 18.5365C37.2412 18.4033 37.1715 18.2726 37.096 18.1442C37.096 18.1441 37.0958 18.144 37.0957 18.144Z" fill="white"/><path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3287_12501)"/><path d="M94.1137 9.20107C94.0478 9.08959 92.8136 8.69289 92.4704 8.4587C92.4634 8.45382 92.4627 8.444 92.4692 8.43848C92.9981 7.98748 93.3497 7.2304 93.3497 6.56135C93.3497 5.52767 92.5115 4.68695 91.4808 4.68695C90.4502 4.68695 89.612 5.52767 89.612 6.56135C89.612 7.2304 89.9635 7.98748 90.4925 8.43848C90.4989 8.444 90.4983 8.45382 90.4913 8.4587C90.1476 8.69289 88.9125 9.08959 88.8471 9.20107C88.5333 9.73445 88.9836 10.3308 89.6024 10.3308H93.3586C93.9775 10.3308 94.4271 9.73471 94.1137 9.20107Z" fill="white"/><path d="M70.2724 25.3874C70.1515 25.1818 67.8722 24.4493 67.238 24.017C67.2251 24.0084 67.2241 23.9898 67.2359 23.9797C68.2123 23.1468 68.8617 21.7491 68.8617 20.5136C68.8617 18.6055 67.3137 17.0529 65.4107 17.0529C63.5078 17.0529 61.9602 18.605 61.9602 20.5136C61.9602 21.7491 62.6096 23.1468 63.586 23.9797C63.5978 23.9898 63.5967 24.0082 63.5839 24.017C62.9493 24.4493 60.669 25.1818 60.5481 25.3874C59.9691 26.3724 60.8001 27.4731 61.9426 27.4731H68.8784C70.0209 27.4731 70.8519 26.372 70.2724 25.3874Z" fill="white"/><path d="M41.042 18.188L55.0281 22.5872" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/><path d="M74.7189 18.4761L87.7192 11.188" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/><path d="M20.3286 14.693C20.5369 14.9898 20.6918 15.3506 20.8035 15.7532C21.9194 19.7664 16.9092 23.2821 13.5144 20.8708C13.1728 20.6282 12.8854 20.3592 12.6765 20.0614C11.4751 18.3489 11.4246 16.1946 12.385 14.6048C12.6947 14.0917 13.1096 13.6376 13.6253 13.2758C15.7384 11.7933 18.7395 12.4279 20.3286 14.693Z" fill="#FF9300" fill-opacity="0.5"/><path d="M21.2509 22.7215C20.7846 22.6905 20.5819 23.6771 21.0055 23.4788C21.2676 23.3562 21.4619 22.8834 21.3432 22.7531C21.3258 22.7341 21.2935 22.7243 21.2509 22.7215Z" fill="#FF9300" fill-opacity="0.5"/><path d="M20.4968 22.9101L20.3717 22.9978C20.3449 23.0166 20.3076 23.0101 20.2888 22.9834C20.27 22.9566 20.2766 22.9193 20.3034 22.9005L20.4285 22.8127C20.4553 22.7939 20.4926 22.8004 20.5114 22.8272C20.5301 22.8539 20.5235 22.8913 20.4968 22.9101Z" fill="#FF9300" fill-opacity="0.5"/><path d="M25.955 28.7423C25.9221 28.8472 25.7719 28.861 25.6851 28.7938C25.6344 28.7546 25.5662 28.6649 25.5898 28.6052C25.8065 28.0577 26.4894 26.2007 26.1524 25.3343C25.9378 24.7825 24.4818 25.2637 23.312 25.6503C22.2802 25.9914 21.4619 26.4206 21.172 26.0656C20.9029 25.7361 21.3225 24.9288 21.6065 24.2812C21.7738 23.8999 21.9633 23.4676 21.883 23.3821C21.7853 23.278 21.4566 23.4615 21.1927 23.6091C20.8624 23.7936 20.5772 23.9529 20.4382 23.8163C20.207 23.5891 20.3435 23.0585 20.3707 22.9625C20.373 22.9545 20.3813 22.9503 20.3893 22.9527C20.3973 22.9551 20.4026 22.9636 20.4013 22.9719C20.3866 23.0641 20.3211 23.5603 20.5086 23.7446C20.594 23.8286 20.8989 23.6583 21.1438 23.5214C21.4838 23.3314 21.889 23.0812 22.0404 23.2426C22.1601 23.3701 21.9433 23.763 21.6985 24.3214C21.4393 24.9126 21.1167 25.6483 21.3411 25.9372C21.5011 26.1431 22.4058 25.8441 23.2807 25.5549C24.6161 25.1136 26.1147 24.56 26.3639 25.2005C26.5371 25.6458 26.4822 26.3962 26.2883 27.4468C26.1995 27.927 26.0505 28.438 25.955 28.7423Z" fill="#FF9300" fill-opacity="0.5"/><path d="M20.3284 14.6936C20.7731 15.3275 20.8737 16.1743 20.59 16.896C20.0851 18.181 18.6847 19.1051 16.4007 19.2781C10.3634 19.7352 12.383 14.6086 12.3842 14.6057C12.3842 14.6056 12.3841 14.6056 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2762C15.7377 11.7936 18.7391 12.4282 20.3284 14.6936Z" fill="#FF9300" fill-opacity="0.2"/><path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/><path d="M88.5735 38.1365C88.6486 38.8472 89.2536 39.5756 90.2316 40.8388C90.9251 41.7344 91.5834 42.0222 91.7717 42.5911C91.882 42.9927 91.2088 43.5772 90.9064 44.1631C90.6209 44.7153 91.3916 45.2729 91.5656 45.3895C91.5865 45.4035 91.6131 45.4029 91.6342 45.3892C91.6742 45.3633 91.6744 45.3035 91.6361 45.2752C91.5048 45.1781 91.2452 44.9631 91.0471 44.6538C90.7259 44.1528 91.8798 43.1803 91.9 42.7294C91.9006 42.7162 91.9006 42.7036 91.9006 42.6909C91.9002 42.1824 91.3956 41.8503 91.0381 41.4881C90.1963 40.635 88.87 39.1461 88.7547 38.0567C88.6614 37.1765 91.1163 36.9311 92.935 36.6438C94.5392 36.39 95.9203 36.4 96.0619 35.7298C96.1933 35.1077 95.0826 34.3224 94.2616 33.6563C93.7785 33.264 93.2303 32.8193 93.2784 32.6511C93.3366 32.4455 93.8989 32.4645 94.3505 32.4799C94.9158 32.4989 95.4041 32.5149 95.4948 32.2384C95.6137 31.8774 95.3037 31.4232 95.1157 31.192C95.0846 31.1538 95.1104 31.0792 95.1171 31.0304C95.1232 30.9814 95.1164 30.9068 95.1574 30.8792C96.4367 30.0173 102.717 25.6139 103.217 21.9348C103.638 18.836 102.228 15.9386 99.8629 14.4833C99.1172 14.0247 98.2766 13.7091 97.3686 13.5762C97.3475 13.5726 97.3258 13.5696 97.3041 13.5665C93.4815 13.0432 89.9314 15.9415 89.3743 20.0399C89.3013 20.5755 89.3433 21.1592 89.4695 21.7689C89.4707 21.7725 89.4711 21.7761 89.4719 21.7798C90.24 25.452 94.1004 30.0668 94.8161 30.8986C94.8165 30.8989 94.816 30.8993 94.8155 30.8992C94.8154 30.8992 94.8154 30.8992 94.8154 30.8991C94.7672 30.8931 94.7221 30.9268 94.7156 30.9754C94.7111 31.0097 94.6928 31.0645 94.6586 31.0701C94.2733 31.1327 93.6094 31.2655 93.5611 31.4454C93.4927 31.6996 94.074 32.1942 94.5062 32.1777C94.8619 32.164 94.92 31.3939 94.929 31.1235C94.9296 31.1076 94.9437 31.0958 94.9595 31.0979C94.9667 31.0989 94.9731 31.1025 94.9774 31.1085C95.0832 31.256 95.4697 31.8338 95.3521 32.1914C95.2963 32.3615 94.7749 32.3438 94.3558 32.3296C93.7737 32.3106 93.0644 32.2554 92.9737 32.5734C92.9022 32.8252 93.4594 33.1983 94.1672 33.773C94.9166 34.3807 96.052 35.1209 95.9126 35.6499C95.7601 36.2281 94.2719 36.2806 92.9114 36.4954C90.835 36.8239 88.4656 37.1144 88.5735 38.1365Z" fill="#792AFF" fill-opacity="0.25"/><path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#792AFF" fill-opacity="0.25"/><path d="M91.8283 17.5348C92.0697 17.7855 93.2592 17.5764 93.8571 17.0026C94.4551 16.4288 94.4226 15.6906 94.2156 15.4109C93.8193 14.8753 92.9492 15.3435 92.3513 15.9173C91.7533 16.491 91.587 17.284 91.8283 17.5348Z" fill="white"/><g clip-path="url(#clip0_3287_12501)"><path d="M119.681 56.8803C118.333 54.7508 116.217 54.1749 116.217 54.1749L111.147 52.2543H106.307L101.237 54.1749C101.237 54.1749 99.1211 54.7508 97.7735 56.8803H119.681Z" fill="#F9CDB1"/><path d="M102.115 66.9627H94.2816C94.2816 65.5966 94.3681 63.6465 94.3681 63.6465C94.8295 55.4606 100.665 54.1749 100.665 54.1749C105.217 53.0498 102.737 60.242 102.737 60.242C102.855 61.975 102.603 64.3081 102.113 66.96L102.115 66.9627Z" fill="url(#paint3_linear_3287_12501)"/><path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/><path d="M105.406 50.4757V47.3979H112.052V48.9221C110.156 50.5346 107.873 50.7382 105.406 50.4757Z" fill="#EAB894"/><path d="M98.3296 66.9627C98.2012 63.3947 98.2405 62.4144 98.272 61.1956C98.3401 58.4848 98.909 56.1919 100.658 54.1776C100.66 54.1776 100.666 54.1776 100.666 54.1776L105.403 52.5999C105.403 52.5999 104.407 54.3811 108.761 54.3811C108.761 54.3811 111.63 54.266 112.049 52.5999L116.86 54.1776C116.86 54.1776 120.973 55.2115 121.679 59.6339L119.261 64.1769L119.348 66.9654H98.3323L98.3296 66.9627Z" fill="url(#paint4_linear_3287_12501)"/><path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/><path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/><path d="M116.065 31.8619C117.827 32.7297 117.887 34.3342 117.803 36.1611C117.74 37.5781 117.31 38.7835 116.815 40.1576C116.252 41.7183 115.721 42.9478 115.221 43.8461C115.221 43.8461 114.054 45.6997 112.552 46.9694C112.987 45.2068 113.11 43.8916 113.506 42.5362C113.983 40.9103 113.845 38.7352 113.137 37.0665C113.137 37.0665 112.736 35.9548 111.089 35.6387L116.068 31.8592" fill="#1A1818"/><path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.2219C115.325 43.2219 114.18 45.1398 112.806 44.0764L113.17 42.188Z" fill="#F9CDB1"/><path d="M113.312 37.5379C113.312 37.5379 113.307 41.1085 112.664 43.0023L113.346 43.1416C113.346 43.1416 113.705 40.6933 114.938 40.6639L113.312 37.5406V37.5379Z" fill="#1A1818"/><path d="M99.9442 29.8931C94.2812 35.786 111.419 35.7271 111.419 35.7271C111.419 35.7271 113.218 36.5762 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0135C108.727 27.0135 103.371 26.3412 99.9442 29.8931Z" fill="#1A1818"/><path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3287_12501)"/></g><g clip-path="url(#clip1_3287_12501)"><path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1606 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2665 34.7656 34.7422Z" fill="#1A1818"/><path d="M37.0334 32.188C37.0334 32.188 36.2653 33.1206 37.3716 34.1837L36.6245 34.9215C36.6245 34.9215 34.9125 32.8721 37.0334 32.1906V32.188Z" fill="#1A1818"/><path d="M33.8818 57.3224C35.2294 55.2857 37.3451 54.7349 37.3451 54.7349L42.4155 52.8981H47.2552L52.3257 54.7349C52.3257 54.7349 54.4414 55.2857 55.789 57.3224H33.8818Z" fill="#F9CDB1"/><path d="M51.4472 66.9626H59.2809C59.2809 65.6561 59.1944 63.7911 59.1944 63.7911C58.733 55.962 52.897 54.7323 52.897 54.7323C48.3457 53.6564 50.8259 60.535 50.8259 60.535C50.7079 62.1925 50.9596 64.4239 51.4498 66.9601L51.4472 66.9626Z" fill="url(#paint6_linear_3287_12501)"/><path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/><path d="M48.1568 51.1944V48.2509H41.5107V49.7086C43.4062 51.2508 45.6898 51.4455 48.1568 51.1944Z" fill="#EAB894"/><path d="M55.2329 66.9626C55.3613 63.5502 55.322 62.6126 55.2905 61.4469C55.2224 58.8543 54.6535 56.6614 52.9048 54.7349C52.9021 54.7349 52.8969 54.7349 52.8969 54.7349L48.1594 53.226C48.1594 53.226 49.1557 54.9296 44.801 54.9296C44.801 54.9296 41.9329 54.8194 41.5134 53.226L36.7025 54.7349C36.7025 54.7349 32.589 55.7238 31.8838 59.9534L34.301 64.2983L34.2145 66.9652H55.2302L55.2329 66.9626Z" fill="url(#paint7_linear_3287_12501)"/><path d="M53.3222 40.3373C53.5109 38.8821 53.1937 37.4859 53.2094 36.0513C53.2199 35.088 53.0285 33.6355 51.8016 33.5125C50.1027 33.3434 49.5783 36.2076 49.4787 37.4501C49.3398 39.1614 49.775 41.011 50.1132 42.6865C50.2338 43.2911 50.2233 44.5003 50.918 44.7923C51.5132 45.0408 52.124 44.4542 52.4281 44.0161C53.1622 42.9606 53.1648 41.5567 53.3195 40.3373H53.3222Z" fill="#1A1818"/><path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/><path d="M53.7288 30.2411C54.0146 31.7756 53.7472 33.902 52.5307 34.8832C51.2093 35.9463 50.0741 35.6568 48.4827 36.0027C46.8284 36.3613 45.1348 36.5048 43.4726 36.8174C42.3086 37.0377 40.9636 37.1632 40.4288 38.3698C39.7209 39.9659 39.582 42.0461 40.0591 43.6012C40.455 44.8949 40.5782 46.1553 41.0135 47.841C39.5112 46.6267 38.3445 44.8539 38.3445 44.8539C37.8447 43.9948 37.3133 42.8189 36.7505 41.3262C36.255 40.012 35.8277 38.8592 35.7621 37.5039C35.6809 35.7568 36.546 34.9959 37.5003 33.3922C38.397 31.8858 39.6554 30.8405 41.2966 30.364C41.2966 30.364 49.6127 30.8201 49.458 28.9653C49.458 28.9653 51.4558 29.4648 51.0756 30.7611C51.0756 30.7611 53.1651 30.3871 52.7221 28.9653C52.7221 28.9653 53.5663 29.3547 53.734 30.2436L53.7288 30.2411Z" fill="#1A1818"/><path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/><path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/><path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3287_12501)"/></g><g clip-path="url(#clip2_3287_12501)"><path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/><path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/><path d="M81.8041 54.8701H72.3047V59.4332H81.8041V54.8701Z" fill="#F9CDB1"/><path d="M92.2785 66.9626H85.8549L84.7075 62.4622C84.7075 62.4622 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9626H92.2785Z" fill="#F9CDB1"/><path d="M69.9639 66.9627L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6858 64.2944 66.9627H69.9639Z" fill="#F9CDB1"/><path d="M80.6781 45.655H74.8174V53.9815H80.6781V45.655Z" fill="#F9CDB1"/><path d="M80.6781 49.0551V45.6575H74.8174V50.5526C76.7512 50.4951 79.0459 50.1553 80.6781 49.0551Z" fill="#EAB894"/><path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/><path d="M68.2589 63.4057V66.9627H87.2847L87.0612 62.8308C86.8241 54.8701 87.7426 54.8675 87.7426 54.8675C86.8861 54.3134 86.1697 54.1279 86.1697 54.1279L81.3997 52.8107C81.3997 52.8107 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5313 74.2085 52.7689L70.4298 54.1279C70.4298 54.1279 68.9323 54.5722 67.5371 55.8867C67.5371 55.8867 68.2104 56.2866 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3287_12501)"/><path d="M81.3168 46.3187C81.1175 46.7683 80.8374 47.1838 80.4469 47.5497C79.4746 48.4592 78.3353 49.1962 77.0856 49.6927C75.5531 50.3043 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6611 70.0613 45.37C69.2291 42.8611 69.2399 40.1222 69.4499 37.5192C69.4984 36.9259 69.5604 36.33 69.6627 35.742C70.6189 30.0629 75.6285 30.0733 75.6285 30.0733C79.4099 29.9139 82.8628 31.6467 83.4446 35.5146C83.6681 36.9965 83.3745 38.5855 83.2964 40.0673C83.2399 41.1545 83.1025 42.2417 82.7443 43.2793C82.378 44.3377 81.7747 45.2943 81.3195 46.3161L81.3168 46.3187Z" fill="#F9CDB1"/><path d="M81.8064 41.9882C81.8064 41.9882 82.3074 39.968 83.9072 40.8017C83.9072 40.8017 84.629 41.1571 84.1092 42.5449C84.1092 42.5449 83.3497 44.5416 81.8037 43.8124V41.9882H81.8064Z" fill="#F9CDB1"/><path d="M69.0724 36.3928C68.8031 28.5942 76.4279 28.9653 76.4279 28.9653C83.9235 29.2632 84.2979 32.5588 84.2979 32.5588C85.2055 36.7404 83.9073 40.8017 83.9073 40.8017C82.3129 40.4646 82.0435 42.5318 82.0435 42.5318C82.2105 39.2179 81.9304 39.0167 81.9304 39.0167C79.4983 38.9932 77.745 38.7397 77.745 38.7397L77.6372 36.4294L77.0501 38.6195C77.0501 38.6195 73.6026 38.5437 70.0232 37.7204C70.0232 37.7204 69.0697 37.7858 69.0697 40.6005V36.3928H69.0724Z" fill="#1A1818"/></g><g clip-path="url(#clip3_3287_12501)"><path d="M11.9798 51.8379C13.2186 51.3943 13.9996 50.3714 15.4728 50.4989C18.0904 50.7274 19.946 53.6286 22.4667 51.6599C25.5153 49.2742 24.7154 34.9304 23.3931 31.6812C22.2728 28.9262 20.2045 31.6201 17.2583 30.6929C13.8327 29.6169 10.8784 32.2843 9.10631 35.0526C7.26424 37.9352 6.31358 41.6068 6.58558 44.9915C6.83604 48.1158 7.40967 53.4745 11.9825 51.8353L11.9798 51.8379Z" fill="#1A1818"/><path d="M0.283728 67H6.70673L7.85399 62.4251C7.85399 62.4251 8.78849 55.4803 3.95979 55.382C3.95979 55.382 1.45522 57.1142 0.771176 61.1923C0.771176 61.1923 0.229866 64.4495 0.281034 67H0.283728Z" fill="url(#paint10_linear_3287_12501)"/><path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3287_12501)"/><path d="M11.883 45.3395H17.7432V53.8039H11.883V45.3395Z" fill="#F9CDB1"/><path d="M11.883 48.7959V45.3422H17.7432V50.3183C15.8095 50.2598 13.515 49.9144 11.883 48.7959Z" fill="#EAB894"/><path d="M25.5127 56.5058C24.1985 54.4973 22.1302 53.9554 22.1302 53.9554L17.1776 52.1461H12.4512L6.39449 53.9554C6.39449 53.9554 4.3262 54.5 3.01197 56.5058H25.5127Z" fill="#F9CDB1"/><path d="M24.3005 67H4.78371L4.90759 63.3842L2.03677 59.1015C2.67503 54.9304 6.39148 53.9554 6.39148 53.9554L11.1609 52.6164C11.1609 52.6164 10.8755 54.1493 14.8128 54.1493C14.8128 54.1493 17.9744 54.1493 18.3515 52.5765L22.1299 53.958C22.1299 53.958 24.8984 54.7923 26.4092 57.5526L24.2978 63.3868V67.0027L24.3005 67Z" fill="url(#paint12_linear_3287_12501)"/><path d="M11.2449 46.0144C11.4442 46.4713 11.7243 46.8938 12.1148 47.2657C13.087 48.1903 14.2261 48.9395 15.4757 49.4442C17.0081 50.0659 19.3214 50.143 20.5414 48.8996C21.5298 47.89 22.0711 46.3624 22.4993 45.05C23.3315 42.4995 23.3207 39.7152 23.1106 37.0691C23.0621 36.466 23.0002 35.8603 22.8979 35.2625C21.9445 29.4894 16.9354 29.5 16.9354 29.5C13.1543 29.338 9.70175 31.0994 9.12004 35.0314C8.89652 36.5378 9.19007 38.1531 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9246C10.1865 44.0006 10.7898 44.9729 11.2449 46.0117V46.0144Z" fill="#F9CDB1"/><path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/><path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs><linearGradient id="paint0_linear_3287_12501" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint1_linear_3287_12501" x1="65.4118" y1="12.5444" x2="65.4118" y2="33.0401" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint2_linear_3287_12501" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint3_linear_3287_12501" x1="98.8464" y1="54.0554" x2="98.8464" y2="66.9627" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint4_linear_3287_12501" x1="109.96" y1="52.5999" x2="109.96" y2="66.9654" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint5_linear_3287_12501" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint6_linear_3287_12501" x1="54.7161" y1="54.6181" x2="54.7161" y2="66.9626" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint7_linear_3287_12501" x1="43.6028" y1="53.226" x2="43.6028" y2="66.9652" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint8_linear_3287_12501" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint9_linear_3287_12501" x1="77.6398" y1="52.7689" x2="77.6398" y2="66.9627" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint10_linear_3287_12501" x1="4.10219" y1="55.382" x2="4.10219" y2="67" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint11_linear_3287_12501" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint12_linear_3287_12501" x1="14.223" y1="52.5765" x2="14.223" y2="67.0027" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><clipPath id="clip0_3287_12501"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath><clipPath id="clip1_3287_12501"><rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip2_3287_12501"><rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip3_3287_12501"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>'))
                                        : appConfigService
                                        .countryConfigCollection ==
                                        "rafco"
                                        ? Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.15" d="M38.6435 66.8377C48.9297 66.8377 57.2684 58.4794 57.2684 48.1688C57.2684 37.8583 48.9297 29.5 38.6435 29.5C28.3572 29.5 20.0186 37.8583 20.0186 48.1688C20.0186 58.4794 28.3572 66.8377 38.6435 66.8377Z" fill="#EA1B23" fill-opacity="0.75"/> <path opacity="0.5" d="M38.6428 60.3294C45.3431 60.3294 50.7748 54.8849 50.7748 48.1688C50.7748 41.4527 45.3431 36.0082 38.6428 36.0082C31.9424 36.0082 26.5107 41.4527 26.5107 48.1688C26.5107 54.8849 31.9424 60.3294 38.6428 60.3294Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M108.011 66.9254C118.343 66.9254 126.719 58.53 126.719 48.1738C126.719 37.8175 118.343 29.4221 108.011 29.4221C97.6794 29.4221 89.3037 37.8175 89.3037 48.1738C89.3037 58.53 97.6794 66.9254 108.011 66.9254Z" fill="#22409A" fill-opacity="0.15"/> <path d="M108.01 60.3882C114.74 60.3882 120.196 54.9196 120.196 48.1737C120.196 41.4277 114.74 35.9591 108.01 35.9591C101.28 35.9591 95.8242 41.4277 95.8242 48.1737C95.8242 54.9196 101.28 60.3882 108.01 60.3882Z" fill="#22409A" fill-opacity="0.15"/> <path d="M38.5814 21.0918C41.1334 18.5321 41.1334 14.3822 38.5814 11.8226C36.0293 9.26293 31.8917 9.26293 29.3396 11.8226C26.7876 14.3822 26.7876 18.5321 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3898_67412)"/> <path d="M65.4118 33.0401C71.0548 33.0401 75.6293 28.452 75.6293 22.7923C75.6293 17.1326 71.0548 12.5444 65.4118 12.5444C59.7689 12.5444 55.1943 17.1326 55.1943 22.7923C55.1943 28.452 59.7689 33.0401 65.4118 33.0401Z" fill="url(#paint1_linear_3898_67412)"/> <path d="M37.0957 18.144C37.0955 18.144 37.0954 18.144 37.0953 18.1438C37.0162 18.0111 35.5472 17.5388 35.1383 17.2602C35.1299 17.2546 35.1292 17.2425 35.1369 17.2359C35.7667 16.6989 36.1853 15.7971 36.1853 15.0008C36.1853 13.7699 35.1869 12.769 33.9597 12.769C32.7325 12.769 31.7345 13.7704 31.7345 15.0008C31.7345 15.7977 32.1537 16.6992 32.7832 17.2362C32.7907 17.2426 32.79 17.2544 32.782 17.2602C32.3729 17.5389 30.9019 18.0115 30.8239 18.144C30.4503 18.7792 30.9864 19.4892 31.7234 19.4892H36.6417C37.1544 19.4892 37.5277 18.9984 37.3053 18.5365C37.2412 18.4033 37.1715 18.2726 37.096 18.1442C37.096 18.1441 37.0958 18.144 37.0957 18.144Z" fill="white"/> <path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3898_67412)"/> <path d="M94.1137 9.20107C94.0478 9.08959 92.8136 8.69289 92.4704 8.4587C92.4634 8.45382 92.4627 8.444 92.4692 8.43848C92.9981 7.98748 93.3497 7.2304 93.3497 6.56135C93.3497 5.52767 92.5115 4.68695 91.4808 4.68695C90.4502 4.68695 89.612 5.52767 89.612 6.56135C89.612 7.2304 89.9635 7.98748 90.4925 8.43848C90.4989 8.444 90.4983 8.45382 90.4913 8.4587C90.1476 8.69289 88.9125 9.08959 88.8471 9.20107C88.5333 9.73445 88.9836 10.3308 89.6024 10.3308H93.3586C93.9775 10.3308 94.4271 9.73471 94.1137 9.20107Z" fill="white"/> <path d="M70.2724 25.3874C70.1515 25.1818 67.8722 24.4493 67.238 24.017C67.2251 24.0084 67.2241 23.9898 67.2359 23.9797C68.2123 23.1468 68.8617 21.7491 68.8617 20.5136C68.8617 18.6055 67.3137 17.0529 65.4107 17.0529C63.5078 17.0529 61.9602 18.605 61.9602 20.5136C61.9602 21.7491 62.6096 23.1468 63.586 23.9797C63.5978 23.9898 63.5967 24.0082 63.5839 24.017C62.9493 24.4493 60.669 25.1818 60.5481 25.3874C59.9691 26.3724 60.8001 27.4731 61.9426 27.4731H68.8784C70.0209 27.4731 70.8519 26.372 70.2724 25.3874Z" fill="white"/> <path d="M41.042 18.188L55.0281 22.5872" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M74.7189 18.4761L87.7192 11.188" stroke="#EA1B23" stroke-opacity="0.5" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M20.3286 14.6929C20.5369 14.9898 20.6918 15.3506 20.8035 15.7531C21.9194 19.7664 16.9092 23.2821 13.5144 20.8708C13.1728 20.6281 12.8854 20.3591 12.6765 20.0614C11.4751 18.3488 11.4246 16.1946 12.385 14.6048C12.6947 14.0916 13.1096 13.6375 13.6253 13.2757C15.7384 11.7933 18.7395 12.4278 20.3286 14.6929Z" fill="#22409A" fill-opacity="0.15"/> <path d="M21.2509 22.7214C20.7846 22.6904 20.5819 23.677 21.0055 23.4788C21.2676 23.3561 21.4619 22.8833 21.3432 22.753C21.3258 22.734 21.2935 22.7242 21.2509 22.7214Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.4968 22.9101L20.3717 22.9978C20.3449 23.0166 20.3076 23.0101 20.2888 22.9834C20.27 22.9566 20.2766 22.9193 20.3034 22.9005L20.4285 22.8127C20.4553 22.7939 20.4926 22.8004 20.5114 22.8272C20.5301 22.8539 20.5235 22.8913 20.4968 22.9101Z" fill="#22409A" fill-opacity="0.15"/> <path d="M25.955 28.7423C25.9221 28.8472 25.7719 28.861 25.6851 28.7938C25.6344 28.7546 25.5662 28.6649 25.5898 28.6052C25.8065 28.0577 26.4894 26.2007 26.1524 25.3343C25.9378 24.7825 24.4818 25.2637 23.312 25.6503C22.2802 25.9914 21.4619 26.4206 21.172 26.0656C20.9029 25.7361 21.3225 24.9288 21.6065 24.2812C21.7738 23.8999 21.9633 23.4676 21.883 23.3821C21.7853 23.278 21.4566 23.4615 21.1927 23.6091C20.8624 23.7936 20.5772 23.9529 20.4382 23.8163C20.207 23.5891 20.3435 23.0585 20.3707 22.9625C20.373 22.9545 20.3813 22.9503 20.3893 22.9527C20.3973 22.9551 20.4026 22.9636 20.4013 22.9719C20.3866 23.0641 20.3211 23.5603 20.5086 23.7446C20.594 23.8286 20.8989 23.6583 21.1438 23.5214C21.4838 23.3314 21.889 23.0812 22.0404 23.2426C22.1601 23.3701 21.9433 23.763 21.6985 24.3214C21.4393 24.9126 21.1167 25.6483 21.3411 25.9372C21.5011 26.1431 22.4058 25.8441 23.2807 25.5549C24.6161 25.1136 26.1147 24.56 26.3639 25.2005C26.5371 25.6458 26.4822 26.3962 26.2883 27.4468C26.1995 27.927 26.0505 28.438 25.955 28.7423Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.3284 14.6936C20.7731 15.3276 20.8737 16.1744 20.59 16.8961C20.0851 18.181 18.6847 19.1052 16.4007 19.2781C10.3634 19.7353 12.383 14.6087 12.3842 14.6057C12.3842 14.6056 12.3841 14.6057 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2763C15.7377 11.7936 18.7391 12.4283 20.3284 14.6936Z" fill="#22409A" fill-opacity="0.15"/> <path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/> <path d="M88.5735 38.1365C88.6486 38.8472 89.2536 39.5756 90.2316 40.8388C90.9251 41.7344 91.5834 42.0222 91.7717 42.5911C91.882 42.9927 91.2088 43.5772 90.9064 44.1631C90.6209 44.7153 91.3916 45.2729 91.5656 45.3895C91.5865 45.4035 91.6131 45.4029 91.6342 45.3892C91.6742 45.3633 91.6744 45.3035 91.6361 45.2752C91.5048 45.1781 91.2452 44.9631 91.0471 44.6538C90.7259 44.1528 91.8798 43.1803 91.9 42.7294C91.9006 42.7162 91.9006 42.7036 91.9006 42.6909C91.9002 42.1824 91.3956 41.8503 91.0381 41.4881C90.1963 40.635 88.87 39.1461 88.7547 38.0567C88.6614 37.1765 91.1163 36.9311 92.935 36.6438C94.5392 36.39 95.9203 36.4 96.0619 35.7298C96.1933 35.1077 95.0826 34.3224 94.2616 33.6563C93.7785 33.264 93.2303 32.8193 93.2784 32.6511C93.3366 32.4455 93.8989 32.4645 94.3505 32.4799C94.9158 32.4989 95.4041 32.5149 95.4948 32.2384C95.6137 31.8774 95.3037 31.4232 95.1157 31.192C95.0846 31.1538 95.1104 31.0792 95.1171 31.0304C95.1232 30.9814 95.1164 30.9068 95.1574 30.8792C96.4367 30.0173 102.717 25.6139 103.217 21.9348C103.638 18.836 102.228 15.9386 99.8629 14.4833C99.1172 14.0247 98.2766 13.7091 97.3686 13.5762C97.3475 13.5726 97.3258 13.5696 97.3041 13.5665C93.4815 13.0432 89.9314 15.9415 89.3743 20.0399C89.3013 20.5755 89.3433 21.1592 89.4695 21.7689C89.4707 21.7725 89.4711 21.7761 89.4719 21.7798C90.24 25.452 94.1004 30.0668 94.8161 30.8986C94.8165 30.8989 94.816 30.8993 94.8155 30.8992C94.8154 30.8992 94.8154 30.8992 94.8154 30.8991C94.7672 30.8931 94.7221 30.9268 94.7156 30.9754C94.7111 31.0097 94.6928 31.0645 94.6586 31.0701C94.2733 31.1327 93.6094 31.2655 93.5611 31.4454C93.4927 31.6996 94.074 32.1942 94.5062 32.1777C94.8619 32.164 94.92 31.3939 94.929 31.1235C94.9296 31.1076 94.9437 31.0958 94.9595 31.0979C94.9667 31.0989 94.9731 31.1025 94.9774 31.1085C95.0832 31.256 95.4697 31.8338 95.3521 32.1914C95.2963 32.3615 94.7749 32.3438 94.3558 32.3296C93.7737 32.3106 93.0644 32.2554 92.9737 32.5734C92.9022 32.8252 93.4594 33.1983 94.1672 33.773C94.9166 34.3807 96.052 35.1209 95.9126 35.6499C95.7601 36.2281 94.2719 36.2806 92.9114 36.4954C90.835 36.8239 88.4656 37.1144 88.5735 38.1365Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#EA1B23" fill-opacity="0.25"/> <path d="M91.8283 17.5349C92.0697 17.7856 93.2592 17.5765 93.8571 17.0027C94.4551 16.4289 94.4226 15.6907 94.2156 15.411C93.8193 14.8753 92.9492 15.3435 92.3513 15.9174C91.7533 16.4911 91.587 17.2841 91.8283 17.5349Z" fill="white"/> <g clip-path="url(#clip0_3898_67412)"> <path d="M119.681 56.8803C118.333 54.7508 116.217 54.1749 116.217 54.1749L111.147 52.2543H106.307L101.237 54.1749C101.237 54.1749 99.1211 54.7508 97.7735 56.8803H119.681Z" fill="#F9CDB1"/> <path d="M102.115 66.9627H94.2816C94.2816 65.5966 94.3681 63.6465 94.3681 63.6465C94.8295 55.4606 100.665 54.1749 100.665 54.1749C105.217 53.0498 102.737 60.242 102.737 60.242C102.855 61.975 102.603 64.3081 102.113 66.96L102.115 66.9627Z" fill="url(#paint3_linear_3898_67412)"/> <path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/> <path d="M105.406 50.4757V47.3979H112.052V48.9221C110.156 50.5346 107.873 50.7382 105.406 50.4757Z" fill="#EAB894"/> <path d="M98.3296 66.9627C98.2012 63.3947 98.2405 62.4144 98.272 61.1956C98.3401 58.4848 98.909 56.1919 100.658 54.1776C100.66 54.1776 100.666 54.1776 100.666 54.1776L105.403 52.5999C105.403 52.5999 104.407 54.3811 108.761 54.3811C108.761 54.3811 111.63 54.266 112.049 52.5999L116.86 54.1776C116.86 54.1776 120.973 55.2115 121.679 59.6339L119.261 64.1769L119.348 66.9654H98.3323L98.3296 66.9627Z" fill="url(#paint4_linear_3898_67412)"/> <path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/> <path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/> <path d="M116.065 31.8618C117.827 32.7297 117.887 34.3342 117.803 36.161C117.74 37.578 117.31 38.7834 116.815 40.1575C116.252 41.7183 115.721 42.9478 115.221 43.846C115.221 43.846 114.054 45.6996 112.552 46.9693C112.987 45.2068 113.11 43.8915 113.506 42.5362C113.983 40.9102 113.845 38.7352 113.137 37.0664C113.137 37.0664 112.736 35.9548 111.089 35.6387L116.068 31.8591" fill="#1A1818"/> <path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.222C115.325 43.222 114.18 45.1399 112.806 44.0765L113.17 42.188Z" fill="#F9CDB1"/> <path d="M113.312 37.5378C113.312 37.5378 113.307 41.1085 112.664 43.0022L113.346 43.1415C113.346 43.1415 113.705 40.6933 114.938 40.6638L113.312 37.5405V37.5378Z" fill="#1A1818"/> <path d="M99.9442 29.8931C94.2812 35.7861 111.419 35.7272 111.419 35.7272C111.419 35.7272 113.218 36.5763 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0136C108.727 27.0136 103.371 26.3413 99.9442 29.8931Z" fill="#1A1818"/> <path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3898_67412)"/></g> <g clip-path="url(#clip1_3898_67412)"> <path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1607 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2666 34.7656 34.7422Z" fill="#1A1818"/> <path d="M37.0334 32.188C37.0334 32.188 36.2653 33.1205 37.3716 34.1837L36.6245 34.9215C36.6245 34.9215 34.9125 32.872 37.0334 32.1906V32.188Z" fill="#1A1818"/> <path d="M33.8818 57.3224C35.2294 55.2857 37.3451 54.7349 37.3451 54.7349L42.4155 52.8981H47.2552L52.3257 54.7349C52.3257 54.7349 54.4414 55.2857 55.789 57.3224H33.8818Z" fill="#F9CDB1"/> <path d="M51.4472 66.9627H59.2809C59.2809 65.6562 59.1944 63.7911 59.1944 63.7911C58.733 55.9621 52.897 54.7324 52.897 54.7324C48.3457 53.6564 50.8259 60.535 50.8259 60.535C50.7079 62.1925 50.9596 64.4239 51.4498 66.9601L51.4472 66.9627Z" fill="url(#paint6_linear_3898_67412)"/> <path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/> <path d="M48.1568 51.1944V48.2509H41.5107V49.7086C43.4062 51.2508 45.6898 51.4455 48.1568 51.1944Z" fill="#EAB894"/> <path d="M55.2329 66.9626C55.3613 63.5502 55.322 62.6126 55.2905 61.4469C55.2224 58.8543 54.6535 56.6614 52.9048 54.7349C52.9021 54.7349 52.8969 54.7349 52.8969 54.7349L48.1594 53.226C48.1594 53.226 49.1557 54.9296 44.801 54.9296C44.801 54.9296 41.9329 54.8194 41.5134 53.226L36.7025 54.7349C36.7025 54.7349 32.589 55.7238 31.8838 59.9534L34.301 64.2983L34.2145 66.9652H55.2302L55.2329 66.9626Z" fill="url(#paint7_linear_3898_67412)"/> <path d="M53.3222 40.3373C53.5109 38.8822 53.1937 37.486 53.2094 36.0513C53.2199 35.0881 53.0285 33.6355 51.8016 33.5125C50.1027 33.3435 49.5783 36.2076 49.4787 37.4501C49.3398 39.1614 49.775 41.0111 50.1132 42.6865C50.2338 43.2911 50.2233 44.5003 50.918 44.7924C51.5132 45.0409 52.124 44.4542 52.4281 44.0162C53.1622 42.9607 53.1648 41.5568 53.3195 40.3373H53.3222Z" fill="#1A1818"/> <path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/> <path d="M53.7288 30.241C54.0146 31.7756 53.7472 33.9019 52.5307 34.8831C51.2093 35.9463 50.0741 35.6568 48.4827 36.0026C46.8284 36.3613 45.1348 36.5048 43.4726 36.8173C42.3086 37.0376 40.9636 37.1631 40.4288 38.3698C39.7209 39.9658 39.582 42.046 40.0591 43.6011C40.455 44.8948 40.5782 46.1553 41.0135 47.841C39.5112 46.6266 38.3445 44.8538 38.3445 44.8538C37.8447 43.9948 37.3133 42.8189 36.7505 41.3262C36.255 40.0119 35.8277 38.8591 35.7621 37.5039C35.6809 35.7567 36.546 34.9958 37.5003 33.3921C38.397 31.8857 39.6554 30.8405 41.2966 30.364C41.2966 30.364 49.6127 30.82 49.458 28.9652C49.458 28.9652 51.4558 29.4648 51.0756 30.7611C51.0756 30.7611 53.1651 30.387 52.7221 28.9652C52.7221 28.9652 53.5663 29.3546 53.734 30.2436L53.7288 30.241Z" fill="#1A1818"/> <path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/> <path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/> <path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3898_67412)"/></g> <g clip-path="url(#clip2_3898_67412)"> <path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/> <path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/> <path d="M81.8041 54.8701H72.3047V59.4333H81.8041V54.8701Z" fill="#F9CDB1"/> <path d="M92.2785 66.9627H85.8549L84.7075 62.4623C84.7075 62.4623 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9627H92.2785Z" fill="#F9CDB1"/> <path d="M69.9639 66.9626L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6857 64.2944 66.9626H69.9639Z" fill="#F9CDB1"/> <path d="M80.6781 45.6549H74.8174V53.9815H80.6781V45.6549Z" fill="#F9CDB1"/> <path d="M80.6781 49.0551V45.6576H74.8174V50.5526C76.7512 50.4951 79.0459 50.1554 80.6781 49.0551Z" fill="#EAB894"/> <path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/> <path d="M68.2589 63.4057V66.9626H87.2847L87.0612 62.8307C86.8241 54.87 87.7426 54.8674 87.7426 54.8674C86.8861 54.3134 86.1697 54.1278 86.1697 54.1278L81.3997 52.8106C81.3997 52.8106 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5312 74.2085 52.7688L70.4298 54.1278C70.4298 54.1278 68.9323 54.5721 67.5371 55.8867C67.5371 55.8867 68.2104 56.2865 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3898_67412)"/> <path d="M81.3168 46.3188C81.1175 46.7683 80.8374 47.1839 80.4469 47.5498C79.4746 48.4592 78.3353 49.1962 77.0856 49.6928C75.5531 50.3044 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6612 70.0613 45.3701C69.2291 42.8612 69.2399 40.1222 69.4499 37.5192C69.4984 36.926 69.5604 36.3301 69.6627 35.742C70.6189 30.063 75.6285 30.0734 75.6285 30.0734C79.4099 29.914 82.8628 31.6467 83.4446 35.5147C83.6681 36.9965 83.3745 38.5855 83.2964 40.0674C83.2399 41.1546 83.1025 42.2418 82.7443 43.2793C82.378 44.3378 81.7747 45.2943 81.3195 46.3162L81.3168 46.3188Z" fill="#F9CDB1"/> <path d="M81.8064 41.9882C81.8064 41.9882 82.3074 39.968 83.9072 40.8017C83.9072 40.8017 84.629 41.1571 84.1092 42.5449C84.1092 42.5449 83.3497 44.5416 81.8037 43.8124V41.9882H81.8064Z" fill="#F9CDB1"/> <path d="M69.0724 36.3927C68.8031 28.5941 76.4279 28.9652 76.4279 28.9652C83.9235 29.2631 84.2979 32.5587 84.2979 32.5587C85.2055 36.7403 83.9073 40.8017 83.9073 40.8017C82.3129 40.4645 82.0435 42.5318 82.0435 42.5318C82.2105 39.2179 81.9304 39.0167 81.9304 39.0167C79.4983 38.9931 77.745 38.7396 77.745 38.7396L77.6372 36.4293L77.0501 38.6194C77.0501 38.6194 73.6026 38.5436 70.0232 37.7204C70.0232 37.7204 69.0697 37.7857 69.0697 40.6004V36.3927H69.0724Z" fill="#1A1818"/></g> <g clip-path="url(#clip3_3898_67412)"> <path d="M11.9798 51.8379C13.2186 51.3943 13.9996 50.3714 15.4728 50.4989C18.0904 50.7274 19.946 53.6286 22.4667 51.6599C25.5153 49.2742 24.7154 34.9304 23.3931 31.6812C22.2728 28.9262 20.2045 31.6201 17.2583 30.6929C13.8327 29.6169 10.8784 32.2843 9.10631 35.0526C7.26424 37.9352 6.31358 41.6068 6.58558 44.9915C6.83604 48.1158 7.40967 53.4745 11.9825 51.8353L11.9798 51.8379Z" fill="#1A1818"/> <path d="M0.283728 67H6.70673L7.85399 62.4251C7.85399 62.4251 8.78849 55.4804 3.95979 55.3821C3.95979 55.3821 1.45522 57.1143 0.771176 61.1924C0.771176 61.1924 0.229866 64.4496 0.281034 67H0.283728Z" fill="url(#paint10_linear_3898_67412)"/> <path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3898_67412)"/> <path d="M11.883 45.3395H17.7432V53.8039H11.883V45.3395Z" fill="#F9CDB1"/> <path d="M11.883 48.7959V45.3422H17.7432V50.3183C15.8095 50.2598 13.515 49.9144 11.883 48.7959Z" fill="#EAB894"/> <path d="M25.5127 56.5058C24.1985 54.4973 22.1302 53.9554 22.1302 53.9554L17.1776 52.1461H12.4512L6.39449 53.9554C6.39449 53.9554 4.3262 54.5 3.01197 56.5058H25.5127Z" fill="#F9CDB1"/> <path d="M24.3005 67H4.78371L4.90759 63.3842L2.03677 59.1015C2.67503 54.9304 6.39148 53.9554 6.39148 53.9554L11.1609 52.6164C11.1609 52.6164 10.8755 54.1493 14.8128 54.1493C14.8128 54.1493 17.9744 54.1493 18.3515 52.5765L22.1299 53.958C22.1299 53.958 24.8984 54.7923 26.4092 57.5526L24.2978 63.3868V67.0027L24.3005 67Z" fill="url(#paint12_linear_3898_67412)"/> <path d="M11.2449 46.0143C11.4442 46.4713 11.7243 46.8937 12.1148 47.2656C13.087 48.1902 14.2261 48.9394 15.4757 49.4442C17.0081 50.0659 19.3214 50.1429 20.5414 48.8995C21.5298 47.89 22.0711 46.3623 22.4993 45.0499C23.3315 42.4994 23.3207 39.7152 23.1106 37.069C23.0621 36.466 23.0002 35.8602 22.8979 35.2625C21.9445 29.4893 16.9354 29.5 16.9354 29.5C13.1543 29.3379 9.70175 31.0993 9.12004 35.0313C8.89652 36.5377 9.19007 38.153 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9245C10.1865 44.0005 10.7898 44.9729 11.2449 46.0117V46.0143Z" fill="#F9CDB1"/> <path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/> <path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs> <linearGradient id="paint0_linear_3898_67412" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint1_linear_3898_67412" x1="65.4118" y1="12.5444" x2="65.4118" y2="33.0401" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint2_linear_3898_67412" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint3_linear_3898_67412" x1="98.8464" y1="54.0554" x2="98.8464" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint4_linear_3898_67412" x1="109.96" y1="52.5999" x2="109.96" y2="66.9654" gradientUnits="userSpaceOnUse"><stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint5_linear_3898_67412" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint6_linear_3898_67412" x1="54.7161" y1="54.6182" x2="54.7161" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint7_linear_3898_67412" x1="43.6028" y1="53.226" x2="43.6028" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint8_linear_3898_67412" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint9_linear_3898_67412" x1="77.6398" y1="52.7688" x2="77.6398" y2="66.9626" gradientUnits="userSpaceOnUse"> <stop stop-color="#F2484F"/> <stop offset="1" stop-color="#EA1B23"/></linearGradient> <linearGradient id="paint10_linear_3898_67412" x1="4.10219" y1="55.3821" x2="4.10219" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient> <linearGradient id="paint11_linear_3898_67412" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#4A77FF"/> <stop offset="1" stop-color="#22409A"/></linearGradient><linearGradient id="paint12_linear_3898_67412" x1="14.223" y1="52.5765" x2="14.223" y2="67.0027" gradientUnits="userSpaceOnUse"><stop stop-color="#4A77FF"/><stop offset="1" stop-color="#22409A"/></linearGradient><clipPath id="clip0_3898_67412"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath><clipPath id="clip1_3898_67412"><rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip2_3898_67412"><rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath><clipPath id="clip3_3898_67412"><rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>'))
                                        : Container(
                                        width: double.infinity,
                                        child: SvgPicture.string(
                                            '<svg width="127" height="67" viewBox="0 0 127 67" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.15" d="M38.6445 66.8377C48.9307 66.8377 57.2694 58.4794 57.2694 48.1688C57.2694 37.8583 48.9307 29.5 38.6445 29.5C28.3582 29.5 20.0195 37.8583 20.0195 48.1688C20.0195 58.4794 28.3582 66.8377 38.6445 66.8377Z" fill="#6A7165" fill-opacity="0.75"/> <path opacity="0.5" d="M38.6437 60.3294C45.3441 60.3294 50.7757 54.8849 50.7757 48.1688C50.7757 41.4527 45.3441 36.0082 38.6437 36.0082C31.9434 36.0082 26.5117 41.4527 26.5117 48.1688C26.5117 54.8849 31.9434 60.3294 38.6437 60.3294Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M108.012 66.9254C118.344 66.9254 126.72 58.53 126.72 48.1738C126.72 37.8175 118.344 29.4221 108.012 29.4221C97.6803 29.4221 89.3047 37.8175 89.3047 48.1738C89.3047 58.53 97.6803 66.9254 108.012 66.9254Z" fill="#22409A" fill-opacity="0.15"/> <path d="M108.012 60.3882C114.742 60.3882 120.198 54.9196 120.198 48.1737C120.198 41.4277 114.742 35.9591 108.012 35.9591C101.282 35.9591 95.8262 41.4277 95.8262 48.1737C95.8262 54.9196 101.282 60.3882 108.012 60.3882Z" fill="#22409A" fill-opacity="0.15"/> <path d="M38.5814 21.0918C41.1334 18.5321 41.1334 14.3822 38.5814 11.8226C36.0293 9.26293 31.8917 9.26293 29.3396 11.8226C26.7876 14.3822 26.7876 18.5321 29.3396 21.0918C31.8917 23.6514 36.0293 23.6514 38.5814 21.0918Z" fill="url(#paint0_linear_3696_32998)"/> <path d="M65.4109 33.0401C71.0538 33.0401 75.6284 28.452 75.6284 22.7923C75.6284 17.1326 71.0538 12.5444 65.4109 12.5444C59.7679 12.5444 55.1934 17.1326 55.1934 22.7923C55.1934 28.452 59.7679 33.0401 65.4109 33.0401Z" fill="url(#paint1_linear_3696_32998)"/> <path d="M37.0967 18.144C37.0965 18.144 37.0964 18.144 37.0963 18.1438C37.0172 18.0111 35.5482 17.5388 35.1393 17.2602C35.1309 17.2546 35.1302 17.2425 35.1379 17.2359C35.7677 16.6989 36.1863 15.7971 36.1863 15.0008C36.1863 13.7699 35.1879 12.769 33.9607 12.769C32.7334 12.769 31.7355 13.7704 31.7355 15.0008C31.7355 15.7977 32.1547 16.6992 32.7842 17.2362C32.7917 17.2426 32.791 17.2544 32.7829 17.2602C32.3739 17.5389 30.9029 18.0115 30.8249 18.144C30.4512 18.7792 30.9874 19.4892 31.7244 19.4892H36.6427C37.1553 19.4892 37.5287 18.9984 37.3063 18.5365C37.2422 18.4033 37.1725 18.2726 37.097 18.1442C37.0969 18.1441 37.0968 18.144 37.0967 18.144Z" fill="white"/> <path d="M95.3604 11.6763C97.5036 9.52672 97.5036 6.04163 95.3604 3.8921C93.2172 1.74256 89.7425 1.74255 87.5993 3.89209C85.4561 6.04163 85.4561 9.52673 87.5993 11.6763C89.7425 13.8258 93.2172 13.8258 95.3604 11.6763Z" fill="url(#paint2_linear_3696_32998)"/><path d="M94.1146 9.201C94.0488 9.08953 92.8146 8.69283 92.4713 8.45864C92.4644 8.45376 92.4637 8.44394 92.4702 8.43842C92.9991 7.98742 93.3507 7.23034 93.3507 6.56129C93.3507 5.52761 92.5124 4.68689 91.4818 4.68689C90.4512 4.68689 89.613 5.52761 89.613 6.56129C89.613 7.23034 89.9645 7.98742 90.4934 8.43842C90.4999 8.44394 90.4992 8.45376 90.4923 8.45864C90.1486 8.69283 88.9134 9.08953 88.848 9.201C88.5343 9.73439 88.9846 10.3307 89.6034 10.3307H93.3596C93.9785 10.3307 94.4281 9.73465 94.1146 9.201Z" fill="white"/> <path d="M70.2734 25.3874C70.1525 25.1818 67.8731 24.4493 67.239 24.017C67.2261 24.0084 67.2251 23.9898 67.2369 23.9797C68.2133 23.1468 68.8627 21.7491 68.8627 20.5136C68.8627 18.6055 67.3146 17.0529 65.4117 17.0529C63.5087 17.0529 61.9612 18.605 61.9612 20.5136C61.9612 21.7491 62.6105 23.1468 63.5869 23.9797C63.5987 23.9898 63.5976 24.0082 63.5849 24.017C62.9502 24.4493 60.67 25.1818 60.549 25.3874C59.9701 26.3724 60.8011 27.4731 61.9436 27.4731H68.8794C70.0218 27.4731 70.8528 26.372 70.2734 25.3874Z" fill="white"/> <path d="M41.043 18.188L55.029 22.5872" stroke="#6A7165" stroke-opacity="0.75" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M74.7189 18.4761L87.7192 11.188" stroke="#6A7165" stroke-opacity="0.75" stroke-width="0.5" stroke-dasharray="2 2"/> <path d="M20.3277 14.6929C20.5359 14.9898 20.6908 15.3506 20.8025 15.7531C21.9184 19.7664 16.9082 23.2821 13.5134 20.8708C13.1718 20.6281 12.8844 20.3591 12.6755 20.0614C11.4741 18.3488 11.4236 16.1946 12.384 14.6048C12.6937 14.0916 13.1086 13.6375 13.6243 13.2757C15.7374 11.7933 18.7385 12.4278 20.3277 14.6929Z" fill="#22409A" fill-opacity="0.15"/> <path d="M21.2509 22.7214C20.7846 22.6904 20.5819 23.677 21.0055 23.4788C21.2676 23.3561 21.4619 22.8833 21.3432 22.753C21.3258 22.734 21.2935 22.7242 21.2509 22.7214Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.4977 22.9101L20.3727 22.9978C20.3459 23.0166 20.3086 23.0101 20.2898 22.9834C20.271 22.9566 20.2776 22.9193 20.3044 22.9005L20.4295 22.8127C20.4563 22.7939 20.4936 22.8004 20.5124 22.8272C20.5311 22.8539 20.5245 22.8913 20.4977 22.9101Z" fill="#22409A" fill-opacity="0.15"/> <path d="M25.953 28.7423C25.9201 28.8472 25.7699 28.861 25.6831 28.7938C25.6325 28.7546 25.5642 28.6649 25.5879 28.6052C25.8046 28.0577 26.4875 26.2007 26.1505 25.3343C25.9358 24.7825 24.4799 25.2637 23.3101 25.6503C22.2782 25.9914 21.46 26.4206 21.17 26.0656C20.9009 25.7361 21.3206 24.9288 21.6046 24.2812C21.7718 23.8999 21.9613 23.4676 21.8811 23.3821C21.7833 23.278 21.4547 23.4615 21.1907 23.6091C20.8605 23.7936 20.5752 23.9529 20.4362 23.8163C20.205 23.5891 20.3416 23.0585 20.3688 22.9625C20.371 22.9545 20.3794 22.9503 20.3874 22.9527C20.3954 22.9551 20.4006 22.9636 20.3993 22.9719C20.3847 23.0641 20.3192 23.5603 20.5067 23.7446C20.5921 23.8286 20.8969 23.6583 21.1418 23.5214C21.4819 23.3314 21.887 23.0812 22.0384 23.2426C22.1581 23.3701 21.9414 23.763 21.6966 24.3214C21.4374 24.9126 21.1148 25.6483 21.3392 25.9372C21.4992 26.1431 22.4039 25.8441 23.2788 25.5549C24.6141 25.1136 26.1127 24.56 26.362 25.2005C26.5352 25.6458 26.4802 26.3962 26.2863 27.4468C26.1976 27.927 26.0485 28.438 25.953 28.7423Z" fill="#22409A" fill-opacity="0.15"/> <path d="M20.3284 14.6936C20.7731 15.3276 20.8737 16.1744 20.59 16.8961C20.0851 18.181 18.6847 19.1052 16.4007 19.2781C10.3634 19.7353 12.383 14.6087 12.3842 14.6057C12.3842 14.6056 12.3841 14.6057 12.3841 14.6056C12.384 14.6055 12.384 14.6054 12.3841 14.6053C12.6938 14.0922 13.1087 13.6381 13.6244 13.2763C15.7377 11.7936 18.7391 12.4283 20.3284 14.6936Z" fill="#22409A" fill-opacity="0.15"/> <path d="M17.7952 13.7816C17.7595 14.0578 16.8942 14.4902 16.2356 14.4063C15.577 14.3225 15.2455 13.8326 15.2452 13.5542C15.2447 13.0211 16.0281 12.9075 16.6867 12.9913C17.3454 13.0752 17.8308 13.5055 17.7952 13.7816Z" fill="white"/> <path d="M88.5745 38.1365C88.6496 38.8472 89.2546 39.5756 90.2326 40.8388C90.9261 41.7344 91.5844 42.0222 91.7726 42.5911C91.883 42.9927 91.2098 43.5772 90.9074 44.1631C90.6218 44.7153 91.3926 45.2729 91.5666 45.3895C91.5875 45.4035 91.6141 45.4029 91.6352 45.3892C91.6752 45.3633 91.6754 45.3035 91.6371 45.2752C91.5057 45.1781 91.2462 44.9631 91.048 44.6538C90.7268 44.1528 91.8808 43.1803 91.901 42.7294C91.9016 42.7162 91.9016 42.7036 91.9016 42.6909C91.9012 42.1824 91.3965 41.8503 91.0391 41.4881C90.1973 40.635 88.871 39.1461 88.7557 38.0567C88.6624 37.1765 91.1173 36.9311 92.9359 36.6438C94.5402 36.39 95.9212 36.4 96.0629 35.7298C96.1943 35.1077 95.0836 34.3224 94.2625 33.6563C93.7794 33.264 93.2313 32.8193 93.2793 32.6511C93.3376 32.4455 93.8999 32.4645 94.3514 32.4799C94.9168 32.4989 95.405 32.5149 95.4958 32.2384C95.6147 31.8774 95.3047 31.4232 95.1167 31.192C95.0856 31.1538 95.1114 31.0792 95.118 31.0304C95.1242 30.9814 95.1174 30.9068 95.1583 30.8792C96.4377 30.0173 102.718 25.6139 103.218 21.9348C103.639 18.836 102.229 15.9386 99.8638 14.4833C99.1182 14.0247 98.2775 13.7091 97.3696 13.5762C97.3484 13.5726 97.3268 13.5696 97.3051 13.5665C93.4824 13.0432 89.9324 15.9415 89.3753 20.0399C89.3023 20.5755 89.3442 21.1592 89.4705 21.7689C89.4716 21.7725 89.4721 21.7761 89.4728 21.7798C90.241 25.452 94.1014 30.0668 94.8171 30.8986C94.8175 30.8989 94.8169 30.8993 94.8165 30.8992C94.8164 30.8992 94.8164 30.8992 94.8164 30.8991C94.7682 30.8931 94.7231 30.9268 94.7166 30.9754C94.712 31.0097 94.6937 31.0645 94.6596 31.0701C94.2743 31.1327 93.6104 31.2655 93.5621 31.4454C93.4936 31.6996 94.075 32.1942 94.5072 32.1777C94.8628 32.164 94.921 31.3939 94.93 31.1235C94.9306 31.1076 94.9446 31.0958 94.9604 31.0979C94.9677 31.0989 94.9741 31.1025 94.9784 31.1085C95.0842 31.256 95.4707 31.8338 95.3531 32.1914C95.2972 32.3615 94.7759 32.3438 94.3568 32.3296C93.7747 32.3106 93.0654 32.2554 92.9747 32.5734C92.9032 32.8252 93.4604 33.1983 94.1682 33.773C94.9176 34.3807 96.053 35.1209 95.9136 35.6499C95.7611 36.2281 94.2729 36.2806 92.9124 36.4954C90.836 36.8239 88.4665 37.1144 88.5745 38.1365Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M89.4557 21.699C89.4648 21.7453 89.4786 21.7902 89.4975 21.8335C89.5239 21.8939 89.5602 21.9502 89.6041 21.9994C91.2339 23.8272 93.9683 23.9984 97.1061 21.7312C102.896 17.5482 98.5547 14.3255 97.4216 13.6088C97.3852 13.5857 97.3471 13.5721 97.3045 13.5661C93.4818 13.0428 89.9318 15.941 89.3747 20.0395C89.3043 20.5548 89.3405 21.1145 89.4557 21.699Z" fill="#FFC20E" fill-opacity="0.25"/> <path d="M91.8293 17.5349C92.0707 17.7856 93.2602 17.5765 93.8581 17.0027C94.4561 16.4289 94.4235 15.6907 94.2166 15.411C93.8202 14.8753 92.9502 15.3435 92.3522 15.9174C91.7543 16.4911 91.5879 17.2841 91.8293 17.5349Z" fill="white"/> <g clip-path="url(#clip0_3696_32998)"> <path d="M119.68 56.8803C118.332 54.7508 116.216 54.1749 116.216 54.1749L111.146 52.2543H106.306L101.236 54.1749C101.236 54.1749 99.1201 54.7508 97.7726 56.8803H119.68Z" fill="#F9CDB1"/> <path d="M102.114 66.9627H94.2806C94.2806 65.5966 94.3671 63.6465 94.3671 63.6465C94.8285 55.4606 100.664 54.1749 100.664 54.1749C105.216 53.0498 102.736 60.242 102.736 60.242C102.854 61.975 102.602 64.3081 102.112 66.96L102.114 66.9627Z" fill="url(#paint3_linear_3696_32998)"/> <path d="M105.403 47.3979H112.049V54.0168H105.403V47.3979Z" fill="#F9CDB1"/> <path d="M105.405 50.4757V47.3979H112.051V48.9221C110.155 50.5346 107.872 50.7382 105.405 50.4757Z" fill="#EAB894"/> <path d="M98.3287 66.9627C98.2002 63.3947 98.2395 62.4144 98.271 61.1956C98.3392 58.4848 98.9081 56.1919 100.657 54.1776C100.659 54.1776 100.665 54.1776 100.665 54.1776L105.402 52.5999C105.402 52.5999 104.406 54.3811 108.761 54.3811C108.761 54.3811 111.629 54.266 112.048 52.5999L116.859 54.1776C116.859 54.1776 120.972 55.2115 121.678 59.6339L119.261 64.1769L119.347 66.9654H98.3313L98.3287 66.9627Z" fill="url(#paint4_linear_3696_32998)"/> <path d="M100.817 37.5379C100.817 35.9655 100.883 34.3958 100.846 32.8235C100.822 31.819 100.733 32.0788 101.871 31.969C103.449 31.8163 103.976 34.811 104.086 36.1075C104.238 37.8941 103.861 39.8174 103.57 41.5585C103.465 42.188 103.494 43.4469 102.849 43.7443C102.298 43.9961 101.721 43.3746 101.433 42.9139C100.767 41.8558 100.885 40.2353 100.841 39.0352C100.822 38.537 100.817 38.0388 100.817 37.5406V37.5379Z" fill="#1A1818"/> <path d="M101.499 46.4765C102.17 49.1605 103.353 49.9025 103.353 49.9025L104.939 50.2185C105.851 50.1007 107.427 49.7203 107.427 49.7203C111.05 48.4614 112.101 48.1212 112.662 46.7149C112.686 46.656 115.52 36.6084 115.52 36.6084C115.984 31.436 110.686 30.3618 109.886 30.2252C109.094 30.0377 104.05 28.6073 102.561 33.5708C102.561 33.5708 100.359 39.3219 101.499 46.4765Z" fill="#F9CDB1"/> <path d="M116.065 31.8618C117.827 32.7297 117.887 34.3342 117.803 36.161C117.74 37.578 117.31 38.7834 116.815 40.1575C116.252 41.7183 115.721 42.9478 115.221 43.846C115.221 43.846 114.054 45.6996 112.552 46.9693C112.987 45.2068 113.11 43.8915 113.506 42.5362C113.983 40.9102 113.845 38.7352 113.137 37.0664C113.137 37.0664 112.736 35.9548 111.089 35.6387L116.068 31.8591" fill="#1A1818"/> <path d="M113.17 42.188C113.17 42.188 114.067 40.1951 115.475 41.3764C115.475 41.3764 116.117 41.888 115.325 43.222C115.325 43.222 114.18 45.1399 112.806 44.0765L113.17 42.188Z" fill="#F9CDB1"/> <path d="M113.312 37.5378C113.312 37.5378 113.307 41.1085 112.664 43.0022L113.346 43.1415C113.346 43.1415 113.705 40.6933 114.938 40.6638L113.312 37.5405V37.5378Z" fill="#1A1818"/> <path d="M99.9442 29.8931C94.2812 35.7861 111.419 35.7272 111.419 35.7272C111.419 35.7272 113.218 36.5763 116.063 31.8619C116.063 31.8619 116.831 28.1761 108.727 27.0136C108.727 27.0136 103.371 26.3413 99.9442 29.8931Z" fill="#1A1818"/> <path d="M114.959 66.9626H122.281C122.281 66.9626 122.255 65.1867 122.252 65.0608C122.197 63.3143 122.09 61.5545 121.781 59.8348C121.34 57.3758 120.171 55.1927 117.596 54.5686C116.419 54.282 115.37 55.0106 114.733 56.0017C114.733 56.0017 113.569 57.4696 114.959 66.9653V66.9626Z" fill="url(#paint5_linear_3696_32998)"/></g> <g clip-path="url(#clip1_3696_32998)"> <path d="M34.7656 34.7422C34.7656 34.7422 35.4158 34.1607 36.6533 35.2136L37.23 34.34C37.23 34.34 35.8405 33.2666 34.7656 34.7422Z" fill="#1A1818"/> <path d="M37.0325 32.188C37.0325 32.188 36.2643 33.1205 37.3707 34.1837L36.6235 34.9215C36.6235 34.9215 34.9115 32.872 37.0325 32.1906V32.188Z" fill="#1A1818"/> <path d="M33.8828 57.3224C35.2304 55.2857 37.3461 54.7349 37.3461 54.7349L42.4165 52.8981H47.2562L52.3266 54.7349C52.3266 54.7349 54.4424 55.2857 55.7899 57.3224H33.8828Z" fill="#F9CDB1"/> <path d="M51.4482 66.9627H59.2819C59.2819 65.6562 59.1954 63.7911 59.1954 63.7911C58.734 55.9621 52.898 54.7324 52.898 54.7324C48.3467 53.6564 50.8269 60.535 50.8269 60.535C50.7089 62.1925 50.9606 64.4239 51.4508 66.9601L51.4482 66.9627Z" fill="url(#paint6_linear_3696_32998)"/> <path d="M48.1597 48.2509H41.5137V54.5812H48.1597V48.2509Z" fill="#F9CDB1"/> <path d="M48.1578 51.1944V48.2509H41.5117V49.7086C43.4072 51.2508 45.6907 51.4455 48.1578 51.1944Z" fill="#EAB894"/> <path d="M55.2338 66.9626C55.3623 63.5502 55.323 62.6126 55.2915 61.4469C55.2233 58.8543 54.6544 56.6614 52.9057 54.7349C52.9031 54.7349 52.8979 54.7349 52.8979 54.7349L48.1604 53.226C48.1604 53.226 49.1567 54.9296 44.802 54.9296C44.802 54.9296 41.9338 54.8194 41.5144 53.226L36.7035 54.7349C36.7035 54.7349 32.59 55.7238 31.8848 59.9534L34.302 64.2983L34.2155 66.9652H55.2312L55.2338 66.9626Z" fill="url(#paint7_linear_3696_32998)"/> <path d="M53.3212 40.3373C53.5099 38.8822 53.1927 37.486 53.2084 36.0513C53.2189 35.0881 53.0275 33.6355 51.8006 33.5125C50.1017 33.3435 49.5774 36.2076 49.4777 37.4501C49.3388 39.1614 49.774 41.0111 50.1122 42.6865C50.2328 43.2911 50.2223 44.5003 50.9171 44.7924C51.5122 45.0409 52.123 44.4542 52.4272 44.0162C53.1612 42.9607 53.1639 41.5568 53.3186 40.3373H53.3212Z" fill="#1A1818"/> <path d="M52.0634 47.3696C51.3922 49.9365 50.2098 50.6462 50.2098 50.6462L48.6237 50.9485C47.7113 50.8358 46.1357 50.472 46.1357 50.472C42.5124 49.2679 41.4611 48.9425 40.9001 47.5976C40.8765 47.5412 38.0424 37.9317 38.0424 37.9317C37.5784 32.9847 42.8769 31.9574 43.6765 31.8268C44.4682 31.6475 49.5124 30.2794 51.0016 35.0265C51.0016 35.0265 53.2038 40.5268 52.0634 47.3696Z" fill="#F9CDB1"/> <path d="M53.7278 30.241C54.0136 31.7756 53.7462 33.9019 52.5297 34.8831C51.2084 35.9463 50.0731 35.6568 48.4818 36.0026C46.8275 36.3613 45.1338 36.5048 43.4717 36.8173C42.3076 37.0376 40.9627 37.1631 40.4278 38.3698C39.72 39.9658 39.581 42.046 40.0582 43.6011C40.454 44.8948 40.5773 46.1553 41.0125 47.841C39.5102 46.6266 38.3436 44.8538 38.3436 44.8538C37.8437 43.9948 37.3123 42.8189 36.7496 41.3262C36.254 40.0119 35.8267 38.8591 35.7612 37.5039C35.6799 35.7567 36.5451 34.9958 37.4994 33.3921C38.396 31.8857 39.6544 30.8405 41.2956 30.364C41.2956 30.364 49.6117 30.82 49.457 28.9652C49.457 28.9652 51.4548 29.4648 51.0746 30.7611C51.0746 30.7611 53.1642 30.387 52.7211 28.9652C52.7211 28.9652 53.5653 29.3546 53.7331 30.2436L53.7278 30.241Z" fill="#1A1818"/> <path d="M40.3922 43.2681C40.3922 43.2681 39.4955 41.362 38.0877 42.4918C38.0877 42.4918 37.4453 42.9811 38.2371 44.2569C38.2371 44.2569 39.3828 46.0912 40.7566 45.0742L40.3922 43.2681Z" fill="#F9CDB1"/> <path d="M40.2505 38.8207C40.2505 38.8207 40.2557 42.2356 40.898 44.0469L40.2164 44.1801C40.2164 44.1801 39.8572 41.8385 38.625 41.8104L40.2505 38.8232V38.8207Z" fill="#1A1818"/> <path d="M38.6037 66.9627H31.2812C31.2812 66.9627 31.3075 65.2641 31.3101 65.1437C31.3651 63.4734 31.4726 61.7903 31.782 60.1456C32.2224 57.7938 33.3917 55.7059 35.9663 55.109C37.1434 54.8348 38.1921 55.5317 38.8292 56.4795C38.8292 56.4795 39.9932 57.8834 38.6037 66.9652V66.9627Z" fill="url(#paint8_linear_3696_32998)"/></g> <g clip-path="url(#clip2_3696_32998)"> <path d="M73.3606 29.5037C73.3606 29.5037 67.0797 31.3357 67.6722 40.9847V61.7828H74.8177L73.3606 29.5037Z" fill="#1A1818"/> <path d="M72.6465 35.7394V65.6167H86.3098V40.4489C86.3098 40.4489 87.2067 30.7947 80.678 29.6082C80.678 29.6082 72.6465 28.1969 72.6465 35.7394Z" fill="#1A1818"/> <path d="M81.8041 54.8701H72.3047V59.4333H81.8041V54.8701Z" fill="#F9CDB1"/> <path d="M92.2785 66.9627H85.8549L84.7075 62.4623C84.7075 62.4623 83.773 55.6306 88.6021 55.5339C88.6021 55.5339 91.1069 57.2379 91.791 61.2496C91.791 61.2496 92.3324 64.4537 92.2812 66.9627H92.2785Z" fill="#F9CDB1"/> <path d="M69.9639 66.9626L70.8311 63.5677C70.8311 63.5677 72.3663 55.986 67.5372 55.8893C67.5372 55.8893 64.0412 58.6857 64.2944 66.9626H69.9639Z" fill="#F9CDB1"/> <path d="M80.6791 45.6549H74.8184V53.9815H80.6791V45.6549Z" fill="#F9CDB1"/> <path d="M80.6791 49.0551V45.6576H74.8184V50.5526C76.7522 50.4951 79.0469 50.1554 80.6791 49.0551Z" fill="#EAB894"/> <path d="M67.0469 56.6394C68.3612 54.6636 70.4297 54.1305 70.4297 54.1305L75.3828 52.3507H80.1096L86.1669 54.1305C86.1669 54.1305 88.2354 54.6663 89.5498 56.6394H67.0469Z" fill="#F9CDB1"/> <path d="M68.2589 63.4057V66.9626H87.2847L87.0612 62.8307C86.8241 54.87 87.7426 54.8674 87.7426 54.8674C86.8861 54.3134 86.1697 54.1278 86.1697 54.1278L81.3997 52.8106C81.3997 52.8106 80.3655 57.1255 76.4305 57.1255C76.4305 57.1255 73.3817 55.5312 74.2085 52.7688L70.4298 54.1278C70.4298 54.1278 68.9323 54.5721 67.5371 55.8867C67.5371 55.8867 68.2104 56.2865 68.2589 63.4031V63.4057Z" fill="url(#paint9_linear_3696_32998)"/> <path d="M81.3168 46.3188C81.1175 46.7683 80.8374 47.1839 80.4469 47.5498C79.4746 48.4592 78.3353 49.1962 77.0856 49.6928C75.5531 50.3044 73.2395 50.3801 72.0194 49.157C71.0309 48.1639 70.4896 46.6612 70.0613 45.3701C69.2291 42.8612 69.2399 40.1222 69.4499 37.5192C69.4984 36.926 69.5604 36.3301 69.6627 35.742C70.6189 30.063 75.6285 30.0734 75.6285 30.0734C79.4099 29.914 82.8628 31.6467 83.4446 35.5147C83.6681 36.9965 83.3745 38.5855 83.2964 40.0674C83.2399 41.1546 83.1025 42.2418 82.7443 43.2793C82.378 44.3378 81.7747 45.2943 81.3195 46.3162L81.3168 46.3188Z" fill="#F9CDB1"/> <path d="M81.8074 41.9882C81.8074 41.9882 82.3083 39.968 83.9082 40.8017C83.9082 40.8017 84.63 41.1571 84.1102 42.5449C84.1102 42.5449 83.3507 44.5416 81.8047 43.8124V41.9882H81.8074Z" fill="#F9CDB1"/> <path d="M69.0734 36.3927C68.804 28.5941 76.4289 28.9652 76.4289 28.9652C83.9245 29.2631 84.2988 32.5587 84.2988 32.5587C85.2065 36.7403 83.9083 40.8017 83.9083 40.8017C82.3139 40.4645 82.0445 42.5318 82.0445 42.5318C82.2115 39.2179 81.9314 39.0167 81.9314 39.0167C79.4993 38.9931 77.7459 38.7396 77.7459 38.7396L77.6382 36.4293L77.0511 38.6194C77.0511 38.6194 73.6036 38.5436 70.0241 37.7204C70.0241 37.7204 69.0707 37.7857 69.0707 40.6004V36.3927H69.0734Z" fill="#1A1818"/></g> <g clip-path="url(#clip3_3696_32998)"> <path d="M11.9808 51.8379C13.2196 51.3943 14.0006 50.3714 15.4737 50.4989C18.0914 50.7274 19.947 53.6286 22.4677 51.6599C25.5163 49.2742 24.7164 34.9304 23.3941 31.6812C22.2738 28.9262 20.2055 31.6201 17.2593 30.6929C13.8336 29.6169 10.8793 32.2843 9.10729 35.0526C7.26522 37.9352 6.31456 41.6068 6.58656 44.9915C6.83702 48.1158 7.41064 53.4745 11.9835 51.8353L11.9808 51.8379Z" fill="#1A1818"/> <path d="M0.284705 67H6.70771L7.85497 62.4251C7.85497 62.4251 8.78947 55.4804 3.96077 55.3821C3.96077 55.3821 1.4562 57.1143 0.772152 61.1924C0.772152 61.1924 0.230843 64.4496 0.282011 67H0.284705Z" fill="url(#paint10_linear_3696_32998)"/> <path d="M22.5966 67L21.7294 63.5489C21.7294 63.5489 20.7949 56.6042 25.6236 56.5059C25.6236 56.5059 28.5187 58.5861 28.2656 67H22.5966Z" fill="url(#paint11_linear_3696_32998)"/> <path d="M11.884 45.3395H17.7441V53.8039H11.884V45.3395Z" fill="#F9CDB1"/> <path d="M11.884 48.7959V45.3422H17.7441V50.3183C15.8105 50.2598 13.516 49.9144 11.884 48.7959Z" fill="#EAB894"/> <path d="M25.5137 56.5058C24.1994 54.4973 22.1312 53.9554 22.1312 53.9554L17.1786 52.1461H12.4522L6.39546 53.9554C6.39546 53.9554 4.32717 54.5 3.01295 56.5058H25.5137Z" fill="#F9CDB1"/> <path d="M24.3015 67H4.78469L4.90857 63.3842L2.03774 59.1015C2.676 54.9304 6.39246 53.9554 6.39246 53.9554L11.1619 52.6164C11.1619 52.6164 10.8764 54.1493 14.8137 54.1493C14.8137 54.1493 17.9754 54.1493 18.3524 52.5765L22.1308 53.958C22.1308 53.958 24.8993 54.7923 26.4102 57.5526L24.2988 63.3868V67.0027L24.3015 67Z" fill="url(#paint12_linear_3696_32998)"/> <path d="M11.2449 46.0143C11.4442 46.4713 11.7243 46.8937 12.1148 47.2656C13.087 48.1902 14.2261 48.9394 15.4757 49.4442C17.0081 50.0659 19.3214 50.1429 20.5414 48.8995C21.5298 47.89 22.0711 46.3623 22.4993 45.0499C23.3315 42.4994 23.3207 39.7152 23.1106 37.069C23.0621 36.466 23.0002 35.8602 22.8979 35.2625C21.9445 29.4893 16.9354 29.5 16.9354 29.5C13.1543 29.3379 9.70175 31.0993 9.12004 35.0313C8.89652 36.5377 9.19007 38.153 9.26816 39.6594C9.32472 40.7646 9.46207 41.8698 9.82025 42.9245C10.1865 44.0005 10.7898 44.9729 11.2449 46.0117V46.0143Z" fill="#F9CDB1"/> <path d="M23.5123 40.4724C24.9261 36.1551 23.7843 31.0781 22.0957 29.4894C17.3101 24.9862 8.47139 26.6998 5.77292 33.7402C4.93806 35.9134 5.33125 44.0244 5.70828 46.5165C6.21458 49.856 7.45879 52.1435 10.028 52.1435C9.96066 49.3911 10.1734 46.3385 9.92565 42.9724C9.68058 39.6328 10.1303 39.5505 12.9473 37.9963C14.3477 37.2232 15.1637 36.4155 16.0336 35.1615C17.1108 33.6073 16.3002 33.0653 18.4331 33.1344C18.4331 33.1344 23.3884 32.967 23.5096 40.4697L23.5123 40.4724Z" fill="#1A1818"/> <path d="M10.7551 41.6121C10.7551 41.6121 10.2542 39.5585 8.65451 40.406C8.65451 40.406 7.93277 40.7673 8.45253 42.178C8.45253 42.178 9.21198 44.2078 10.7578 43.4665V41.6121H10.7551Z" fill="#F9CDB1"/></g><defs> <linearGradient id="paint0_linear_3696_32998" x1="29.3396" y1="11.8226" x2="38.6088" y2="21.0643" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint1_linear_3696_32998" x1="65.4109" y1="12.5444" x2="65.4109" y2="33.0401" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint2_linear_3696_32998" x1="87.5993" y1="3.8921" x2="95.3834" y2="11.6532" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint3_linear_3696_32998" x1="98.8454" y1="54.0554" x2="98.8454" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint4_linear_3696_32998" x1="109.959" y1="52.5999" x2="109.959" y2="66.9654" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint5_linear_3696_32998" x1="118.279" y1="54.5056" x2="118.279" y2="66.9653" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint6_linear_3696_32998" x1="54.7171" y1="54.6182" x2="54.7171" y2="66.9627" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint7_linear_3696_32998" x1="43.6038" y1="53.226" x2="43.6038" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint8_linear_3696_32998" x1="35.2835" y1="55.0487" x2="35.2835" y2="66.9652" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint9_linear_3696_32998" x1="77.6398" y1="52.7688" x2="77.6398" y2="66.9626" gradientUnits="userSpaceOnUse"> <stop stop-color="#FFC30E"/> <stop offset="1" stop-color="#C08F00"/></linearGradient> <linearGradient id="paint10_linear_3696_32998" x1="4.10317" y1="55.3821" x2="4.10317" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint11_linear_3696_32998" x1="24.969" y1="56.5059" x2="24.969" y2="67" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <linearGradient id="paint12_linear_3696_32998" x1="14.2239" y1="52.5765" x2="14.2239" y2="67.0027" gradientUnits="userSpaceOnUse"> <stop stop-color="#B8C3B0"/> <stop offset="1" stop-color="#6A7165"/></linearGradient> <clipPath id="clip0_3696_32998"> <rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 122.281 26.9626)" fill="white"/></clipPath> <clipPath id="clip1_3696_32998"> <rect x="31.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath> <clipPath id="clip2_3696_32998"> <rect x="64.2812" y="28.9626" width="28" height="38" rx="14" fill="white"/></clipPath> <clipPath id="clip3_3696_32998"> <rect width="28" height="40" rx="14" transform="matrix(-1 0 0 1 28.2812 27)" fill="white"/></clipPath></defs></svg>')),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    Text(
                                      mrJoin.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .titleLarge
                                            ?.fontWeight,
                                      ),
                                    ),
                                    SizedBox(
                                      height: 10.h,
                                    ),
                                    Text(
                                      mrJoinDes.tr,
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(0.5),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(
                                      height: 20.h,
                                    ),
                                    Padding(
                                      padding: EdgeInsets.only(
                                          left: 24.w, right: 24.w),
                                      child: InkWell(
                                        onTap: () {
                                          Get.to(
                                                  () => const MrRegister());
                                        },
                                        child: Container(
                                          height: 48.h,
                                          width: double.infinity,
                                          decoration: BoxDecoration(
                                              color: configTheme()
                                                  .colorScheme
                                                  .onPrimary
                                                  .withOpacity(0.05),
                                              borderRadius:
                                              BorderRadius.circular(
                                                  16)),
                                          child: Center(
                                            child: Text(
                                              mrSign.tr,
                                              style: TextStyle(
                                                color: appConfigService
                                                    .countryConfigCollection ==
                                                    "aam"
                                                    ? Color(0xFF792AFF)
                                                    .withOpacity(0.75)
                                                    : appConfigService
                                                    .countryConfigCollection ==
                                                    "rafco"
                                                    ? configTheme()
                                                    .colorScheme
                                                    .onPrimary
                                                    : Color(
                                                    0xFF6A7165),
                                                fontSize: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontSize,
                                                fontFamily: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontFamily,
                                                fontWeight: configTheme()
                                                    .primaryTextTheme
                                                    .titleMedium
                                                    ?.fontWeight,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ),
                  ),
                )

                /// เป็น MR
                    : Container(
                  height: Get.height,
                  width: Get.width,
                  color: Colors.white,
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 24.w, right: 24.w, top: 104.h),
                    child: SingleChildScrollView(
                      child: Column(
                        // crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          buildHead(true
                            // mrController.mrData.value.mr_id.toString(),
                            // mrController.mrData.value.mr_fname
                            //         .toString() +
                            //     mrController.mrData.value.mr_lname
                            //         .toString(),
                            // mrController.mrData.value.mr_rank.toString()
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Row(
                            mainAxisAlignment:
                            MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              InkWell(
                                onTap: () {
                                  mrController.getReferFriend();
                                  mrController.searchController.clear();
                                  Get.to(() => const MrHistoryLoan());
                                },
                                child: buildContent(
                                  mrLoan.tr,
                                  205.h,
                                  appConfigService.countryConfigCollection
                                      .toString() ==
                                      "aam"
                                      ? '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/> <path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                      : appConfigService
                                      .countryConfigCollection
                                      .toString() ==
                                      "rafco"
                                      ? '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#EA1B23" fill-opacity="0.5"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>'
                                      : '<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#6A7165" fill-opacity="0.75"/><path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/><path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/><path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>',
                                  '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',
                                  mrWait.tr,
                                  mrSuccess.tr,
                                  mrController.close?.value.toString(),
                                  mrController.pending?.value.toString(),
                                  // mrController.mrReferral[0].transection_pending.toString(),
                                  // mrController.mrReferral[0].transection_close.toString(),
                                  mrController.mrReferral.length
                                      .toString(),
                                  // MrHistoryLoan()
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  Get.to(() => const MrHistoryLoadApp());
                                },
                                child: buildContent(
                                  mrApp.tr,
                                  205.h,
                                  appConfigService.countryConfigCollection
                                      .toString() ==
                                      "aam"
                                      ? '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="6" y="3" width="12" height="18" rx="2" fill="#792AFF" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/> <path d="M9 3V3C9 3.46499 9 3.69748 9.05111 3.88823C9.18981 4.40587 9.59413 4.81019 10.1118 4.94889C10.3025 5 10.535 5 11 5H13C13.465 5 13.6975 5 13.8882 4.94889C14.4059 4.81019 14.8102 4.40587 14.9489 3.88823C15 3.69748 15 3.46499 15 3V3" stroke="#1A1818" stroke-width="1.2"/> <circle cx="12" cy="18" r="1" fill="white"/></svg>'
                                      : appConfigService
                                      .countryConfigCollection
                                      .toString() ==
                                      "rafco"
                                      ? '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#EA1B23" fill-opacity="0.5" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>'
                                      : '<svg width="14" height="20" viewBox="0 0 14 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="1" width="12" height="18" rx="2" fill="#6A7165" fill-opacity="0.75" stroke="#1A1818" stroke-width="1.2"/><path d="M4 1V1C4 1.46499 4 1.69748 4.05111 1.88823C4.18981 2.40587 4.59413 2.81019 5.11177 2.94889C5.30252 3 5.53501 3 6 3H8C8.46499 3 8.69748 3 8.88823 2.94889C9.40587 2.81019 9.81019 2.40587 9.94889 1.88823C10 1.69748 10 1.46499 10 1V1" stroke="#1A1818" stroke-width="1.2"/><circle cx="7" cy="16" r="1" fill="white"/></svg>',
                                  '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',
                                  "",
                                  "",
                                  "",
                                  "",
                                  mrController.referral_download.length.toString() ?? "0",
                                  // MrHistoryLoan()
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: 16.h),
                          InkWell(
                            onTap: () {
                              Get.to(() =>
                                  RewardHistory(
                                      point: mrController
                                          .totalPointReferal!.value
                                          .toString()));
                            },
                            child: buildPoint(mrController
                                .totalPointReferal?.value
                                .toString()),
                          ),
                          SizedBox(height: 20.h),
                          buildMenu(
                            mrRecommend.tr,
                            mrReferLoan.tr,
                            '${mrPointReceive.tr} ${webViewPointCtl.namePoint
                                .value}',
                          ),
                          // buildMenu(mrRecommend.tr,mrReferLoan.tr,mrPointReceive.tr, '<svg width="120" height="79" viewBox="0 0 120 79" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3430_36568)"><path opacity="0.15" d="M60.4642 79.5C82.3185 79.5 100.035 61.8152 100.035 40C100.035 18.1848 82.3185 0.5 60.4642 0.5C38.6099 0.5 20.8936 18.1848 20.8936 40C20.8936 61.8152 38.6099 79.5 60.4642 79.5Z" fill="#FF9300"/><path opacity="0.5" d="M60.4633 65.7295C74.6988 65.7295 86.239 54.21 86.239 39.9998C86.239 25.7897 74.6988 14.2701 60.4633 14.2701C46.2277 14.2701 34.6875 25.7897 34.6875 39.9998C34.6875 54.21 46.2277 65.7295 60.4633 65.7295Z" fill="url(#paint0_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M27.6287 7.4359C27.4981 7.78338 27.4255 8.16498 27.3705 8.61276C27.3436 8.83717 27.3177 9.14845 27.2886 9.5135C27.1694 11.0316 26.9683 13.5384 26.2218 14.1992C25.9501 14.4412 25.5178 14.3244 24.8418 13.5663C24.0912 12.7245 23.4546 11.5528 22.79 10.3346C22.2581 9.35631 21.7096 8.34802 21.0647 7.43694H20.3203V6.70166H28.8324V7.43694H27.6287V7.4359Z" fill="#792AFF" fill-opacity="0.1"/><path d="M27.6287 7.39046L27.3947 7.34793C27.2538 7.72259 27.1783 8.12684 27.1224 8.58231L27.1223 8.58295C27.0947 8.81329 27.0684 9.12981 27.0394 9.49369L27.0394 9.49393C26.9796 10.2549 26.9003 11.2492 26.7442 12.1344C26.6662 12.5771 26.5703 12.9846 26.4512 13.3176C26.3298 13.6573 26.1947 13.8893 26.0561 14.012L26.0555 14.0126C26.0214 14.0429 25.9895 14.0567 25.9554 14.061C25.919 14.0656 25.8625 14.0613 25.779 14.0268C25.605 13.9548 25.3583 13.7699 25.0284 13.3999C24.3035 12.5871 23.6829 11.4495 23.0167 10.2282L23.0096 10.2152L23.0094 10.2149L23.009 10.2141C22.4786 9.23845 21.9234 8.21734 21.2688 7.2925L21.1941 7.18694H21.0647H20.5703V6.95166H28.5824V7.18694H27.6287V7.39046Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M32.5251 6.6139C32.6889 6.73076 32.9201 6.69249 33.0383 6.52599C33.1555 6.36053 33.1182 6.12992 32.9523 6.01306C32.4505 5.65628 31.7465 5.49495 31.0466 5.50012C30.3157 5.50633 29.5713 5.69557 29.055 6.03064C28.4982 6.39569 28.1509 6.76591 27.9197 7.20646C27.6926 7.63873 27.59 8.11444 27.5174 8.70701C27.4904 8.93142 27.4645 9.24269 27.4355 9.60775C27.3162 11.1259 27.1151 13.6326 26.3686 14.2935C26.097 14.5355 25.6646 14.4186 24.9886 13.6606C24.238 12.8188 23.6014 11.6471 22.9368 10.4289C22.2805 9.22201 21.6004 7.97069 20.744 6.90863C20.6154 6.7504 20.3842 6.72558 20.2245 6.85175C20.0659 6.97998 20.041 7.21267 20.1675 7.36986C20.9824 8.38332 21.647 9.60361 22.2878 10.7805C22.9731 12.039 23.6315 13.2479 24.435 14.1508C25.4707 15.31 26.2566 15.3804 26.8601 14.8447C27.8295 13.9863 28.0441 11.2955 28.1726 9.66566C28.1996 9.31302 28.2255 9.01001 28.2514 8.79801C28.3157 8.28197 28.3976 7.88072 28.5718 7.5467C28.7439 7.22301 29.0135 6.94069 29.4583 6.64802C29.8605 6.38845 30.4546 6.24057 31.0508 6.23643C31.6076 6.2323 32.1508 6.34916 32.522 6.61286L32.5251 6.6139Z" fill="#1A1818"/><path d="M32.6397 6.38853L32.6703 6.41038C32.7212 6.44665 32.7961 6.43538 32.8345 6.38128L33.0383 6.52598L32.8343 6.38153C32.8732 6.3266 32.8588 6.25301 32.8083 6.21744L32.8074 6.21681C32.3622 5.90028 31.7157 5.7452 31.0486 5.75011C30.3528 5.75605 29.6584 5.93726 29.1916 6.24C28.6623 6.58714 28.3483 6.92779 28.141 7.32264L28.141 7.32272C27.9351 7.71463 27.837 8.15442 27.7656 8.73703C27.7393 8.9555 27.7138 9.26146 27.6847 9.62757C27.6252 10.3847 27.5444 11.4028 27.3835 12.3155C27.303 12.7719 27.2012 13.2099 27.0689 13.5802C26.9389 13.9438 26.7689 14.273 26.5343 14.4807L26.3686 14.2935M32.6397 6.38853L31.0489 5.98644C30.42 5.99082 29.7756 6.14573 29.3227 6.43796L29.3227 6.43795L29.3209 6.43918C28.8521 6.74759 28.5482 7.0585 28.3511 7.42932L28.3501 7.4311C28.154 7.80727 28.0682 8.24658 28.0034 8.76711L28.0033 8.76767C27.9766 8.98592 27.9503 9.29403 27.9234 9.64642C27.8589 10.4634 27.774 11.5294 27.5862 12.4968C27.4924 12.9803 27.3743 13.4315 27.2237 13.8101C27.0719 14.192 26.8948 14.48 26.6943 14.6575L26.6941 14.6577C26.4429 14.8807 26.1769 14.9637 25.8675 14.8901C25.5387 14.8118 25.1253 14.5481 24.6217 13.9845C23.8411 13.1074 23.1965 11.9265 22.5073 10.6609L22.503 10.6529C21.8658 9.48255 21.1919 8.2449 20.3623 7.2132L20.3623 7.21313C20.3231 7.1644 20.3295 7.08937 20.3806 7.04704C20.4335 7.00621 20.5083 7.01507 20.55 7.06628L20.744 6.90863M32.6397 6.38853L32.6349 6.38693M32.6397 6.38853L32.6349 6.38693M26.3686 14.2935L26.5349 14.4801C26.4332 14.5708 26.3091 14.6331 26.1646 14.6513C26.0225 14.6692 25.8778 14.6422 25.7347 14.583C25.4556 14.4676 25.1481 14.215 24.8021 13.827L24.802 13.827C24.0387 12.9709 23.3952 11.7912 22.7442 10.5978L22.7173 10.5486L22.7172 10.5483C22.0588 9.33755 21.3891 8.10692 20.5494 7.06555L20.744 6.90863M26.3686 14.2935C26.097 14.5355 25.6646 14.4186 24.9886 13.6606C24.2446 12.8262 23.6126 11.6677 22.9543 10.461L22.9368 10.4289L22.9345 10.4245C22.2789 9.21903 21.5994 7.96943 20.744 6.90863M26.3686 14.2935C27.1151 13.6326 27.3162 11.1259 27.4355 9.60775L20.2245 6.85175C20.3842 6.72558 20.6154 6.7504 20.744 6.90863M32.6349 6.38693C32.2067 6.09726 31.619 5.98222 31.0491 5.98644L32.6349 6.38693Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M28.1742 9.66591C28.2011 9.31326 28.2271 9.01026 28.253 8.79826C28.3173 8.28222 28.3992 7.88097 28.5734 7.54694C28.6304 7.44146 28.6967 7.34011 28.7745 7.24083C28.9404 7.03814 29.1612 6.84476 29.4609 6.64827C29.8631 6.3887 30.4572 6.24081 31.0534 6.23668C31.6101 6.23254 32.1534 6.3494 32.5246 6.61311C32.5401 6.62448 32.5598 6.63689 32.5775 6.64413L32.6169 6.66585L32.7734 6.75789C33.7978 7.20567 34.6708 7.95026 35.2783 8.87582C35.8662 9.77036 36.2104 10.8428 36.2104 11.9927V14.7384C33.4432 14.7808 30.2602 14.8697 26.6553 15C26.7278 14.9556 26.7963 14.9028 26.8626 14.8439C27.832 13.9855 28.0467 11.2947 28.1752 9.66487L28.1742 9.66591Z" fill="url(#paint1_linear_3430_36568)"/><path d="M26.9906 14.988C27.0199 14.9869 27.0492 14.9859 27.0784 14.9849C27.1584 14.9078 27.2325 14.8212 27.3014 14.7268M26.9906 14.988L27.3014 14.7268M26.9906 14.988L26.8626 14.8439L26.9902 14.988L26.9906 14.988ZM27.3014 14.7268C30.5344 14.6129 33.4192 14.5334 35.9604 14.4923V11.9927C35.9604 10.8933 35.6314 9.86835 35.0694 9.01312L35.0693 9.01301C34.488 8.1274 33.6526 7.41505 32.6733 6.98696L32.6596 6.98097L32.6467 6.9734L32.4931 6.88312L32.4662 6.86826C32.43 6.85148 32.3989 6.83084 32.3786 6.81609C32.0669 6.5952 31.5839 6.48274 31.0552 6.48667L31.0551 6.48667C30.4922 6.49058 29.9488 6.63124 29.5971 6.85785C29.3147 7.04317 29.1154 7.21955 28.9696 7.39711C28.9015 7.48433 28.8438 7.57267 28.7942 7.66418C28.6425 7.95574 28.5647 8.31843 28.5011 8.82887C28.4872 8.94238 28.4732 9.08391 28.4589 9.24723L28.4244 9.68453C28.3603 10.4973 28.2737 11.5915 28.0797 12.5913C27.9826 13.0915 27.8573 13.5759 27.6909 13.9941C27.5836 14.2641 27.4558 14.5151 27.3014 14.7268Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M39.793 75.3084C39.9337 75.0047 40.297 74.7509 40.3547 74.3565C40.3841 74.161 40.4125 73.8864 40.4419 73.5669C40.5721 72.2356 40.7884 70.0382 41.5853 69.4582C41.8782 69.2469 42.6951 69.2169 43.4196 69.8817C44.6029 70.4242 127.868 71.0058 91.5717 72.848C92.1302 73.6859 94.7257 73.5378 95.3956 74.3224C95.4114 74.3399 96.1831 74.5845 96.1978 74.6028C94.7919 75.8883 94.8213 75.9549 92.4484 76.6372L37.2448 76.5415L36.4814 76.5082L39.0895 75.4956L39.792 75.3092L39.793 75.3084Z" fill="url(#paint2_linear_3430_36568)"/><path d="M39.9949 75.4674L39.9483 75.5043L39.9472 75.5051L39.9064 75.5375L39.8561 75.5508L39.1671 75.7337L37.7282 76.2923L92.4133 76.3871C93.5836 76.0502 94.1325 75.8715 94.5628 75.6285C94.9286 75.4219 95.212 75.1677 95.7213 74.7005C95.7202 74.7002 95.7191 74.6998 95.718 74.6994L95.7037 74.6947C95.6105 74.6636 95.5167 74.6323 95.4444 74.6075C95.4065 74.5945 95.3727 74.5826 95.3471 74.5731C95.3392 74.5702 95.3311 74.5671 95.3232 74.564C95.3186 74.5622 95.314 74.5603 95.3097 74.5585C95.3043 74.5562 95.2956 74.5524 95.2861 74.5476C95.2815 74.5453 95.2735 74.5411 95.2641 74.5352L95.2637 74.5349C95.258 74.5314 95.2337 74.5162 95.2099 74.4898L95.2054 74.4848L95.2055 74.4847C95.0862 74.345 94.8569 74.2242 94.5187 74.1172C94.2045 74.0178 93.8372 73.942 93.4538 73.8629L93.3913 73.85C92.9931 73.7677 92.5788 73.6801 92.2281 73.5575C91.8852 73.4376 91.5508 73.2674 91.3637 72.9867L91.1196 72.6206L91.559 72.5983C96.0071 72.3726 98.6451 72.1663 99.8585 71.9787C99.7494 71.9631 99.6289 71.9475 99.4973 71.9319C96.791 71.6115 89.7627 71.3493 81.5436 71.1274C75.2524 70.9576 68.2742 70.8115 62.024 70.6808C60.1151 70.6408 58.2741 70.6023 56.5413 70.565C52.8387 70.4852 49.6298 70.4107 47.3093 70.3393C46.1494 70.3036 45.2099 70.2685 44.5412 70.2339C44.2071 70.2166 43.9384 70.1992 43.7426 70.1818C43.645 70.173 43.5626 70.164 43.498 70.1545C43.4458 70.1469 43.3716 70.1348 43.3154 70.109L43.2796 70.0926L43.2505 70.0659C42.925 69.7672 42.5857 69.6305 42.3039 69.5887C42.0109 69.5452 41.8075 69.6065 41.7319 69.6607C41.5853 69.7676 41.4448 69.9663 41.3179 70.2575C41.1927 70.5448 41.0911 70.8977 41.0079 71.2835C40.8416 72.0551 40.7561 72.9226 40.6908 73.5905C40.6907 73.5907 40.6907 73.591 40.6907 73.5912L40.4419 73.5669C40.4125 73.8864 40.3841 74.161 40.3547 74.3565L39.9949 75.4674ZM39.9949 75.4674L40.0198 75.4135C40.0605 75.3258 40.1225 75.2477 40.2066 75.1418C40.2362 75.1047 40.2684 75.0642 40.3033 75.0186C40.4238 74.8617 40.5631 74.6571 40.602 74.3937L39.9949 75.4674Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M34.4091 65.1283H88.8409V9.83698C88.8409 7.75972 86.706 6.06165 84.0989 6.06165H30.7949V6.4638C32.9185 7.20655 34.4091 8.89807 34.4091 10.8555V65.1291V65.1283Z" fill="white"/><path d="M88.8409 65.3783C88.979 65.3783 89.0909 65.2664 89.0909 65.1283V9.83698C89.0909 7.57144 86.7878 5.81165 84.0989 5.81165H30.7949C30.6569 5.81165 30.5449 5.92357 30.5449 6.06165V6.4638C30.5449 6.57005 30.6121 6.6647 30.7124 6.69978C32.7687 7.419 34.1591 9.03551 34.1591 10.8555V65.1283V65.1291C34.1591 65.2672 34.2711 65.3791 34.4091 65.3791C34.416 65.3791 34.4228 65.3789 34.4295 65.3783H88.8409Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.8414 65.1291H34.1768V73.4919C34.1768 74.4669 34.6807 75.3556 35.4938 75.9998C35.7924 76.2361 36.1298 76.4359 36.5007 76.5984C36.6076 76.5752 36.7259 76.5876 36.8244 76.6423C37.1974 76.8521 37.7465 76.9466 38.3091 76.9425C38.9116 76.94 39.514 76.8222 39.9185 76.6125C40.3669 76.3787 40.6435 76.1532 40.8154 75.8937C40.9914 75.6267 41.0742 75.3051 41.1391 74.8905L41.2177 74.1933C41.3476 72.8875 41.5676 70.7294 42.5431 70.0421C42.9182 69.781 43.3562 69.6997 43.8821 69.8863H88.8414V65.1299V65.1291Z" fill="white"/><path d="M36.5007 76.5984C36.6076 76.5752 36.7259 76.5876 36.8244 76.6423C37.1974 76.8521 37.7465 76.9466 38.3091 76.9425C38.9116 76.94 39.514 76.8222 39.9185 76.6125M36.5007 76.5984L36.601 76.3694C36.5793 76.3599 36.5577 76.3502 36.5362 76.3405M36.5007 76.5984C36.1298 76.4359 35.7924 76.2361 35.4938 75.9998M36.5007 76.5984L36.4476 76.3541C36.4765 76.3478 36.5061 76.3432 36.5362 76.3405M36.5362 76.3405C36.6719 76.3279 36.8169 76.3521 36.9459 76.4238L36.947 76.4244C37.2658 76.6037 37.7648 76.6965 38.3073 76.6925L38.3081 76.6925C38.8861 76.6901 39.4453 76.5761 39.8029 76.3908M36.5362 76.3405C36.209 76.1914 35.9122 76.0121 35.649 75.8039M39.8029 76.3908L39.9185 76.6125M39.8029 76.3908C39.8031 76.3907 39.8032 76.3906 39.8034 76.3906L39.9185 76.6125M39.8029 76.3908C40.2325 76.1668 40.4678 75.9657 40.6068 75.7558M39.9185 76.6125C40.3669 76.3787 40.6435 76.1532 40.8154 75.8937M40.8154 75.8937L40.6066 75.7561C40.6067 75.756 40.6067 75.7559 40.6068 75.7558M40.8154 75.8937C40.9914 75.6267 41.0742 75.3051 41.1391 74.8905M40.8154 75.8937L40.6069 75.7557C40.6069 75.7557 40.6068 75.7558 40.6068 75.7558M40.6068 75.7558C40.7512 75.5367 40.8277 75.2613 40.8913 74.8571M41.1391 74.8905L40.8907 74.8625L40.8913 74.8571M41.1391 74.8905L41.2177 74.1933M41.1391 74.8905L40.8921 74.8518C40.8918 74.8536 40.8916 74.8553 40.8913 74.8571M40.8913 74.8571L40.9691 74.1669M41.2177 74.1933L40.9689 74.1685C40.969 74.168 40.969 74.1674 40.9691 74.1669M41.2177 74.1933C41.3476 72.8875 41.5676 70.7294 42.5431 70.0421C42.9182 69.781 43.3562 69.6997 43.8821 69.8863M41.2177 74.1933L40.9693 74.1653L40.9691 74.1669M40.9691 74.1669C41.0338 73.5165 41.1226 72.6341 41.3205 71.8263C41.5146 71.0337 41.83 70.2388 42.3991 69.8378L42.4003 69.837C42.8316 69.5367 43.3399 69.4417 43.9239 69.6363M43.9239 69.6363H43.8821V69.8863M43.9239 69.6363C43.9378 69.6409 43.9517 69.6457 43.9657 69.6507L43.8821 69.8863M43.9239 69.6363H88.5914M43.8821 69.8863H88.5914V69.6363M88.5914 69.6363V65.3791M88.5914 69.6363H88.8414M88.8414 69.6363V65.3791H88.5914M88.8414 69.6363L35.649 75.8039M88.8414 69.6363L35.6489 75.8038M88.5914 65.3791V65.1299V65.1291H34.4268V65.3791M88.5914 65.3791H34.4268M34.4268 65.3791H34.1768V73.4919C34.1768 74.4669 34.6807 75.3556 35.4938 75.9998M34.4268 65.3791V73.4919C34.4268 74.3757 34.8832 75.197 35.6489 75.8038M35.4938 75.9998L35.6489 75.8038M35.4938 75.9998L35.649 75.8039M35.649 75.8039L35.6489 75.8038" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M36.4481 76.0387C36.283 75.9418 36.0517 75.9738 35.934 76.1086C35.8174 76.2434 35.8546 76.4321 36.0197 76.5273C36.5214 76.8162 37.2223 76.9484 37.916 76.9434C38.6459 76.9383 39.385 76.7867 39.8991 76.5112C40.4534 76.2156 40.8003 75.914 41.0294 75.5551C41.2555 75.203 41.3587 74.8138 41.43 74.3311C41.4568 74.15 41.4826 73.8965 41.5094 73.5974C41.6302 72.3608 41.8305 70.3188 42.5737 69.7805C42.8442 69.5851 43.2736 69.6803 43.9457 70.296C44.6951 70.9834 45.3289 71.9362 45.9906 72.9285C46.6441 73.9125 47.3212 74.9326 48.1729 75.7952C48.3019 75.9258 48.5332 75.9468 48.6911 75.8415C48.849 75.7388 48.8738 75.5492 48.7468 75.4195C47.9355 74.5956 47.2727 73.5999 46.6337 72.6421C45.9524 71.6161 45.2969 70.6313 44.4979 69.8967C43.4667 68.9507 42.6842 68.8935 42.0834 69.3298C41.1203 70.029 40.9045 72.2209 40.7755 73.5477C40.7486 73.8366 40.7228 74.0826 40.6981 74.2561C40.6341 74.6773 40.5515 75.0042 40.3791 75.2754C40.2098 75.5391 39.9393 75.7699 39.4954 76.0041C39.0959 76.2181 38.5034 76.3377 37.9098 76.3402C37.3576 76.3453 36.8146 76.2484 36.4471 76.0353L36.4481 76.0387Z" fill="#792AFF" fill-opacity="0.75"/><path d="M36.8737 76.5781L36.3216 76.2543C36.2507 76.2127 36.1546 76.2367 36.1227 76.2726C36.1174 76.2789 36.1164 76.2826 36.1163 76.2829L36.1163 76.2829C36.1162 76.2831 36.1162 76.2831 36.1162 76.2831C36.1162 76.2832 36.1162 76.2831 36.1162 76.2832C36.1163 76.2834 36.1168 76.2861 36.1202 76.2906L36.8737 76.5781ZM36.8737 76.5781L36.8384 76.462L36.8737 76.5781ZM48.7468 75.4195L48.5682 75.5944C48.5756 75.6019 48.5774 75.6068 48.5777 75.6077C48.5781 75.6087 48.5779 75.6085 48.578 75.608C48.578 75.6076 48.578 75.6093 48.5755 75.613C48.573 75.6168 48.5672 75.6239 48.5548 75.632L48.5547 75.632L48.5524 75.6335C48.4898 75.6753 48.392 75.6612 48.3507 75.6195C47.5189 74.777 46.8546 73.7776 46.1989 72.7902L46.1986 72.7898L46.1781 72.7591C45.5281 71.7842 44.8827 70.8163 44.1146 70.1118L44.1145 70.1117C43.7678 69.794 43.4623 69.5898 43.1894 69.4973C42.9058 69.4012 42.6423 69.4225 42.4273 69.5779L42.4271 69.578C42.1887 69.7507 42.0144 70.0265 41.8818 70.33C41.7474 70.6376 41.6446 70.9997 41.5638 71.3743C41.4023 72.1225 41.3209 72.9559 41.2608 73.5716L41.2606 73.5731L41.2606 73.5731L41.2604 73.5751C41.2336 73.8742 41.2083 74.1213 41.1827 74.2945L41.1826 74.2946C41.1134 74.7635 41.017 75.1118 40.8191 75.4201L40.8187 75.4206C40.6197 75.7323 40.3113 76.008 39.7814 76.2907L39.781 76.2909C39.3134 76.5415 38.6176 76.6885 37.9143 76.6934L37.9142 76.6934C37.2463 76.6983 36.5943 76.5696 36.1446 76.3107L37.9098 76.3402L37.9109 76.5902C37.9111 76.5902 37.9113 76.5902 37.9115 76.5902C38.5299 76.5875 39.1659 76.464 39.6128 76.2249C40.078 75.9793 40.3887 75.7232 40.5895 75.4105L40.5901 75.4095C40.7916 75.0925 40.8801 74.7224 40.9452 74.2937L40.9452 74.2937L40.9456 74.2915C40.9713 74.1115 40.9975 73.8604 41.0244 73.5713C41.0891 72.9055 41.1741 72.043 41.3599 71.2612C41.4527 70.8708 41.5688 70.5088 41.7157 70.2065C41.8633 69.9027 42.0348 69.674 42.2303 69.5321L42.2303 69.5321C42.479 69.3514 42.7551 69.2766 43.0813 69.3403C43.4185 69.4061 43.8314 69.6246 44.3288 70.0809C45.1013 70.7911 45.7402 71.7484 46.4255 72.7804L46.4258 72.7809L46.4433 72.8071C47.0745 73.7533 47.7453 74.7589 48.5687 75.5949L48.7468 75.4195ZM48.7468 75.4195C48.8738 75.5492 48.849 75.7388 48.6911 75.8415C48.5332 75.9468 48.3019 75.9258 48.1729 75.7952C47.3222 74.9336 46.6456 73.9147 45.9928 72.9318L45.9906 72.9285L45.9807 72.9136C45.3226 71.9267 44.6913 70.98 43.9457 70.296C43.2736 69.6803 42.8442 69.5851 42.5737 69.7805M48.7468 75.4195C47.9402 74.6005 47.2805 73.6116 46.645 72.659L46.6337 72.6421C45.9524 71.6161 45.2969 70.6313 44.4979 69.8967C43.4667 68.9507 42.6842 68.8935 42.0834 69.3298C41.1203 70.029 40.9045 72.2209 40.7755 73.5477C40.7486 73.8366 40.7228 74.0826 40.6981 74.2561C40.6341 74.6773 40.5515 75.0042 40.3791 75.2754C40.2098 75.5391 39.9393 75.7699 39.4954 76.0041L42.5737 69.7805M42.5737 69.7805C41.8305 70.3188 41.6302 72.3608 41.5094 73.5974C41.4826 73.8965 41.4568 74.15 41.43 74.3311L42.5737 69.7805Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.4611 69.0679H43.7559C44.0903 69.1903 44.4591 69.4242 44.8634 69.7949C45.6708 70.532 46.3324 71.5182 47.0211 72.5466C47.6629 73.5066 48.3329 74.5038 49.1518 75.3304C49.2789 75.4604 49.2539 75.6504 49.0955 75.7534C49.0382 75.7914 48.9715 75.8125 48.9028 75.8184H104.62C104.526 75.8108 104.433 75.7728 104.369 75.707C103.512 74.8415 102.824 73.8199 102.165 72.8337C101.804 72.2924 101.447 71.7622 101.077 71.2809C100.544 70.5877 99.3258 69.2038 98.3246 69.0687H88.4611V69.0679Z" fill="white" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M61.5319 69.0687H43.7559C44.0959 69.1902 44.4732 69.4248 44.8855 69.7952C45.7111 70.5317 46.3859 71.5189 47.0883 72.5457C47.7451 73.5058 48.4262 74.5031 49.2636 75.3282C49.3932 75.459 49.3677 75.6472 49.2051 75.7518C49.1478 75.7906 49.0787 75.8125 49.0075 75.8184H69.6793C67.7889 74.2314 66.1226 72.4284 63.9707 70.7216C63.1621 70.0778 62.3523 69.5336 61.5319 69.0679V69.0687Z" fill="#792AFF" fill-opacity="0.15"/><path d="M61.4672 69.3187C62.2561 69.7705 63.0357 70.2968 63.815 70.9171L63.8154 70.9174C64.8828 71.7641 65.831 72.6356 66.7523 73.4975C66.8701 73.6077 66.9875 73.7178 67.1047 73.8277C67.7355 74.4193 68.3598 75.0047 69.0039 75.5684H49.5935C49.6098 75.4141 49.5525 75.2646 49.4411 75.1522L49.4412 75.1522L49.4391 75.1502C48.6219 74.3449 47.954 73.3684 47.2947 72.4045L47.2947 72.4045L47.2811 72.3847C46.5876 71.3708 45.8993 70.3646 45.0522 69.6089C44.9336 69.5023 44.8167 69.4057 44.7017 69.3187H61.4672Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M88.7766 70.8327V10.1814C88.7766 7.93905 86.4687 6.10345 83.6507 6.10345H64.6658C87.6257 24.6104 52.4555 21.9316 52.4555 57.8524C52.4555 63.6223 51.8966 66.075 53.2787 70.8335H88.7776L88.7766 70.8327Z" fill="#FF9300" fill-opacity="0.2"/><mask id="path-21-inside-1_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7333 28.2639H83.0877C83.3552 28.2639 83.577 28.3766 83.577 28.51C83.577 28.6424 83.3562 28.7531 83.0877 28.7531H50.7333C50.4669 28.7531 50.2471 28.6434 50.2471 28.51C50.2471 28.3756 50.4658 28.2639 50.7333 28.2639Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7333 28.2639H83.0877C83.3552 28.2639 83.577 28.3766 83.577 28.51C83.577 28.6424 83.3562 28.7531 83.0877 28.7531H50.7333C50.4669 28.7531 50.2471 28.6434 50.2471 28.51C50.2471 28.3756 50.4658 28.2639 50.7333 28.2639Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7333 28.7639H83.0877V27.7639H50.7333V28.7639ZM83.0877 28.7639C83.1199 28.7639 83.1485 28.7674 83.1711 28.7723C83.1823 28.7747 83.1912 28.7773 83.1977 28.7795C83.2009 28.7806 83.2033 28.7816 83.205 28.7823C83.2067 28.783 83.2075 28.7834 83.2074 28.7834C83.2074 28.7833 83.2063 28.7828 83.2043 28.7816C83.2023 28.7804 83.1991 28.7783 83.195 28.7753C83.1872 28.7695 83.1734 28.7581 83.1575 28.7395C83.1255 28.7019 83.077 28.6232 83.077 28.51H84.077C84.077 28.1497 83.7949 27.9597 83.6579 27.8906C83.4872 27.8044 83.2851 27.7639 83.0877 27.7639V28.7639ZM83.077 28.51C83.077 28.3959 83.1262 28.3163 83.159 28.2781C83.1752 28.2592 83.1894 28.2477 83.1974 28.2419C83.2016 28.2388 83.2048 28.2367 83.2069 28.2355C83.209 28.2342 83.2101 28.2336 83.2102 28.2336C83.2102 28.2336 83.2094 28.234 83.2077 28.2347C83.206 28.2354 83.2036 28.2363 83.2003 28.2374C83.1937 28.2397 83.1847 28.2422 83.1733 28.2447C83.1502 28.2496 83.121 28.2531 83.0877 28.2531V29.2531C83.284 29.2531 83.4854 29.2137 83.6559 29.1288C83.7923 29.0609 84.077 28.8719 84.077 28.51H83.077ZM83.0877 28.2531H50.7333V29.2531H83.0877V28.2531ZM50.7333 28.2531C50.7005 28.2531 50.6717 28.2496 50.649 28.2448C50.6379 28.2424 50.6291 28.2399 50.6228 28.2378C50.6163 28.2356 50.6135 28.2342 50.6137 28.2343C50.6138 28.2343 50.6188 28.2368 50.6272 28.243C50.6355 28.2491 50.6497 28.2608 50.666 28.2798C50.6992 28.3187 50.7471 28.3978 50.7471 28.51H49.7471C49.7471 28.8706 50.0288 29.0599 50.1669 29.1289C50.3373 29.214 50.5382 29.2531 50.7333 29.2531V28.2531ZM50.7471 28.51C50.7471 28.6199 50.7008 28.6978 50.6682 28.7366C50.6523 28.7555 50.6382 28.7673 50.6299 28.7734C50.6215 28.7797 50.6164 28.7823 50.6162 28.7824C50.6158 28.7826 50.6185 28.7813 50.6246 28.7791C50.6307 28.777 50.6393 28.7745 50.6502 28.7722C50.6724 28.7674 50.7007 28.7639 50.7333 28.7639V27.7639C50.5366 27.7639 50.3346 27.8039 50.1636 27.8907C50.0231 27.962 49.7471 28.1529 49.7471 28.51H50.7471Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-21-inside-1_3430_36568)"/><mask id="path-23-inside-2_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 30.5434H61.5784C61.8459 30.5434 62.0667 30.654 62.0667 30.7864C62.0667 30.9188 61.8459 31.0305 61.5784 31.0305H50.7335C50.466 31.0305 50.2451 30.9209 50.2451 30.7864C50.2451 30.652 50.4639 30.5434 50.7335 30.5434Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 30.5434H61.5784C61.8459 30.5434 62.0667 30.654 62.0667 30.7864C62.0667 30.9188 61.8459 31.0305 61.5784 31.0305H50.7335C50.466 31.0305 50.2451 30.9209 50.2451 30.7864C50.2451 30.652 50.4639 30.5434 50.7335 30.5434Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7335 31.0434H61.5784V30.0434H50.7335V31.0434ZM61.5784 31.0434C61.6113 31.0434 61.6403 31.0469 61.6632 31.0517C61.6745 31.0542 61.6834 31.0567 61.69 31.0589C61.6932 31.06 61.6956 31.061 61.6973 31.0617C61.699 31.0624 61.6998 31.0628 61.6997 31.0627C61.6996 31.0627 61.6985 31.0621 61.6964 31.0609C61.6944 31.0596 61.6911 31.0575 61.6869 31.0545C61.6789 31.0486 61.6648 31.0371 61.6486 31.0183C61.6159 30.9801 61.5667 30.9005 61.5667 30.7864H62.5667C62.5667 30.4247 62.2822 30.2357 62.146 30.1678C61.9757 30.0829 61.7744 30.0434 61.5784 30.0434V31.0434ZM61.5667 30.7864C61.5667 30.6725 61.6157 30.5932 61.6481 30.5554C61.6641 30.5366 61.6781 30.5252 61.686 30.5194C61.6902 30.5163 61.6934 30.5142 61.6954 30.513C61.6975 30.5118 61.6986 30.5112 61.6987 30.5112C61.6987 30.5111 61.698 30.5115 61.6963 30.5122C61.6946 30.5129 61.6922 30.5138 61.689 30.5149C61.6825 30.5172 61.6736 30.5197 61.6624 30.5221C61.6397 30.527 61.611 30.5305 61.5784 30.5305V31.5305C61.7752 31.5305 61.9767 31.4905 62.147 31.405C62.2832 31.3367 62.5667 31.1473 62.5667 30.7864H61.5667ZM61.5784 30.5305H50.7335V31.5305H61.5784V30.5305ZM50.7335 30.5305C50.7002 30.5305 50.6711 30.527 50.6481 30.5221C50.6368 30.5197 50.6279 30.5172 50.6215 30.515C50.6149 30.5128 50.612 30.5113 50.6121 30.5114C50.6121 30.5114 50.6171 30.5139 50.6255 30.5201C50.6338 30.5261 50.648 30.5378 50.6643 30.5568C50.6975 30.5959 50.7451 30.6748 50.7451 30.7864H49.7451C49.7451 31.1473 50.0268 31.337 50.1658 31.4063C50.3367 31.4915 50.5382 31.5305 50.7335 31.5305V30.5305ZM50.7451 30.7864C50.7451 30.8969 50.6984 30.9757 50.6645 31.0155C50.6481 31.0349 50.6335 31.0468 50.625 31.0531C50.6163 31.0595 50.6111 31.0621 50.6108 31.0622C50.6104 31.0624 50.6132 31.061 50.6197 31.0588C50.626 31.0567 50.6349 31.0542 50.6463 31.0518C50.6694 31.0469 50.6991 31.0434 50.7335 31.0434V30.0434C50.5383 30.0434 50.3368 30.0816 50.1656 30.1668C50.0247 30.2368 49.7451 30.4266 49.7451 30.7864H50.7451Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-23-inside-2_3430_36568)"/><mask id="path-25-inside-3_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 32.8149H54.3747C54.6443 32.8149 54.863 32.9266 54.863 33.059C54.863 33.1934 54.6443 33.3041 54.3747 33.3041H50.7335C50.466 33.3041 50.2451 33.1934 50.2451 33.059C50.2451 32.9266 50.4639 32.8149 50.7335 32.8149Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M50.7335 32.8149H54.3747C54.6443 32.8149 54.863 32.9266 54.863 33.059C54.863 33.1934 54.6443 33.3041 54.3747 33.3041H50.7335C50.466 33.3041 50.2451 33.1934 50.2451 33.059C50.2451 32.9266 50.4639 32.8149 50.7335 32.8149Z" fill="#FF9300" fill-opacity="0.5"/><path d="M50.7335 33.3149H54.3747V32.3149H50.7335V33.3149ZM54.3747 33.3149C54.4081 33.3149 54.4371 33.3184 54.4598 33.3233C54.471 33.3257 54.4798 33.3283 54.4861 33.3304C54.4925 33.3326 54.4954 33.3341 54.4952 33.334C54.4952 33.334 54.4902 33.3315 54.4819 33.3254C54.4738 33.3193 54.4597 33.3077 54.4437 33.2888C54.4111 33.2505 54.363 33.1716 54.363 33.059H55.363C55.363 32.6999 55.0827 32.5101 54.9447 32.4407C54.7739 32.3548 54.572 32.3149 54.3747 32.3149V33.3149ZM54.363 33.059C54.363 32.9489 54.4095 32.8706 54.4426 32.8314C54.4587 32.8123 54.473 32.8005 54.4813 32.7943C54.4899 32.788 54.495 32.7854 54.4952 32.7853C54.4956 32.7851 54.4928 32.7865 54.4865 32.7887C54.4802 32.7908 54.4715 32.7933 54.4603 32.7957C54.4376 32.8006 54.4084 32.8041 54.3747 32.8041V33.8041C54.5712 33.8041 54.7733 33.7648 54.9447 33.6786C55.0855 33.6077 55.363 33.4172 55.363 33.059H54.363ZM54.3747 32.8041H50.7335V33.8041H54.3747V32.8041ZM50.7335 32.8041C50.7005 32.8041 50.6717 32.8006 50.6489 32.7958C50.6377 32.7934 50.6288 32.7908 50.6224 32.7886C50.616 32.7864 50.613 32.785 50.6132 32.785C50.6132 32.785 50.6181 32.7875 50.6264 32.7936C50.6346 32.7997 50.6487 32.8113 50.6648 32.8302C50.6977 32.8689 50.7451 32.9475 50.7451 33.059H49.7451C49.7451 33.4191 50.0257 33.6091 50.1648 33.6789C50.3357 33.7646 50.5375 33.8041 50.7335 33.8041V32.8041ZM50.7451 33.059C50.7451 33.1716 50.6971 33.2505 50.6645 33.2888C50.6484 33.3077 50.6343 33.3193 50.6262 33.3254C50.6179 33.3315 50.613 33.334 50.6129 33.334C50.6127 33.3341 50.6156 33.3326 50.622 33.3304C50.6284 33.3283 50.6372 33.3257 50.6484 33.3233C50.6711 33.3184 50.7001 33.3149 50.7335 33.3149V32.3149C50.5361 32.3149 50.3342 32.3548 50.1635 32.4407C50.0254 32.5101 49.7451 32.6999 49.7451 33.059H50.7451Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-25-inside-3_3430_36568)"/><mask id="path-27-inside-4_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M56.003 32.8149H83.0873C83.3548 32.8149 83.5767 32.9266 83.5767 33.059C83.5767 33.1934 83.3559 33.3041 83.0873 33.3041H56.003C55.7355 33.3041 55.5137 33.1934 55.5137 33.059C55.5137 32.9266 55.7345 32.8149 56.003 32.8149Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M56.003 32.8149H83.0873C83.3548 32.8149 83.5767 32.9266 83.5767 33.059C83.5767 33.1934 83.3559 33.3041 83.0873 33.3041H56.003C55.7355 33.3041 55.5137 33.1934 55.5137 33.059C55.5137 32.9266 55.7345 32.8149 56.003 32.8149Z" fill="#FF9300" fill-opacity="0.5"/><path d="M56.003 33.3149H83.0873V32.3149H56.003V33.3149ZM83.0873 33.3149C83.1199 33.3149 83.1487 33.3184 83.1717 33.3233C83.183 33.3258 83.192 33.3283 83.1986 33.3306C83.2019 33.3317 83.2043 33.3327 83.2061 33.3334C83.2078 33.3341 83.2087 33.3345 83.2086 33.3345C83.2086 33.3345 83.2076 33.334 83.2056 33.3328C83.2036 33.3316 83.2005 33.3296 83.1963 33.3265C83.1885 33.3208 83.1746 33.3094 83.1585 33.2908C83.1262 33.2532 83.0767 33.1736 83.0767 33.059H84.0767C84.0767 32.697 83.7915 32.5079 83.656 32.4401C83.4857 32.3549 83.2841 32.3149 83.0873 32.3149V33.3149ZM83.0767 33.059C83.0767 32.9475 83.1241 32.8688 83.1571 32.8301C83.1732 32.8111 83.1874 32.7995 83.1955 32.7935C83.1998 32.7903 83.2031 32.7882 83.2053 32.7869C83.2074 32.7856 83.2086 32.785 83.2088 32.7849C83.2089 32.7848 83.2059 32.7863 83.1994 32.7885C83.1929 32.7907 83.1841 32.7933 83.1727 32.7957C83.1498 32.8006 83.1207 32.8041 83.0873 32.8041V33.8041C83.2836 33.8041 83.4855 33.7647 83.6566 33.679C83.7959 33.6093 84.0767 33.4192 84.0767 33.059H83.0767ZM83.0873 32.8041H56.003V33.8041H83.0873V32.8041ZM56.003 32.8041C55.9701 32.8041 55.9411 32.8006 55.9181 32.7957C55.9068 32.7933 55.8979 32.7907 55.8914 32.7885C55.8848 32.7863 55.8817 32.7848 55.8817 32.7848C55.8818 32.7848 55.883 32.7854 55.885 32.7867C55.8871 32.7879 55.8904 32.79 55.8946 32.7931C55.9027 32.7991 55.9168 32.8106 55.9329 32.8295C55.9657 32.868 56.0137 32.9468 56.0137 33.059H55.0137C55.0137 33.4201 55.296 33.6099 55.4344 33.6791C55.6053 33.7646 55.8071 33.8041 56.003 33.8041V32.8041ZM56.0137 33.059C56.0137 33.173 55.9646 33.2523 55.9322 33.2902C55.9162 33.3089 55.9022 33.3203 55.8942 33.3262C55.8901 33.3293 55.8868 33.3313 55.8848 33.3326C55.8828 33.3338 55.8816 33.3344 55.8816 33.3344C55.8815 33.3344 55.8823 33.334 55.884 33.3333C55.8857 33.3326 55.8881 33.3317 55.8914 33.3306C55.8979 33.3283 55.9068 33.3258 55.9181 33.3233C55.9411 33.3184 55.97 33.3149 56.003 33.3149V32.3149C55.806 32.3149 55.6043 32.3549 55.4337 32.4403C55.2973 32.5086 55.0137 32.6979 55.0137 33.059H56.0137Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-27-inside-4_3430_36568)"/><mask id="path-29-inside-5_3430_36568" fill="white"><path fill-rule="evenodd" clip-rule="evenodd" d="M63.7784 30.5434H83.0878C83.3573 30.5434 83.5771 30.654 83.5771 30.7864C83.5771 30.9188 83.3542 31.0305 83.0878 31.0305H63.7784C63.513 31.0305 63.2891 30.9209 63.2891 30.7864C63.2891 30.652 63.5099 30.5434 63.7784 30.5434Z"/></mask><path fill-rule="evenodd" clip-rule="evenodd" d="M63.7784 30.5434H83.0878C83.3573 30.5434 83.5771 30.654 83.5771 30.7864C83.5771 30.9188 83.3542 31.0305 83.0878 31.0305H63.7784C63.513 31.0305 63.2891 30.9209 63.2891 30.7864C63.2891 30.652 63.5099 30.5434 63.7784 30.5434Z" fill="#FF9300" fill-opacity="0.5"/><path d="M63.7784 31.0434H83.0878V30.0434H63.7784V31.0434ZM83.0878 31.0434C83.1215 31.0434 83.1509 31.0469 83.174 31.0518C83.1854 31.0542 83.1943 31.0568 83.2008 31.059C83.2074 31.0612 83.2105 31.0628 83.2104 31.0627C83.2103 31.0627 83.2091 31.0621 83.207 31.0608C83.2049 31.0595 83.2016 31.0574 83.1973 31.0543C83.1892 31.0483 83.175 31.0367 83.1588 31.0178C83.1258 30.9793 83.0771 30.8999 83.0771 30.7864H84.0771C84.0771 30.4254 83.794 30.2363 83.6567 30.1678C83.486 30.0827 83.2844 30.0434 83.0878 30.0434V31.0434ZM83.0771 30.7864C83.0771 30.6711 83.1272 30.5914 83.1594 30.554C83.1894 30.5191 83.213 30.5088 83.209 30.5108C83.201 30.5147 83.1577 30.5305 83.0878 30.5305V31.5305C83.2843 31.5305 83.4856 31.4904 83.6558 31.4054C83.7904 31.3382 84.0771 31.1493 84.0771 30.7864H83.0771ZM83.0878 30.5305H63.7784V31.5305H83.0878V30.5305ZM63.7784 30.5305C63.7459 30.5305 63.717 30.5271 63.6938 30.5221C63.6824 30.5197 63.6733 30.5171 63.6666 30.5149C63.6633 30.5137 63.6607 30.5128 63.659 30.5121C63.6581 30.5117 63.6574 30.5114 63.657 30.5112C63.6565 30.511 63.6563 30.5109 63.6563 30.5109C63.6563 30.5109 63.6566 30.511 63.6571 30.5113C63.6576 30.5116 63.6584 30.512 63.6594 30.5126C63.6614 30.5138 63.6646 30.5158 63.6688 30.5189C63.6767 30.5247 63.6908 30.5361 63.707 30.5549C63.74 30.5932 63.7891 30.6727 63.7891 30.7864H62.7891C62.7891 31.1501 63.0756 31.3392 63.2121 31.4068C63.3826 31.4914 63.5837 31.5305 63.7784 31.5305V30.5305ZM63.7891 30.7864C63.7891 30.8982 63.7413 30.9775 63.7076 31.0169C63.6912 31.0361 63.6767 31.0478 63.6684 31.0539C63.6599 31.0601 63.6549 31.0626 63.6549 31.0626C63.6547 31.0627 63.6577 31.0612 63.6644 31.059C63.6709 31.0568 63.68 31.0542 63.6915 31.0518C63.7148 31.0469 63.7445 31.0434 63.7784 31.0434V30.0434C63.5836 30.0434 63.3823 30.0817 63.2112 30.1664C63.072 30.2353 62.7891 30.4246 62.7891 30.7864H63.7891Z" fill="#792AFF" fill-opacity="0.5" mask="url(#path-29-inside-5_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.8383 27.3766H47.1452C47.8139 27.3766 48.3624 27.9226 48.3624 28.5907V32.9796C48.3624 33.6487 47.8129 34.1937 47.1452 34.1937H42.8383C42.1664 34.1937 41.6211 33.6476 41.6211 32.9796V28.5907C41.6211 27.9226 42.1664 27.3766 42.8383 27.3766Z" fill="#792AFF" fill-opacity="0.1"/><path d="M42.8383 27.6266H47.1452C47.6766 27.6266 48.1124 28.0614 48.1124 28.5907V32.9796C48.1124 33.5096 47.6758 33.9437 47.1452 33.9437H42.8383C42.3042 33.9437 41.8711 33.5093 41.8711 32.9796V28.5907C41.8711 28.061 42.3042 27.6266 42.8383 27.6266Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.4221 37.085H70.9786C71.2575 37.085 71.4846 37.1987 71.4846 37.3383C71.4846 37.4748 71.2544 37.5907 70.9786 37.5907H42.4221C42.1411 37.5907 41.9141 37.4779 41.9141 37.3383C41.9141 37.1987 42.1411 37.085 42.4221 37.085Z" fill="#FF9300" fill-opacity="0.5"/><path d="M42.4221 37.335H70.9786C71.0013 37.335 71.0231 37.3359 71.0437 37.3377C71.023 37.3396 71.0012 37.3407 70.9786 37.3407H42.4221C42.3988 37.3407 42.3766 37.3397 42.3555 37.3378C42.3766 37.336 42.3988 37.335 42.4221 37.335Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.42 39.4481H62.3203C62.5992 39.4481 62.8284 39.5639 62.8284 39.7014C62.8284 39.8379 62.5972 39.9548 62.3203 39.9548H42.42C42.1401 39.9548 41.9141 39.84 41.9141 39.7014C41.9141 39.5608 42.1411 39.4481 42.42 39.4481Z" fill="#FF9300" fill-opacity="0.5"/><path d="M42.42 39.6981H62.3203C62.3449 39.6981 62.3684 39.6992 62.3906 39.7014C62.3683 39.7036 62.3448 39.7048 62.3203 39.7048H42.42C42.3949 39.7048 42.371 39.7036 42.3484 39.7014C42.371 39.6993 42.3949 39.6981 42.42 39.6981Z" stroke="#792AFF" stroke-opacity="0.5" stroke-width="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M52.2911 23.1545H48.9236V15.2878H47.1123V24.6799H52.2901V23.1535L52.2911 23.1545ZM57.0573 15.1399C54.5949 15.1399 53.0397 17.2061 53.0397 20.0138C53.0397 22.8215 54.4466 24.8143 56.9349 24.8143C59.274 24.8143 60.939 23.1421 60.939 19.868C60.939 17.3633 59.6648 15.1399 57.0583 15.1399H57.0573ZM57.0054 16.6187C58.3906 16.6187 59.0407 18.2196 59.0407 19.9404C59.0407 21.835 58.3408 23.3355 56.994 23.3355C55.6472 23.3355 54.937 21.8236 54.937 19.9776C54.937 18.2547 55.585 16.6187 57.0054 16.6187ZM66.5844 15.2878H64.3066L61.6368 24.6799H63.474L64.1231 22.201H66.6456L67.3195 24.6799H69.2158L66.5844 15.2878ZM64.3802 20.8329L64.9432 18.6829C65.0769 18.1337 65.2366 17.3633 65.36 16.7769H65.3973C65.5186 17.3499 65.6783 18.1079 65.8255 18.6943L66.3885 20.8329H64.3812H64.3802ZM77.3008 15.2878H75.6585V18.1079C75.6585 19.6343 75.7062 20.9673 75.8171 22.1638H75.7933C75.4128 21.1876 74.8871 20.0397 74.3853 19.0976L72.3988 15.2867H70.4413V24.6789H72.0826V21.7967C72.0826 20.1224 72.0463 18.8049 71.9716 17.6436H72.0204C72.4123 18.6684 72.9638 19.8297 73.4667 20.7946L75.4864 24.6789H77.2997V15.2867L77.3008 15.2878Z" fill="url(#paint3_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M85.1315 36.4293C75.5497 46.9839 61.7623 55.2913 45.1461 58.6872C42.308 53.9535 39.9951 49.0813 38.2168 44.1548C52.7508 42.2015 64.788 35.6809 73.0511 27.25C76.6396 30.6385 80.6764 33.7275 85.1315 36.4293Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M83.7105 36.5811C74.4327 46.4658 61.3524 54.2512 45.6629 57.6022C43.1962 53.4653 41.1271 49.2199 39.4727 44.9278C53.4195 42.8023 65.0328 36.5174 73.1833 28.4065C76.3554 31.3833 79.8726 34.13 83.7105 36.5811Z" fill="url(#paint4_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M71.1197 34.6132C70.1237 35.4271 68.8181 35.7639 67.6157 35.618C62.4721 39.3451 56.4676 42.423 49.7614 44.5391C49.3581 45.628 48.3264 46.5636 46.8126 46.9546C45.2951 47.3494 43.7475 47.6956 42.1699 47.9912C43.1135 50.068 44.1564 52.128 45.2951 54.1619C46.9646 53.7989 48.604 53.3854 50.2135 52.9176C51.7686 52.4704 53.4475 52.5079 54.9087 52.9251C62.262 50.3075 68.8481 46.6552 74.5019 42.2939C74.6013 41.218 75.1903 40.1216 76.2201 39.2459C77.5876 38.0859 78.8895 36.8809 80.1294 35.6348C78.2592 34.2764 76.4734 32.8488 74.7795 31.3539C73.6202 32.4784 72.3972 33.5655 71.1179 34.6132H71.1197Z" fill="#F9F9F9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M67.2153 41.6077C69.3406 43.881 69.1455 47.0917 66.2961 48.6952C63.4542 50.2987 59.2936 49.3725 57.4815 46.7587C55.6657 44.1486 56.613 41.1062 59.1323 39.8302C61.6647 38.5598 65.0712 39.3231 67.2153 41.6077Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M54.1556 48.5475C54.8422 49.5766 54.2794 50.8395 52.82 51.3297C51.3644 51.82 49.6104 51.3017 48.9877 50.2184C48.3649 49.135 49.0589 47.9432 50.4508 47.5072C51.8464 47.0731 53.469 47.5203 54.1556 48.5475Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M74.0348 37.1284C74.9296 37.9255 74.8395 39.2782 73.744 40.1408C72.6523 41.0052 71.0034 40.994 70.1631 40.1333C69.3114 39.2726 69.5422 37.9442 70.5833 37.1471C71.6262 36.35 73.1325 36.3369 74.0329 37.1303L74.0348 37.1284Z" fill="#792AFF" fill-opacity="0.5"/><path d="M64.5389 43.3175C64.8489 43.4128 65.0991 43.6135 65.2897 43.9197C65.5998 44.4178 65.6834 44.9051 65.5408 45.382C65.3981 45.8588 65.0491 46.2694 64.4936 46.614L64.2972 46.7358L64.7719 47.4984L64.3788 47.7422L63.9041 46.9797L62.4263 47.8965L59.5292 43.2432C59.4679 43.1447 59.4095 43.0803 59.3543 43.0496C59.299 43.0194 59.2333 42.9953 59.157 42.978C59.1095 42.9717 59.071 42.9595 59.0417 42.9418C59.0124 42.9239 58.9918 42.8938 58.9796 42.8509C58.9392 42.7394 58.933 42.6157 58.9611 42.4796C58.9889 42.3435 59.0501 42.2462 59.1444 42.1877C59.2439 42.126 59.3622 42.1175 59.4987 42.1619L60.5126 41.5329L60.0329 40.7625L60.426 40.5186L60.9057 41.289L61.0785 41.1818C61.5867 40.8666 62.0713 40.7493 62.5322 40.8302C62.9928 40.9112 63.3573 41.167 63.6254 41.5975C63.8223 41.9138 63.9032 42.2071 63.8679 42.4771C63.8327 42.7472 63.7079 43.0134 63.4934 43.2756L63.508 43.299C63.8849 43.2164 64.2285 43.2226 64.5384 43.3177L64.5389 43.3175ZM61.2507 44.2571L61.9424 43.828L60.9057 42.1628L60.214 42.5919L61.2507 44.2571ZM63.5119 46.3493L62.3394 44.4662L61.6477 44.8953L62.8202 46.7784L63.5119 46.3493ZM61.2986 41.9191L62.3353 43.5843L62.3431 43.5794C62.6522 43.3877 62.8515 43.1652 62.9409 42.9121C63.0301 42.6591 62.9861 42.3898 62.8083 42.1044C62.6566 41.8606 62.4585 41.7118 62.2147 41.6583C61.9705 41.6047 61.7044 41.6673 61.4163 41.846L61.2984 41.9192L61.2986 41.9191ZM64.6627 45.3012C64.7327 45.0349 64.6693 44.7434 64.4722 44.4268C64.2882 44.1312 64.0504 43.9623 63.7589 43.9201C63.4675 43.878 63.1489 43.964 62.803 44.1786L62.7323 44.2224L63.9048 46.1056L64.062 46.008C64.3922 45.8032 64.5922 45.5677 64.6623 45.3011L64.6627 45.3012Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M77.5629 66.5975C78.4275 65.3943 79.6811 64.3733 79.989 62.9259C80.3549 61.2151 79.2708 59.4921 79.4273 57.7515C79.6251 55.5595 81.6532 54.0424 82.695 52.1046C83.7177 50.2075 83.1642 49.6136 83.1534 47.4596C83.1414 44.2148 85.5921 39.9902 89.1965 40.9053C92.8547 41.8352 92.4377 45.2035 91.9351 47.7501C91.5753 49.563 91.8842 49.8057 92.4132 51.5748C93.055 53.7132 94.8721 55.7172 94.2667 57.8657C93.8926 59.1947 92.6538 60.1408 92.2555 61.464C91.8052 62.9628 92.5244 64.5244 92.8482 66.0542C93.5334 69.2769 92.9301 72.0158 90.7026 74.8176C90.4307 75.1051 90.1452 75.3686 89.8445 75.6135C86.5642 75.6148 82.9062 75.6514 78.9285 75.7184C78.8709 75.6666 78.8151 75.613 78.7582 75.5577C76.472 73.3094 75.618 69.2992 77.5638 66.5975L77.5629 66.5975Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M87.888 43.9303C87.9352 43.7813 88.0965 43.6998 88.2477 43.7486C88.3971 43.7974 88.4788 43.9583 88.4298 44.1073C88.186 44.855 87.9898 45.6347 87.828 46.4453C87.9054 46.3681 87.9865 46.2925 88.0702 46.2176C88.44 45.8879 88.8674 45.5951 89.3445 45.3394C89.483 45.2634 89.6548 45.3173 89.7309 45.4546C89.8044 45.5938 89.7512 45.7642 89.6136 45.8401C89.1735 46.0754 88.7855 46.3433 88.4487 46.6421C88.1306 46.9268 87.8564 47.2391 87.6272 47.5828C87.3527 49.333 87.2111 51.2088 87.0976 53.1838C87.4005 52.6545 87.73 52.1787 88.0834 51.7546C88.6967 51.0161 89.3878 50.4308 90.1568 50.0013C90.2943 49.9228 90.4676 49.9721 90.5446 50.1094C90.6206 50.245 90.5738 50.4186 90.4361 50.4945C89.7308 50.8899 89.0933 51.4318 88.5215 52.1178C87.9548 52.7991 87.4548 53.6253 87.0232 54.5918L86.995 55.1273C86.9343 56.3043 86.8707 57.5124 86.7882 58.7461C86.7888 58.7575 86.7885 58.769 86.7847 58.7816C86.6979 60.0703 86.5852 61.3885 86.4277 62.7303C86.9941 61.7506 87.613 60.8813 88.2852 60.1169C89.2682 58.9968 90.364 58.1074 91.5734 57.4496C91.7111 57.3737 91.8837 57.4258 91.9597 57.5622C92.0341 57.7005 91.9836 57.8726 91.8459 57.9476C90.6969 58.5758 89.6511 59.4229 88.7132 60.4904C87.777 61.5569 86.9449 62.8432 86.2153 64.3475C85.9565 66.1296 85.6031 67.9474 85.1043 69.7939L85.102 69.8002C84.8575 70.7059 84.5779 71.6188 84.2569 72.5355C85.0804 71.7545 85.9602 71.0704 86.9017 70.4838C88.2125 69.6646 89.6307 69.0376 91.1595 68.6018C91.3103 68.5579 91.4679 68.6451 91.5111 68.7965C91.556 68.9478 91.4676 69.105 91.3159 69.1489C89.8368 69.5707 88.4637 70.1769 87.2033 70.9635C85.9558 71.7423 84.8132 72.7021 83.7736 73.8401C83.5343 74.4517 83.2765 75.066 82.9975 75.6805L82.3669 75.6872C83.3035 73.6638 84.0082 71.6559 84.5454 69.6786C84.58 68.2876 84.3447 66.9122 83.8375 65.5515C83.3233 64.1727 82.5307 62.802 81.4567 61.4475C81.3601 61.3244 81.3809 61.1442 81.5025 61.0488C81.6258 60.9507 81.8047 60.9715 81.9031 61.0945C83.0115 62.4984 83.834 63.9162 84.3688 65.3522C84.6727 66.168 84.8849 66.9895 85.0034 67.8131C85.6987 64.6953 86.0162 61.6648 86.2147 58.779C85.8194 57.5795 85.3941 56.5704 84.9377 55.7499C84.4851 54.9329 84.0061 54.3087 83.501 53.8818C83.3825 53.7793 83.368 53.6009 83.4707 53.4808C83.5725 53.3626 83.7522 53.3464 83.8699 53.4489C84.4306 53.9242 84.952 54.5991 85.4368 55.4746C85.7392 56.0196 86.0279 56.6438 86.3058 57.3498C86.3498 56.5876 86.3881 55.8362 86.427 55.0964L86.4577 54.5229C86.4571 54.5114 86.4576 54.5034 86.4588 54.4919C86.6624 50.5714 86.8895 47.0036 87.8906 43.9302L87.888 43.9303Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M87.3449 67.6517C88.3061 67.0694 89.4822 66.7284 90.1253 65.8111C90.8863 64.727 90.6453 63.2062 91.2702 62.0409C92.0592 60.5715 93.9214 60.1142 95.2262 59.0729C96.501 58.0507 96.2891 57.4764 96.9223 55.9717C97.8783 53.7064 100.842 51.4892 103.081 53.1909C105.353 54.9209 104.061 57.1443 102.954 58.7686C102.165 59.9247 102.308 60.1857 102.153 61.5741C101.964 63.2537 102.634 65.1876 101.573 66.5042C100.919 67.3197 99.7749 67.6123 99.1039 68.4163C98.3454 69.3263 98.384 70.6271 98.1553 71.7898C97.8473 73.3546 97.2305 74.6263 96.2041 75.6781C93.3531 75.6291 90.0485 75.6211 86.358 75.6469C86.0115 75.2243 85.7244 74.7526 85.5152 74.2461C84.5911 72.0048 85.1884 68.9582 87.3458 67.6517L87.3449 67.6517Z" fill="url(#paint5_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M101.154 54.8489C101.231 54.7585 101.367 54.7497 101.456 54.8281C101.546 54.9047 101.555 55.0402 101.478 55.1315C101.086 55.5788 100.717 56.0655 100.364 56.5823C100.441 56.5519 100.521 56.5231 100.602 56.496C100.956 56.3763 101.34 56.2966 101.75 56.2588C101.867 56.2475 101.97 56.3358 101.983 56.4552C101.992 56.5721 101.906 56.6763 101.788 56.6876C101.41 56.7229 101.06 56.7937 100.737 56.903C100.43 57.0052 100.147 57.1424 99.8855 57.3147C99.1732 58.4518 98.518 59.7167 97.8534 61.0579C98.2214 60.7814 98.5907 60.5462 98.9631 60.3541C99.6114 60.0217 100.266 59.8186 100.93 59.7458C101.048 59.7336 101.154 59.8199 101.166 59.9358C101.181 60.0542 101.095 60.1601 100.978 60.1749C100.369 60.2397 99.7625 60.4288 99.1625 60.7375C98.5642 61.0462 97.972 61.472 97.3842 62.0176L97.2047 62.3825C96.8141 63.1845 96.4104 64.0066 95.9871 64.8421C95.9822 64.8503 95.979 64.8576 95.975 64.8657C95.5314 65.7384 95.061 66.6222 94.5536 67.5105C95.2377 66.9962 95.9283 66.5733 96.6218 66.2404C97.6381 65.7505 98.6671 65.455 99.7042 65.3524C99.824 65.341 99.9285 65.4292 99.9408 65.5469C99.9503 65.6638 99.8637 65.7697 99.7466 65.781C98.7614 65.8783 97.7814 66.1615 96.808 66.6271C95.84 67.0933 94.8798 67.7445 93.9227 68.5782C93.2144 69.7408 92.4291 70.9037 91.531 72.0407L91.5278 72.0461L91.526 72.0462C91.0884 72.6044 90.623 73.157 90.1254 73.7015C90.9319 73.3989 91.7483 73.184 92.5785 73.0515C93.7328 72.8696 94.908 72.8526 96.1014 73.0007C96.2207 73.0148 96.3041 73.1218 96.2907 73.2399C96.2763 73.3571 96.1674 73.4412 96.0481 73.4271C94.8912 73.2833 93.7567 73.3 92.6447 73.477C91.5449 73.6517 90.4628 73.9816 89.402 74.4676C89.0126 74.8618 88.6088 75.2514 88.1853 75.6368L87.5341 75.6393C88.9313 74.4167 90.1284 73.1232 91.1751 71.795C91.6125 70.8379 91.8587 69.8085 91.9085 68.7115C91.9618 67.5975 91.813 66.4083 91.4685 65.147C91.4378 65.032 91.5052 64.9138 91.6204 64.8832C91.7356 64.8517 91.8541 64.9189 91.8848 65.0338C92.243 66.3377 92.393 67.5692 92.3386 68.73C92.3078 69.3874 92.2112 70.0208 92.0497 70.631C93.4617 68.6636 94.5835 66.6467 95.5786 64.6945C95.658 63.7442 95.6614 62.9144 95.5878 62.2067C95.5145 61.5035 95.3681 60.9293 95.1425 60.4819C95.0901 60.3761 95.1339 60.2458 95.2409 60.1936C95.3461 60.1405 95.4758 60.1833 95.529 60.2891C95.7766 60.7848 95.9407 61.4111 96.0175 62.1644C96.0673 62.6341 96.0816 63.1541 96.0668 63.7268C96.3234 63.2097 96.5741 62.6981 96.8198 62.1939L97.012 61.8019C97.0144 61.7956 97.0193 61.7883 97.0216 61.782C98.3284 59.1112 99.5446 56.6914 101.157 54.8479L101.154 54.8489Z" fill="white"/><path d="M22.8188 58.0477C22.4203 58.9984 21.9771 59.9743 21.4899 60.9572C21.0356 61.8707 20.5614 62.7503 20.0797 63.5864L15.5098 63.1148C16.37 62.9511 17.6331 61.5484 18.6044 59.601C19.6155 57.5723 19.9581 55.6268 19.4901 54.8883L22.8188 58.0477Z" fill="url(#paint6_linear_3430_36568)"/><path d="M47.0415 69.0195C47.0415 69.0195 45.7257 74.5444 42.7527 77.3487C41.7497 76.8387 40.7659 76.3671 39.8194 75.9377C39.3607 75.728 38.9132 75.5253 38.4707 75.3358C41.4437 72.5309 42.991 66.6421 42.991 66.6421C44.2454 67.4285 45.5898 68.2205 47.0421 69.0195H47.0415Z" fill="#792AFF" fill-opacity="0.75"/><path d="M43.2326 66.7049C43.2604 66.5991 43.2162 66.4876 43.1235 66.4295C36.22 62.1048 32.2342 58.0229 29.9768 55.0319C28.8479 53.5362 28.1506 52.3125 27.7365 51.4664C27.5294 51.0433 27.3931 50.7146 27.3091 50.4933C27.2671 50.3827 27.2381 50.2989 27.2199 50.2437C27.2108 50.2161 27.2044 50.1956 27.2004 50.1824L27.1961 50.1682L27.1952 50.1652L27.1952 50.1651L27.1951 50.1649C27.1627 50.0504 27.0541 49.9746 26.9354 49.9838C26.8167 49.9929 26.7209 50.0845 26.7064 50.2027L26.6985 50.2676C26.6985 50.2678 26.6985 50.268 26.6984 50.2682C26.2881 53.4913 25.0289 57.5136 22.9934 61.6273C22.9839 61.6416 22.9753 61.6575 22.9683 61.6752C22.9681 61.6757 22.9679 61.6761 22.9677 61.6766C22.9618 61.6894 22.9564 61.7013 22.951 61.7126C21.014 65.6182 18.7147 68.9047 16.5083 71.1715C16.4312 71.2508 16.4153 71.3713 16.4693 71.4678C16.5232 71.5644 16.6342 71.614 16.7421 71.5898C18.461 71.2048 24.0077 70.4497 33.3912 73.6358L33.3919 73.636C33.9353 73.8189 34.489 74.0155 35.0585 74.2321L35.0604 74.2328C36.1213 74.6267 37.2251 75.0683 38.3711 75.5645C38.4628 75.6042 38.5693 75.5855 38.642 75.517C40.1685 74.0771 41.3149 71.863 42.0766 70.0324C42.459 69.1132 42.7476 68.2827 42.9407 67.6818C43.0372 67.3813 43.11 67.1379 43.1587 66.9693C43.1831 66.885 43.2014 66.8193 43.2137 66.7745L43.2277 66.7233L43.2313 66.7099L43.2322 66.7063L43.2325 66.7053L43.2326 66.7051C43.2326 66.705 43.2326 66.7049 42.9908 66.6414L43.2326 66.7049ZM23.0555 61.5626L23.055 61.5629L23.0555 61.5626Z" fill="url(#paint7_linear_3430_36568)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><mask id="mask0_3430_36568" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="42" y="69" width="11" height="11"><path style="mix-blend-mode:multiply" d="M51.3557 76.876C49.8792 79.5588 46.239 79.2018 46.239 79.2018C45.0429 78.5325 43.8835 77.9174 42.7539 77.3482C45.7269 74.5433 47.0427 69.019 47.0427 69.019C48.15 69.6222 49.3106 70.2317 50.5377 70.8367C51.4203 71.4978 52.8385 74.1945 51.3557 76.876Z" fill="url(#paint8_linear_3430_36568)"/></mask><g mask="url(#mask0_3430_36568)"><path d="M51.3557 76.8761C49.8792 79.5588 46.239 79.2018 46.239 79.2018C45.0429 78.5326 43.8835 77.9174 42.7539 77.3483C45.7269 74.5433 47.0427 69.0191 47.0427 69.0191C48.15 69.6223 49.3106 70.2317 50.5377 70.8368C51.4203 71.4979 52.8385 74.1945 51.3557 76.8761Z" fill="url(#paint9_linear_3430_36568)"/></g><path d="M23.2138 61.7242L23.1797 61.7767L23.1911 61.7844L23.2252 61.7318L23.2138 61.7242Z" fill="url(#paint10_linear_3430_36568)"/><path d="M19.2614 54.6248L19.2188 54.6705L19.4681 54.9098L19.5108 54.864L19.2614 54.6248Z" fill="url(#paint11_linear_3430_36568)"/><path d="M39.8206 75.9372L32.3776 86.3617C32.0002 86.8856 31.2926 87.038 30.7396 86.7055L26.6178 84.2645C25.9009 83.8389 25.7861 82.8309 26.3993 82.256L31.6576 77.2915L35.1489 73.9987C36.2139 74.3941 37.3224 74.8373 38.4719 75.3353C38.9151 75.5248 39.3626 75.7276 39.8206 75.9372Z" fill="#792AFF"/><path d="M35.1465 73.9984L31.6553 77.2913L30.9757 76.7536C30.6771 76.5207 30.6579 76.0762 30.931 75.8155L31.2183 75.5385L32.9587 73.8883L33.4707 73.399C34.0169 73.5829 34.5743 73.7806 35.1465 73.9984Z" fill="url(#paint12_linear_3430_36568)"/><path d="M32.9599 73.8885L31.2195 75.5394L30.9297 75.3379L32.675 73.694L32.9599 73.8885Z" fill="#792AFF"/><path d="M25.2603 43.4349C22.1551 41.8432 16.1793 47.4984 11.9221 56.0649C7.65937 64.6308 6.72712 72.8586 9.83791 74.4515C11.5187 75.3153 14.0467 74.0504 16.6876 71.3463C18.9158 69.057 21.2297 65.7471 23.1761 61.8221C23.1841 61.8051 23.1922 61.7875 23.2003 61.7698C23.2009 61.7635 23.2071 61.7648 23.2077 61.7585C25.2584 57.6188 26.5314 53.5616 26.9466 50.2995L26.9547 50.2334C27.3848 46.7957 26.8504 44.2445 25.2603 43.4349ZM21.4897 60.957C21.0354 61.8706 20.5612 62.7501 20.0796 63.5863L15.5096 63.1147C16.3698 62.951 17.6329 61.5482 18.6042 59.6008C19.6153 57.5722 19.9579 55.6267 19.4899 54.8882L22.8186 58.0476C22.4201 58.9983 21.9769 59.9742 21.4897 60.957Z" fill="url(#paint13_linear_3430_36568)"/><path d="M22.8187 58.0485L19.4901 54.8891C19.9581 55.6277 19.6155 57.5732 18.6044 59.6018C17.6331 61.5492 16.37 62.952 15.5098 63.1157L20.0797 63.5872C16.5444 69.7153 12.4325 73.5943 10.347 72.5271C7.97671 71.3145 9.11874 64.1671 12.8967 56.5664C16.6816 48.9601 21.6693 43.7777 24.039 44.9966C26.1015 46.055 25.5019 51.6126 22.8187 58.0485Z" fill="#1A1818" fill-opacity="0.35"/><path d="M18.6033 59.6011C17.632 61.5485 16.3689 62.9513 15.5087 63.115C15.3355 63.149 15.1865 63.1307 15.055 63.0652C14.2456 62.6522 14.5299 60.436 15.6849 58.1121C16.8406 55.7888 18.4295 54.2362 19.2389 54.6492C19.3419 54.6989 19.4282 54.7827 19.4884 54.8884C19.9564 55.627 19.6138 57.5725 18.6027 59.6011H18.6033Z" fill="url(#paint14_linear_3430_36568)"/><path d="M17.9864 59.2868C18.8726 57.5048 19.1737 55.8466 18.6589 55.5832C18.1442 55.3197 17.0084 56.5508 16.1222 58.3328C15.236 60.1148 14.9349 61.773 15.4497 62.0364C15.9644 62.2999 17.1001 61.0688 17.9864 59.2868Z" fill="white"/><path d="M77.7408 52.5636L77.7407 52.5636C77.6191 52.5482 77.4841 52.5016 77.3273 52.4476C76.8659 52.2883 76.2167 52.0643 75.1708 52.3835C74.5758 51.9993 74.0093 51.8549 73.4609 51.8547C72.8889 51.8546 72.3505 52.0118 71.8442 52.1872C71.7179 52.2309 71.594 52.2755 71.4718 52.3193C70.586 52.6375 69.7931 52.9223 68.9007 52.6343C68.8059 52.6037 68.702 52.6323 68.6362 52.7071C68.5704 52.782 68.5553 52.8887 68.5978 52.9788C69.0647 53.9686 69.7128 54.6663 70.4066 55.2605C70.7415 55.5473 71.0893 55.8119 71.4305 56.0714L71.4625 56.0958C71.8159 56.3646 72.1619 56.6289 72.492 56.9156C72.5134 56.9342 72.5378 56.9489 72.5641 56.9594C74.2088 57.6092 75.7996 57.5518 77.3387 56.9602C77.3528 56.9548 77.3664 56.9481 77.3793 56.9402C78.0311 56.5418 78.6376 55.9746 79.2097 55.4397C79.3481 55.3103 79.4844 55.1828 79.6188 55.0601C80.3301 54.4108 80.9843 53.8977 81.6685 53.7647C81.7606 53.7468 81.8349 53.6789 81.861 53.5887C81.887 53.4985 81.8604 53.4014 81.792 53.3371C81.3802 52.9503 80.9994 52.7118 80.6311 52.5762C80.2615 52.44 79.9186 52.4126 79.5917 52.4287C79.3632 52.44 79.1313 52.474 78.91 52.5065C78.8253 52.5189 78.7422 52.5311 78.6614 52.5417C78.3597 52.5813 78.0619 52.6041 77.7408 52.5636ZM77.7144 57.4021C77.65 57.3619 77.5709 57.3531 77.4992 57.3782C75.8368 57.9608 74.1532 58.0495 72.4138 57.3808C72.3405 57.3526 72.2582 57.3605 72.1916 57.4021C68.408 59.7662 66.0514 62.6188 65.0093 65.547C63.9881 68.4164 64.2359 71.3417 65.6186 73.9123L63.7372 75.7952C63.6658 75.8667 63.6444 75.9742 63.6831 76.0676C63.7218 76.161 63.813 76.2219 63.9141 76.2219C65.9043 76.2219 67.6344 76.5454 69.3876 76.8739L69.3955 76.8753C71.1427 77.2026 72.9134 77.5343 74.9532 77.5343C76.993 77.5343 78.7635 77.2026 80.5104 76.8753L80.5184 76.8739C82.2715 76.5454 84.0013 76.2219 85.9916 76.2219C86.0927 76.2219 86.1838 76.161 86.2225 76.0676C86.2612 75.9742 86.2399 75.8667 86.1684 75.7952L84.2871 73.9123C85.6697 71.3419 85.9176 68.4165 84.8964 65.5471C83.8544 62.6189 81.4978 59.7662 77.7144 57.4021Z" fill="url(#paint15_linear_3430_36568)" stroke="#1A1818" stroke-width="0.5" stroke-linejoin="round"/><path fill-rule="evenodd" clip-rule="evenodd" d="M68.9531 52.9713C69.5301 53.7221 71.8141 53.7068 72.5515 53.6986C72.9252 53.6947 73.3293 53.7274 73.6733 53.8832C73.8916 53.9826 74.0316 54.1109 74.2228 54.2409C74.6711 54.5453 75.2349 54.4123 75.7236 54.2841C76.4085 54.1045 77.0716 53.9434 77.7711 53.8233C78.988 53.6138 80.2234 53.5165 81.4549 53.4969C81.4692 53.493 81.4838 53.4887 81.4974 53.4855C80.899 52.9449 80.3206 52.6755 79.4874 52.7439C78.8661 52.7956 78.3413 52.9502 77.7011 52.8693C77.3656 52.8269 77.0344 52.6591 76.6986 52.5839C76.1667 52.4628 75.6629 52.5422 75.1506 52.7136L75.1231 52.7229L75.0988 52.7061C74.4396 52.2521 73.769 52.0797 72.9759 52.2023C71.588 52.4171 70.4104 53.3568 68.9535 52.9706L68.9531 52.9713Z" fill="url(#paint16_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M72.6566 56.7265C72.4444 57.0811 72.3279 57.3797 72.3086 57.6231C72.314 57.6206 72.3186 57.617 72.3243 57.6138C73.036 57.8878 73.7387 58.0382 74.4336 58.0838C75.5 58.1537 76.5479 57.9755 77.5822 57.6138C77.5404 57.3319 77.43 57.0358 77.2496 56.7269C76.3278 57.0807 75.3904 57.2364 74.4336 57.1538C73.8491 57.1035 73.2575 56.9638 72.6566 56.7269V56.7265Z" fill="#F9F9F9"/><path d="M77.7785 68.2428C78.0682 68.5777 78.2129 68.9922 78.2129 69.4864C78.2129 70.2904 77.9589 70.9182 77.4509 71.3705C76.9428 71.8227 76.244 72.0486 75.3541 72.0486H75.0393V73.2794H74.4095V72.0486H72.0419V64.5374C72.0419 64.3785 72.0208 64.2613 71.979 64.1857C71.9368 64.1105 71.8783 64.035 71.8025 63.9598C71.7521 63.9181 71.7164 63.8761 71.6953 63.8344C71.6743 63.7923 71.6721 63.7424 71.6889 63.6836C71.7225 63.5247 71.8043 63.3761 71.9347 63.2378C72.0647 63.0996 72.2055 63.0304 72.3566 63.0304C72.516 63.0304 72.6589 63.106 72.7846 63.2564H74.4091V62.0127H75.039V63.2564H75.3158C76.1301 63.2564 76.7746 63.4698 77.249 63.8971C77.7231 64.324 77.9603 64.8852 77.9603 65.5801C77.9603 66.0908 77.8428 66.4906 77.6077 66.7796C77.3726 67.0686 77.0368 67.2885 76.6002 67.4388V67.4766C77.0954 67.6527 77.488 67.9082 77.7778 68.2428H77.7785ZM73.302 66.9617H74.4102V64.2737H73.302V66.9617ZM74.4098 71.0312V67.9915H73.3016V71.0312H74.4098ZM75.0397 64.2737V66.9617H75.0522C75.5474 66.9617 75.9378 66.8466 76.2233 66.6164C76.5084 66.3862 76.6513 66.0409 76.6513 65.5801C76.6513 65.1867 76.5295 64.8703 76.2862 64.6319C76.0425 64.3931 75.6899 64.2737 75.2283 64.2737H75.0393H75.0397ZM76.4941 70.6421C76.7667 70.3827 76.9032 69.9974 76.9032 69.4864C76.9032 69.0093 76.7499 68.6408 76.4434 68.381C76.1368 68.1216 75.7067 67.9915 75.1526 67.9915H75.0393V71.0312H75.2912C75.8203 71.0312 76.2208 70.9015 76.4938 70.6417L76.4941 70.6421Z" fill="white"/><path fill-rule="evenodd" clip-rule="evenodd" d="M42.2092 32.2286C34.2813 33.0819 25.4338 31.4377 17.233 26.694C17.8098 23.6653 18.6573 20.7736 19.7482 18.0575C26.5428 22.6313 33.9947 24.3726 40.5818 23.9169C40.819 26.6621 41.3547 29.4486 42.2092 32.2286Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M41.5594 31.7649C34.0075 32.4513 25.6495 30.8513 17.8524 26.4325C18.362 23.7907 19.0781 21.2511 19.9847 18.8457C26.5982 23.13 33.7854 24.8125 40.2065 24.4486C40.423 26.8676 40.8692 29.3185 41.5594 31.7649Z" fill="url(#paint17_linear_3430_36568)"/><path fill-rule="evenodd" clip-rule="evenodd" d="M37.0353 26.274C36.3165 26.2445 35.6462 25.9009 35.1985 25.3942C31.6641 25.0429 28.0119 24.1013 24.4248 22.4981C23.8511 22.8031 23.0723 22.8111 22.2948 22.4131C21.5143 22.0152 20.7394 21.5858 19.9708 21.1243C19.5918 22.3412 19.2606 23.588 18.9791 24.8593C19.8112 25.3268 20.6495 25.7621 21.4955 26.1636C22.3112 26.5536 22.9981 27.1917 23.4529 27.9078C27.4969 29.5416 31.6056 30.459 35.589 30.7347C36.0309 30.3224 36.6848 30.0831 37.4406 30.0994C38.4432 30.1221 39.4351 30.1018 40.4165 30.0414C40.1412 28.7808 39.9269 27.5226 39.7761 26.2704C38.8736 26.31 37.9585 26.3103 37.0345 26.2733L37.0353 26.274Z" fill="#F9F9F9"/><path fill-rule="evenodd" clip-rule="evenodd" d="M32.8024 27.7464C32.8437 29.4835 31.5674 30.7516 29.7811 30.3644C27.998 29.9801 26.6056 28.0506 26.8218 26.2875C27.0351 24.5245 28.5627 23.6056 30.0893 24.007C31.6193 24.4157 32.7574 25.9976 32.8024 27.7464Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M24.7678 25.8019C24.6715 26.4861 23.9665 26.8047 23.1748 26.4682C22.3847 26.1331 21.8453 25.2664 21.9885 24.5832C22.1316 23.9 22.8649 23.6597 23.6082 23.9939C24.3524 24.3301 24.8634 25.1185 24.7678 25.8019Z" fill="#792AFF" fill-opacity="0.5"/><path fill-rule="evenodd" clip-rule="evenodd" d="M37.3167 28.4066C37.3937 29.0712 36.8527 29.6026 36.0743 29.5565C35.2969 29.5125 34.6127 28.8964 34.5821 28.2255C34.5469 27.5504 35.1375 27.0813 35.8688 27.1346C36.6008 27.1886 37.2346 27.7417 37.3152 28.4067L37.3167 28.4066Z" fill="#792AFF" fill-opacity="0.5"/><path d="M31.0493 27.4666C31.1433 27.6213 31.173 27.7979 31.1387 27.9964C31.0828 28.3193 30.9363 28.5538 30.6993 28.7C30.4623 28.8462 30.1638 28.8881 29.8037 28.826L29.6763 28.8041L29.5908 29.2985L29.3359 29.2545L29.4214 28.7601L28.4634 28.5949L28.9855 25.578C28.9966 25.5141 28.9962 25.4656 28.9845 25.4323C28.9727 25.3992 28.9542 25.3647 28.9288 25.3292C28.9113 25.309 28.8998 25.2896 28.8942 25.2714C28.8886 25.253 28.8912 25.2328 28.902 25.2104C28.9267 25.1489 28.9701 25.0949 29.0325 25.0485C29.0947 25.002 29.1565 24.9841 29.2176 24.9946C29.2821 25.0057 29.3347 25.0461 29.3751 25.1152L30.0325 25.2286L30.119 24.7291L30.3738 24.773L30.2874 25.2726L30.3994 25.2919C30.7289 25.3487 30.9749 25.4794 31.1372 25.6841C31.2993 25.8887 31.3563 26.1307 31.308 26.4098C31.2725 26.6149 31.1972 26.7673 31.0819 26.867C30.9667 26.9666 30.8155 27.0315 30.6284 27.0614L30.6258 27.0766C30.8139 27.1819 30.955 27.3119 31.049 27.4665L31.0493 27.4666ZM29.3269 26.6396L29.7754 26.717L29.9622 25.6373L29.5138 25.56L29.3269 26.6396ZM29.4923 28.3515L29.7036 27.1306L29.2552 27.0532L29.0439 28.2742L29.4923 28.3515ZM30.2169 25.6812L30.0301 26.7609L30.0352 26.7618C30.2355 26.7963 30.4015 26.7773 30.5331 26.7048C30.6644 26.6322 30.7463 26.5035 30.7783 26.3184C30.8056 26.1604 30.7783 26.0248 30.6965 25.9121C30.6145 25.7992 30.4801 25.7266 30.2933 25.6944L30.2168 25.6812L30.2169 25.6812ZM30.3628 28.3407C30.4911 28.2555 30.5731 28.1103 30.6087 27.905C30.6418 27.7134 30.6054 27.5547 30.4994 27.4289C30.3934 27.3034 30.2284 27.2211 30.0042 27.1824L29.9584 27.1745L29.747 28.3954L29.849 28.413C30.0631 28.4499 30.2342 28.4258 30.3627 28.3405L30.3628 28.3407Z" fill="white"/></g><defs><linearGradient id="paint0_linear_3430_36568" x1="60.4633" y1="14.2701" x2="60.4633" y2="65.7295" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint1_linear_3430_36568" x1="31.4328" y1="6.23657" x2="31.4328" y2="15" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint2_linear_3430_36568" x1="68.604" y1="69.324" x2="68.604" y2="76.6372" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint3_linear_3430_36568" x1="62.2065" y1="15.1399" x2="62.2065" y2="24.8143" gradientUnits="userSpaceOnUse"><stop stop-color="#FF9300" stop-opacity="0.15"/><stop offset="1" stop-color="#FF9300" stop-opacity="0.4"/></linearGradient><linearGradient id="paint4_linear_3430_36568" x1="61.5916" y1="28.4065" x2="61.5916" y2="57.6022" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><linearGradient id="paint5_linear_3430_36568" x1="94.1577" y1="52.9524" x2="95.3247" y2="75.7232" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint6_linear_3430_36568" x1="19.1643" y1="54.8883" x2="19.1643" y2="63.5864" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint7_linear_3430_36568" x1="17.9321" y1="60.9593" x2="43.3052" y2="64.0039" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.37" stop-color="#F2F2FF"/><stop offset="1" stop-color="#D2D2FF"/></linearGradient><linearGradient id="paint8_linear_3430_36568" x1="40.5734" y1="71.1969" x2="49.269" y2="75.2563" gradientUnits="userSpaceOnUse"><stop stop-color="white"/><stop offset="0.17" stop-color="#C5C5C5"/><stop offset="0.4" stop-color="#808080"/><stop offset="0.6" stop-color="#494949"/><stop offset="0.78" stop-color="#212121"/><stop offset="0.92" stop-color="#080808"/><stop offset="1"/></linearGradient><linearGradient id="paint9_linear_3430_36568" x1="40.5734" y1="71.1969" x2="49.269" y2="75.2564" gradientUnits="userSpaceOnUse"><stop stop-color="#FFD571"/><stop offset="0.28" stop-color="#FFAC54"/><stop offset="0.59" stop-color="#FF8538"/><stop offset="0.85" stop-color="#FF6C27"/><stop offset="1" stop-color="#FF6421"/></linearGradient><linearGradient id="paint10_linear_3430_36568" x1="23.1821" y1="61.7513" x2="23.2216" y2="61.756" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint11_linear_3430_36568" x1="19.2101" y1="54.7472" x2="19.5208" y2="54.7844" gradientUnits="userSpaceOnUse"><stop stop-color="#808080"/><stop offset="0.64" stop-color="#51514C"/><stop offset="1" stop-color="#3C3C35"/></linearGradient><linearGradient id="paint12_linear_3430_36568" x1="32.9424" y1="73.399" x2="32.9424" y2="77.2913" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint13_linear_3430_36568" x1="17.5488" y1="43.1666" x2="17.5488" y2="74.7205" gradientUnits="userSpaceOnUse"><stop stop-color="#A169FF"/><stop offset="1" stop-color="#792AFF"/></linearGradient><linearGradient id="paint14_linear_3430_36568" x1="17.1442" y1="54.5826" x2="17.1442" y2="63.1317" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint15_linear_3430_36568" x1="74.9528" y1="52.1047" x2="74.9528" y2="77.2843" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint16_linear_3430_36568" x1="75.2253" y1="52.1639" x2="75.2253" y2="54.4264" gradientUnits="userSpaceOnUse"><stop stop-color="#FFBF69"/><stop offset="1" stop-color="#FF9300"/></linearGradient><linearGradient id="paint17_linear_3430_36568" x1="35.3671" y1="20.1503" x2="24.524" y2="32.3582" gradientUnits="userSpaceOnUse"><stop stop-color="#EEE5FF"/><stop offset="1" stop-color="#DECBFF"/></linearGradient><clipPath id="clip0_3430_36568"><rect width="120" height="78" fill="white" transform="translate(0 0.5)"/></clipPath></defs></svg>'),
                          SizedBox(
                            height: 16.h,
                          ),
                          buildContainerContent(180.h, mrSignDes1.tr,
                              mrSignDes2.tr, mrSignDes3.tr),
                          SizedBox(
                            height: 32.h,
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                GetBuilder<MRController>(
                  // init: MRController(),
                  builder: (mrControllers) =>
                      Container(
                        color: Colors.white,
                        height: 88.h,
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: 24.w, right: 24.w, top: 44.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                // mrController.mrReferral[0].cust_name.toString(),
                                mr.tr,
                                style: TextStyle(
                                  color: configTheme().textTheme.bodyMedium
                                      ?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  MRPopup.buildMRPrivileges(context);
                                },
                                child: Container(
                                    child: SvgPicture.string(
                                        '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_32814)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09998V12.89" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#FFC20E" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_32814"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>',
                                    color:Colors.black )),
                              )
                              // appConfigService.countryConfigCollection
                              //     .toString() ==
                              //     "aam"
                              //     ? InkWell(
                              //   onTap: () {
                              //     setState(() {
                              //       print("MR");
                              //       var i = box.read('isMRPopupShown');
                              //       print(i);
                              //       if (i == true) {
                              //         i = box.write("isMRPopupShown", null);
                              //         print(i);
                              //         navigationController.update();
                              //         navigationController.showMRPopup();
                              //       }
                              //     });
                              //   },
                              //   child: Container(
                              //       height: 24.h,
                              //       width: 24.w,
                              //       child: SvgPicture.string(
                              //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3242_15175)"> <path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 8.10001V12.89" stroke="#792AFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 16.5H12.01" stroke="#792AFF" stroke-width="2" stroke-linecap="round"/></g><defs> <clipPath id="clip0_3242_15175"> <rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                              // )
                              //     : appConfigService.countryConfigCollection
                              //     .toString() ==
                              //     "rafco"
                              //     ? InkWell(
                              //   onTap: () {
                              //     setState(() {
                              //       print("MR");
                              //       var i = box.read('isMRPopupShown');
                              //       print(i);
                              //       if (i == true) {
                              //         i = box.write("isMRPopupShown", null);
                              //         print(i);
                              //         navigationController.update();
                              //         navigationController.showMRPopup();
                              //       }
                              //     });
                              //   },
                              //   child: Container(
                              //       child: SvgPicture.string(
                              //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_67041)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.10001V12.89" stroke="#EA1B23" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#EA1B23" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_67041"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                              // )
                              //     : InkWell(
                              //   onTap: () {
                              //     setState(() {
                              //       print("MR");
                              //       var i = box.read('isMRPopupShown');
                              //       print(i);
                              //       if (i == true) {
                              //         i = box.write("isMRPopupShown", null);
                              //         print(i);
                              //         navigationController.update();
                              //         navigationController.showMRPopup();
                              //       }
                              //     });
                              //   },
                              //   child: Container(
                              //       child: SvgPicture.string(
                              //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_32814)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09998V12.89" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#FFC20E" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_32814"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                              // )
                            ],
                          ),
                        ),
                      ),
                )
                // mrController.showTutorialMr!.value == false
                //     ? buildContainerShowTutorial()
                //     : Text("")
                //     ? showTutorialMR()
                //     :Container(
                //   height: 40,
                //   width: 40,
                //   color: Colors.red,
                // ),
              ],
            );
          },
        ));
  }

  buildContainerContent(height, textContent1, textContent2, textContent3) {
    return Container(
      height: 136.h,
      // height: height,
      width: double.infinity,
      // width: 156.w,
      decoration: BoxDecoration(
          border: Border.all(
            color: Color(0xFF792AFF).withOpacity(0.08),
          ),
          borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(15.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Text("1.สมัครเป็นตัวแทนแนะนำเพื่อนกับเรา"),

            SizedBox(
              child: Text(
                textContent1,
                style: TextStyle(
                  // color: Colors.black,
                  color: appConfigService.countryConfigCollection == "aam"
                      ? Color(0xFF792AFF).withOpacity(0.75)
                      : appConfigService.countryConfigCollection == "rafco"
                      ? Color(0xFF22409A)
                      : Color(0xFF6A7165),
                  fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.bodySmall?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.bodySmall?.fontWeight,
                  height: 1.2.h,
                ),
              ),
            ),
            SizedBox(
              height: 8.h,
            ),
            SizedBox(
              child: Text(textContent2,
                  style: TextStyle(
                    color: appConfigService.countryConfigCollection == "aam"
                        ? Color(0xFF792AFF).withOpacity(0.75)
                        : appConfigService.countryConfigCollection == "rafco"
                        ? Color(0xFF22409A)
                        : Color(0xFF6A7165),
                    fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                    fontFamily:
                    configTheme().primaryTextTheme.bodySmall?.fontFamily,
                    fontWeight:
                    configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    height: 1.2.h,
                  )),
            ),
            // Text("2.ร่วมทำกิจกรรม พร้อมสะสมคะแนนเพื่อรับสิทธิ\nประโยชน์มากมาย แบบไม่ผูกมัด"),
            SizedBox(
              height: 8.h,
            ),
            SizedBox(
              child: Text(textContent3,
                  style: TextStyle(
                    color: appConfigService.countryConfigCollection == "aam"
                        ? Color(0xFF792AFF).withOpacity(0.75)
                        : appConfigService.countryConfigCollection == "rafco"
                        ? Color(0xFF22409A)
                        : Color(0xFF6A7165),
                    fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                    fontFamily:
                    configTheme().primaryTextTheme.bodySmall?.fontFamily,
                    fontWeight:
                    configTheme().primaryTextTheme.bodySmall?.fontWeight,
                    height: 1.2.h,
                  )),
            ),

            // Text("3.ยิ่งแนะนำมาก ก็ยิ่งได้มาก เพิ่มรายได้ให้กับคุณ")
          ],
        ),
      ),
    );
  }

  buildHead(bool isMRMember) {
    // final MRController mrController = Get.find<MRController>();
    final ProfileController profileController = Get.find<ProfileController>();
    return GetBuilder<MRController>(builder: (mrController) {
      return Container(
        height: 74.h,
        width: Get.width,
        // color: Colors.blueGrey,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(left: 5.w),
              width: 59.w,
              child: Stack(
                children: [
                  Container(
                    alignment: Alignment.center,
                    width: 50.w,
                    height: 55.h,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Obx(() {
                          final avatarUrl =
                              profileController.profile.value.avatar ?? '';
                          bool isValidAvatar = avatarUrl!.isNotEmpty &&
                              avatarUrl.toString().toLowerCase() != 'null';

                          return CircleAvatar(
                            radius: 20.r,
                            backgroundColor: Colors.transparent,
                            child: ClipOval(
                              child: Image.network(
                                isValidAvatar
                                    ? avatarUrl
                                    : 'https://s3-ap-southeast-1.amazonaws.com/storage-pkg/icon/icon1548914956default.png',
                                fit: BoxFit.cover,
                                width: 40,
                                height: 40,
                                errorBuilder: (context, error, stackTrace) =>
                                    Icon(Icons.person,
                                        size: 40, color: Colors.grey),
                              ),
                            ),
                          );
                        }),
                        Obx(() {
                          final percent =
                              profileController.percentUpdateProfile.value;
                          final country = appConfigService
                              .countryConfigCollection
                              .toString();
                          final mrRank =
                          mrController.mrData.value.mr_rank.toString();

                          Color progressColor;
                          if (country == 'aam') {
                            progressColor = (mrRank == "GOLD")
                                ? const Color(0xFFFFD400)
                                : const Color(0xDDDBC6FF);
                          } else if (country == 'rplc') {
                            progressColor =
                                const Color(0xFFFFD400).withOpacity(0.5);
                          } else {
                            progressColor =
                                AppColors.primaryRafco.withOpacity(0.75);
                          }

                          return CircularPercentIndicator(
                            radius: 26.5.r,
                            lineWidth: 5.0.w,
                            percent: percent,
                            progressColor: progressColor,
                            backgroundColor: const Color(0xEEEDEDED),
                            animation: true,
                            circularStrokeCap: CircularStrokeCap.round,
                          );
                        }),
                      ],
                    ),
                  ),
                  Obx(() {
                    return Container(
                        margin:
                        EdgeInsets.only(top: 38.h, left: 13.w, right: 13.w),
                        width: 24.w,
                        height: 24.h,
                        child: SvgPicture.string(mrController
                            .iconProfileLevel.value
                            .toString())
                    );
                  }),
                  Container(
                    height: 17.h,
                    width: 22.w,
                    margin: EdgeInsets.only(top: 40.h, left: 37.w),
                    alignment: Alignment.lerp(
                        Alignment.topCenter, Alignment.bottomCenter, 0.5),
                    child: Obx(() {
                      return FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '${profileController.percentUpdateProfileText.value
                              .obs}%',
                          //TODO เปอร์เซ็นต์อัปเดตข้อมูล
                          style: TextStyle(
                            color: const Color(0xFF1A1818),
                            fontSize: 10.sp,
                            fontFamily: 'IBM Plex Sans Thai',
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      );
                    }),
                  )
                ],
              ),
            ),
            SizedBox(width: 19.w),
            Expanded(
              child: Container(
                height: 74.h,
                width: Get.width,
                // color: Colors.orangeAccent,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Text("DSF735"),
                        Obx(() {
                          return Text(
                            isMRMember
                                ? mrController.mrData.value.mr_id.toString()
                                : profileController.profile.value.ref_code
                                .toString(),
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontWeight,
                              // height: 0,
                            ),
                          );
                        }),
                        Obx(() {
                          return Text(
                            isMRMember
                                ? "${mrController.mrData.value
                                .mr_fname} ${mrController.mrData.value
                                .mr_lname}"
                                : "${profileController.profile.value
                                .firstname} ${profileController.profile.value
                                .lastname}",
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall
                                  ?.fontWeight,
                              // height: 0,
                            ),
                          );
                        }),
                        Container(
                            height: 22.h,
                            decoration: BoxDecoration(
                              color: mrController.mrData.value.mr_id == null
                                  ? Color(0xffffffff)
                                  : configTheme()
                                  .primaryColor
                                  .withOpacity(0.05),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Center(
                                child: Container(
                                  margin: EdgeInsets.only(
                                      left: 6.w, right: 6.w),
                                  child: Text(
                                    mrController.mrData.value.mr_rank
                                        .toString(),
                                    style: TextStyle(
                                      color: configTheme()
                                          .primaryColor
                                          .withOpacity(0.75),
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .labelMedium
                                          ?.fontWeight,
                                      // height: 0,
                                    ),
                                  ),
                                ))),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          mrRefer.tr,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                            // height: 0,
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            // Get.to(()=>AddReferralID());
                            if (mrController.referCode.text == "") {
                              showDialog(
                                  useSafeArea: false,
                                  context: context,
                                  builder: (_) => AddReferralID());
                            } else {
                              debugPrint("เพิ่มรหัสไปแล้ว");
                            }
                          },
                          child: Text(
                            mrController.referCode.text == ""
                                ? mrAddRefer.tr
                                : profileController.profile.value.ref_code_other
                                .obs.toString(),
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              decorationColor: configTheme()
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.75),
                              color: configTheme()
                                  .colorScheme
                                  .onSecondary
                                  .withOpacity(0.75),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyLarge
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyLarge
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyLarge
                                  ?.fontWeight,
                              // height: 0,
                            ),
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  buildMenu(textHeadMenu,
      textHeadContent,
      textContent,) {
    // final MRController mrController = Get.put(MRController());
    final country = appConfigService.countryConfigCollection.toString();
    var name_point =
    Get
        .find<WebViewPointController>()
        .namePoint
        .value
        .toString();

    return SizedBox(
      height: 165.h,
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 21.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: configTheme().colorScheme.onSecondary,
                    // width: 3.w
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Center(
                  child: Text(
                    textHeadMenu,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize: configTheme().primaryTextTheme.bodyLarge
                          ?.fontSize,
                      fontFamily:
                      configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                      fontWeight:
                      configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                      // height: 0,
                    ),
                  )
                // Text("กิจกรรมแนะนำ")
              )
            ],
          ),
          // SizedBox(height: 16.h),
          Container(
            height: 128.h,
            // color: Colors.red,
            width: double.infinity,
            // decoration: BoxDecoration(
            //     color: const Color(0xFFF9F9F9),
            //     borderRadius: BorderRadius.circular(16)),
            child: CarouselSlider(
              items: List.generate(
                2,
                // newsController.newsLastestList.length,
                    (i) {
                  return GestureDetector(
                    onTap: () {},
                    child: Container(
                      width: Get.width,
                      height: 88.h,
                      // color: Colors.orange,
                      // color: Colors.teal,
                      margin:
                      EdgeInsets.only(left: 16.w, right: 10.w, top: 14.h),
                      //padding: EdgeInsets.only(bottom : 12.h),
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    homeController.indexNews!.value == 0
                                        ? textHeadContent
                                        : mrReferApp.tr,
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontWeight,
                                      // height: 0,
                                    ),
                                  ),
                                  // Text('แนะนำเพื่อนมาจัดสินเชื่อ'),
                                  Text(
                                    homeController.indexNews!.value == 0
                                        ? "${mrEarn.tr} 5,000 $name_point"
                                        : "",
                                    // : "${mrEarn.tr} 500 $name_point",
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodySmall
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodySmall
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodySmall
                                          ?.fontWeight,
                                      // height: 0,
                                    ),
                                  ),
                                  // Text('รับคะแนนสูงสุด 5,000 AAMP'),
                                  const SizedBox(height: 10),
                                  InkWell(
                                    onTap: () {
                                      homeController.indexNews!.value == 0
                                          ? showDialog(
                                          context: context,
                                          useSafeArea: false,
                                          builder: (_) =>
                                          mrController.mrData.value.mr_id ==
                                              null
                                              ? MrRegister()
                                              : ReferFriend()
                                      )

                                          : showDialog(
                                          context: context,
                                          useSafeArea: false,
                                          builder: (_) =>
                                          mrController.mrData.value.mr_id ==
                                              null
                                              ? MrRegister()
                                              : QrReferalPage(
                                              id: mrController.mrData.value!
                                                  .mr_id.toString())

                                      );
                                    },
                                    child: Container(
                                      height: 34.h,
                                      width: 116.w,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(12),
                                        color: configTheme()
                                            .colorScheme
                                            .onSecondary
                                            .withOpacity(0.75),
                                        boxShadow: [
                                          BoxShadow(
                                            color: configTheme()
                                                .primaryColor
                                                .withOpacity(0.45),
                                            offset: Offset(2, 4),
                                            blurRadius: 10,
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: Text(
                                          mrReferFriend.tr,
                                          style: TextStyle(
                                            color: appConfigService
                                                .countryConfigCollection ==
                                                "aam" ||
                                                appConfigService
                                                    .countryConfigCollection ==
                                                    "rafco"
                                                ? Color(0xFFFFFFFF)
                                                : Color(0xFF1A1818),
                                            fontSize: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontSize,
                                            fontFamily: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontFamily,
                                            fontWeight: configTheme()
                                                .primaryTextTheme
                                                .bodyMedium
                                                ?.fontWeight,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                ],
                              ),
                              //todo refer friend icon
                              homeController.indexNews!.value == 0
                                  ? Container(
                                  height: 78.h,
                                  child: Image.asset(
                                    country == 'rplc'
                                        ? AppImageAssets.referFriend_RPLC
                                        : country == 'rafco'
                                        ? AppImageAssets
                                        .referFriend_RAFCO
                                        : AppImageAssets
                                        .referFriend_AAM,
                                  ))
                              //todo download icon
                                  : Container(
                                  height: 78.h,
                                  child: Image.asset(
                                    country == 'rplc'
                                        ? AppImageAssets.download_RPLC
                                        : country == 'rafco'
                                        ? AppImageAssets.download_RAFCO
                                        : AppImageAssets.download_AAM,
                                  )),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Opacity(
                                opacity: homeController.indexNews?.value == 1
                                    ? 1
                                    : 0.40,
                                child: Container(
                                  width: homeController.indexNews?.value == 1
                                      ? 15
                                      : 4,
                                  height: 4.h,
                                  decoration: ShapeDecoration(
                                    color: configTheme().colorScheme.primary,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                ),
                              ),
                              SizedBox(width: 6.w),
                              Opacity(
                                opacity: homeController.indexNews?.value == 0
                                    ? 1
                                    : 0.40,
                                child: Container(
                                  width: homeController.indexNews?.value == 0
                                      ? 15
                                      : 4,
                                  height: 4.h,
                                  decoration: ShapeDecoration(
                                    color: configTheme().colorScheme.primary,
                                    shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          // SizedBox(height: 12.h)
                        ],
                      ),
                    ),
                  );
                },
              ),
              options: CarouselOptions(
                  autoPlay: false,
                  initialPage: 0,
                  aspectRatio: 1,
                  viewportFraction: 1,
                  height: 130.h,
                  disableCenter: true,
                  enableInfiniteScroll: false,
                  onPageChanged: (index, reason) {
                    homeController.setIndexNews(index);
                    setState(() {});
                    print('index: $index');
                  }),
            ),
            // ),
            // SizedBox(height: 16.h),
          )
        ],
      ),
    );
  }

  buildPoint(point) {
    final WebViewPointController webViewPointController =
    Get.find<WebViewPointController>();
    var name_point = webViewPointController.namePoint.value.toString();
    return Container(
      height: 94.h,
      width: double.infinity,
      decoration: BoxDecoration(
          color: const Color(0xFFF9F9F9),
          borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              "${mrTatal.tr} $name_point ${mrTatalReceive.tr}",
              // "${mrTatal.tr} $name_point ที่ได้รับ",
              style: TextStyle(
                color: configTheme()
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.35),
                fontSize: configTheme().primaryTextTheme.bodySmall?.fontSize,
                fontFamily:
                configTheme().primaryTextTheme.bodySmall?.fontFamily,
                fontWeight:
                configTheme().primaryTextTheme.bodySmall?.fontWeight,
              ),
            ),
            // SizedBox(height: 4.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  height: 36.h,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: 28.h,
                        width: 28.w,
                        child: Center(
                          child: SvgPicture.string(
                            webViewPointController.point_icon.value,
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Container(
                        alignment: Alignment.centerLeft,
                        height: 36.h,
                        width: 200.w,
                        padding: EdgeInsets.only(top: 5.h),
                        child: Text(
                          point,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: 24.sp,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  child: SvgPicture.string(
                    '<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>',
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  buildContent(textHeader,
      height,
      svg,
      svg1,
      textData,
      textData1,
      numCount,
      numCount1,
      sumCount,) {
    return Container(
        height: height,
        // height: 149.h,
        width: 156.w,
        decoration: BoxDecoration(
            color: const Color(0xFFF9F9F9),
            borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: EdgeInsets.only(left: 18.w, right: 18.w, top: 16.h),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 28.h,
                    width: 28.w,
                    decoration: BoxDecoration(
                        color: const Color(0xFF1A181814).withOpacity(0.08),
                        borderRadius: BorderRadius.circular(10)),
                    child: Center(
                      child: SvgPicture.string(svg),
                      // SvgPicture.string('<svg width="18" height="20" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="1" y="6" width="10" height="13" rx="2" fill="#792AFF" fill-opacity="0.5"/> <path d="M4 12L10 12" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 9L8 9" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M4 15L8 15" stroke="white" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 11V13C14 15.8284 14 17.2426 13.1213 18.1213C12.2426 19 10.8284 19 8 19H7C4.17157 19 2.75736 19 1.87868 18.1213C1 17.2426 1 15.8284 1 13V7C1 4.17157 1 2.75736 1.87868 1.87868C2.75736 1 4.17157 1 7 1H7.5" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M14 1L14 7" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/> <path d="M17 4H11" stroke="#1A1818" stroke-width="1.2" stroke-linecap="round"/></svg>')),
                    ),
                  ),
                  Container(
                    height: 28.h,
                    width: 28.w,
                    child: Center(
                      child: SvgPicture.string(svg1),
                      // SvgPicture.string('<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>')),
                    ),
                  ),
                  // InkWell(
                  //   onTap: () {
                  //     // buildMRHistoryLoan();
                  //     Get.to(()=>page);
                  //     // Navigator.push(context,
                  //     //     MaterialPageRoute(builder: (context) => page));
                  //   },
                  //   child: Container(
                  //     height: 28.h,
                  //     width: 28.w,
                  //     child: Center(
                  //       child: SvgPicture.string(svg1),
                  //       // SvgPicture.string('<svg width="6" height="11" viewBox="0 0 6 11" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M4.19058 5.80833L0.219581 9.73533C0.149795 9.80359 0.0943465 9.88511 0.0564913 9.97509C0.0186362 10.0651 -0.000862635 10.1617 -0.000862631 10.2593C-0.000862627 10.3569 0.0186362 10.4536 0.0564914 10.5436C0.0943465 10.6336 0.149795 10.7151 0.219581 10.7833C0.361433 10.9222 0.552056 11 0.750581 11C0.949106 11 1.13973 10.9222 1.28158 10.7833L5.78158 6.33433C5.91745 6.20009 5.99572 6.01824 5.99983 5.82729C6.00394 5.63633 5.93355 5.45128 5.80358 5.31133L1.28558 0.83133C1.14361 0.692677 0.953028 0.615055 0.754581 0.615055C0.556133 0.615055 0.365558 0.692677 0.223582 0.83133C0.153796 0.899592 0.0983477 0.981105 0.0604926 1.07109C0.0226374 1.16107 0.00313669 1.25771 0.00313669 1.35533C0.00313669 1.45295 0.0226374 1.54959 0.0604926 1.63957C0.0983477 1.72955 0.153796 1.81107 0.223582 1.87933L4.19058 5.80833Z" fill="#1A1818"/></svg>')),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                textHeader,
                style: TextStyle(
                  color: configTheme().textTheme.bodyMedium?.color,
                  fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.bodyLarge?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.bodyLarge?.fontWeight,
                ),
              ),
              SizedBox(height: 8.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    textData,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.35),
                      fontSize:
                      configTheme().primaryTextTheme.labelSmall?.fontSize,
                      fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                      fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    numCount,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                      configTheme().primaryTextTheme.labelMedium?.fontSize,
                      fontFamily: configTheme()
                          .primaryTextTheme
                          .labelMedium
                          ?.fontFamily,
                      fontWeight: configTheme()
                          .primaryTextTheme
                          .labelMedium
                          ?.fontWeight,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    textData1,
                    style: TextStyle(
                      color: configTheme()
                          .textTheme
                          .bodyMedium
                          ?.color
                          ?.withOpacity(0.35),
                      fontSize:
                      configTheme().primaryTextTheme.labelSmall?.fontSize,
                      fontFamily:
                      configTheme().primaryTextTheme.labelSmall?.fontFamily,
                      fontWeight:
                      configTheme().primaryTextTheme.labelSmall?.fontWeight,
                    ),
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    numCount1,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                      configTheme().primaryTextTheme.labelMedium?.fontSize,
                      fontFamily: configTheme()
                          .primaryTextTheme
                          .labelMedium
                          ?.fontFamily,
                      fontWeight: configTheme()
                          .primaryTextTheme
                          .labelMedium
                          ?.fontWeight,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Text(
                sumCount,
                style: TextStyle(
                  color: configTheme().textTheme.bodyMedium?.color,
                  fontSize: 24.sp,
                  // configTheme().primaryTextTheme.labelMedium?.fontSize,
                  fontFamily:
                  configTheme().primaryTextTheme.labelMedium?.fontFamily,
                  fontWeight:
                  configTheme().primaryTextTheme.labelMedium?.fontWeight,
                ),
              ),
              SizedBox(height: 16.h),
            ],
          ),
        ));
  }

  buildMRHistoryLoan() {
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return Container(
            height: Get.height,
            width: Get.width,
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  color: Colors.white,
                  height: 88.h,
                  child: Padding(
                    padding: EdgeInsets.only(top: 44.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          height: 50.h,
                          width: 50.w,
                          child: Center(
                              child: SvgPicture.string(
                                  '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3430_45429)"><path d="M14.25 18.5L7.75 12L14.25 5.5" stroke="#1A1818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g><defs><clipPath id="clip0_3430_45429"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                        ),
                        Text('จัดสินเชื่อ'),
                        Container(
                          height: 50.h,
                          width: 50.w,
                        )
                      ],
                    ),
                  ),
                ),
                Text('แนะนำเพื่อนไปแล้ว'),
                Text("5"),
                Divider(
                  height: 1,
                  thickness: 1,
                  color: Colors.black,
                ),
                Container(
                  height: 40.h,
                  width: 37.w,
                  child: Center(
                      child: SvgPicture.string(
                          '<svg width="38" height="41" viewBox="0 0 38 41" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M37.4993 18.4996C37.4993 8.28246 29.2168 0 18.9996 0C8.78246 0 0.5 8.28246 0.5 18.4996C0.5 27.9735 7.62163 35.7831 16.8033 36.8696L17.8519 38.6855L19.1184 40.8789L20.3849 38.6855L21.4517 36.8375C30.5095 35.6381 37.5 27.8854 37.5 18.4996H37.4993Z" fill="#792AFF" fill-opacity="0.08"/><path d="M17.5069 19.8426L17.0726 13.3418C16.991 12.0753 16.9502 11.1659 16.9502 10.6143C16.9502 9.86372 17.1469 9.27784 17.5404 8.85664C17.9339 8.43618 18.4521 8.22559 19.094 8.22559C19.8716 8.22559 20.3919 8.49448 20.6549 9.03299C20.9173 9.57151 21.0484 10.3468 21.0484 11.3605C21.0484 11.9573 21.0164 12.5643 20.9537 13.1793L20.37 19.8696C20.3066 20.666 20.1711 21.2767 19.9627 21.7015C19.7543 22.1271 19.4103 22.3391 18.9315 22.3391C18.4528 22.3391 18.1037 22.1337 17.9135 21.7219C17.7234 21.3102 17.5878 20.6835 17.5062 19.8426H17.5069ZM19.0139 28.7722C18.4615 28.7722 17.9799 28.5936 17.5689 28.2358C17.1571 27.8788 16.9517 27.3789 16.9517 26.7362C16.9517 26.1751 17.1484 25.6977 17.5419 25.3042C17.9354 24.9107 18.4171 24.714 18.9869 24.714C19.5568 24.714 20.0428 24.9107 20.4458 25.3042C20.848 25.6977 21.0499 26.1751 21.0499 26.7362C21.0499 27.3694 20.8466 27.8671 20.4392 28.2293C20.0319 28.5914 19.5568 28.7722 19.0146 28.7722H19.0139Z" fill="#792AFF" fill-opacity="0.15"/></svg>')),
                ),
                Text('คุณได้รับเงินแล้ว'),
                Spacer(),
                Container(
                  height: 76.h,
                  width: Get.width,
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: configTheme().primaryColor.withOpacity(0.08),
                        width: 1),
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Container(
                            child: SvgPicture.string(
                                '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3430_45422)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#995DFE" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3430_45422"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                            'แค่แนะนำเพื่อนครั้ง รับคะแนน 500 AAMP ทันที\nและลุ้นรับต่ออีก ถ้าเพื่อนของคุณเข้าร่วมกิจกรรม')
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                Container(
                  height: 76.h,
                  width: Get.width,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black, width: 1),
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                ),
                SizedBox(
                  height: 48.h,
                ),
              ],
            ),
          );
        });
  }
}
