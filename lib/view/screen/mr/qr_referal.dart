import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:ui';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'dart:ui' as ui;
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:share_plus/share_plus.dart';


class QrReferalPage extends StatefulWidget {
  // const QrReferalPage({super.key});

  final String id;
  QrReferalPage({required this.id});

  @override
  State<QrReferalPage> createState() => _QrReferalPageState();
}

class _QrReferalPageState extends State<QrReferalPage> {
  GlobalKey _globalKey = GlobalKey();
  final ProfileController profileController = Get.find<ProfileController>();
  final MRController mrClt = Get.put(MRController());
  String qrCodeData = '';


  checkCode(){
    if(mrClt.mrData.value.mr_id != null){
      qrCodeData = mrClt.mrData.value.mr_id.toString();
    }
    else{
      qrCodeData = profileController.profile.value.ref_code.toString();
    }
  }

  Future<void> saveQrCode() async {
    try {
      if (_globalKey.currentContext == null) {
        Get.snackbar('', 'ไม่พบ widget ที่มี key นี้');
        return;
      }

      RenderRepaintBoundary? boundary = _globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      ui.Image? image = await boundary?.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image?.toByteData(format: ui.ImageByteFormat.png);

      if (image == null || byteData == null) {
        Get.snackbar('', 'เกิดข้อผิดพลาดในการแปลงภาพ');
        return;
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double padding = 100; // ระยะห่างจากขอบซ้ายและขวา
      final double extraHeight = 200; // ความสูงเพิ่มขึ้นจากเดิม
      final double width = image.width.toDouble() + padding * 2;
      final double height = image.height.toDouble() + extraHeight; // เพิ่มพื้นที่สำหรับพื้นหลัง

      // วาดพื้นหลังสีขาวที่สูงขึ้น
      final paintBackground = Paint()..color = Colors.white;
      canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paintBackground);

      // วาด QR code บน canvas โดยมีระยะห่างจากซ้ายและขวา
      final paint = Paint();
      canvas.drawImage(image, Offset(padding, 150), paint); // เพิ่มความสูงจากด้านบน

      // วาดข้อความบน QR code
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: 'My QR Code MR',
          style: TextStyle(color: Colors.black, fontSize: 30),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = 50;
      textPainter.paint(canvas, Offset(textX, textY));

      // จบการวาดและแปลงภาพเป็น PNG
      final ui.Picture picture = recorder.endRecording();
      final ui.Image finalImage = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? finalByteData = await finalImage.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List finalPngBytes = finalByteData!.buffer.asUint8List();

      // บันทึกภาพในแกลเลอรี
      final result = await ImageGallerySaver.saveImage(finalPngBytes, quality: 100, name: 'QRCodeMR_${DateTime.now().millisecondsSinceEpoch}.png');
      if (result['isSuccess']) {
        Get.snackbar('', 'บันทึกสำเร็จแล้ว!');
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('บันทึกสำเร็จแล้ว!')),
        // );
      } else {
        Get.snackbar("", 'การบันทึกล้มเหลว!');
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('การบันทึกล้มเหลว!')),
        // );
      }
    } catch (e) {
      print('Error: $e');
      Get.snackbar('', 'เกิดข้อผิดพลาด: $e');
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(content: Text('เกิดข้อผิดพลาด: $e')),
      // );
    }
  }



  Future<void> shareQrCode() async {
    try {
      if (_globalKey.currentContext == null) {
        Get.snackbar('', 'ไม่พบ widget ที่มี key นี้');
        return;
      }

      RenderRepaintBoundary? boundary = _globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
      ui.Image? image = await boundary?.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image?.toByteData(format: ui.ImageByteFormat.png);

      if (image == null || byteData == null) {
        Get.snackbar('', 'เกิดข้อผิดพลาดในการแปลงภาพ');
        return;
      }

      Uint8List pngBytes = byteData.buffer.asUint8List();

      final ui.PictureRecorder recorder = ui.PictureRecorder();
      final Canvas canvas = Canvas(recorder);

      final double padding = 100; // ระยะห่างจากขอบซ้ายและขวา
      final double extraHeight = 200; // ความสูงเพิ่มขึ้นจากเดิม
      final double width = image.width.toDouble() + padding * 2;
      final double height = image.height.toDouble() + extraHeight; // เพิ่มพื้นที่สำหรับพื้นหลัง

      // วาดพื้นหลังสีขาวที่สูงขึ้น
      final paintBackground = Paint()..color = Colors.white;
      canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paintBackground);

      // วาด QR code บน canvas โดยมีระยะห่างจากซ้ายและขวา
      final paint = Paint();
      canvas.drawImage(image, Offset(padding, 150), paint); // เพิ่มความสูงจากด้านบน

      // วาดข้อความบน QR code
      final TextPainter textPainter = TextPainter(
        text: TextSpan(
          text: 'My QR Code MR',
          style: TextStyle(color: Colors.black, fontSize: 30),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      final double textX = (width - textPainter.width) / 2;
      final double textY = 50;
      textPainter.paint(canvas, Offset(textX, textY));

      // จบการวาดและแปลงภาพเป็น PNG
      final ui.Picture picture = recorder.endRecording();
      final ui.Image finalImage = await picture.toImage(width.toInt(), height.toInt());
      final ByteData? finalByteData = await finalImage.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List finalPngBytes = finalByteData!.buffer.asUint8List();

      // บันทึกภาพลงไฟล์ในโฟลเดอร์ชั่วคราว
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/qr_code.png';
      final file = await File(filePath).writeAsBytes(finalPngBytes);

      // แชร์ภาพจากไฟล์ที่บันทึก
      await Share.shareXFiles([XFile(file.path)], text: 'Check out my QR code!');
    } catch (error) {
      print('Error sharing QR code: $error');
    }
  }




  @override
  void initState() {
    // TODO: implement initState
    checkCode();
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    this.widget.id;
    return GetBuilder<MRController>(
        init: MRController(),
        builder: (mrController) => Scaffold(
                body: Container(
              color: Colors.white,
              height: Get.height,
              width: Get.width,
              child: Padding(
                padding: EdgeInsets.only(left: 24.w, right: 24.w),
                child: Column(
                  children: [
                    Container(
                      color: Colors.white,
                      height: 88.h,
                      child: Padding(
                        padding: EdgeInsets.only(top: 44.h),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                // Get.back();
                              },
                              child: Container(
                                height: 50.h,
                                width: 50.w,
                                child: Center(
                                    child: SvgPicture.string(
                                        '<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12 12.5L7 7.5M12 12.5L17 17.5M12 12.5L17 7.5M12 12.5L7 17.5" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')),
                              ),
                            ),
                            Text(
                              mrQr.tr,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontWeight,
                              ),
                            ),
                            Container(
                              height: 50.h,
                              width: 50.w,
                            )
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 113.h,

                    ),
                    Container(
                      height: 200.h,
                      width: 200.w,
                      child:RepaintBoundary(
                        key: _globalKey,
                        child: Container(
                          color: Colors.white, // เพิ่มพื้นหลังสีขาวเพื่อให้ QR code ชัดเจน
                          child: PrettyQr(
                            typeNumber: 3,
                            data: qrCodeData,
                            errorCorrectLevel: QrErrorCorrectLevel.M,
                            roundEdges: true,
                          ),
                        ),),),

                    // Container(
                    //   height: 200.h,
                    //   width: 200.w,
                    //   child:
                    //   PrettyQrView.data(
                    //     data: qrCodeData
                    //   ),
                    // ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Center(
                        child: Text(
                      mrQrSend.tr,
                      textAlign: TextAlign.center,
                    )),
                    Spacer(),
                    InkWell(
                      onTap:(){
                        Clipboard.setData(ClipboardData(text: qrCodeData));
                        Get.snackbar('Success', 'Save id success');
                      },
                      child: Container(
                          height: 50.h,
                          width: Get.width,
                          // color: configTheme().primaryColor,
                          decoration: BoxDecoration(
                            // color: configTheme().primaryColor.withOpacity(0.08),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: configTheme().colorScheme.onSecondary.withOpacity(0.08),
                              width: 1,
                            ),
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(left: 16.w, right: 19.w),
                            child: Row(
                              children: [
                                Text(
                                  mrID.tr,
                                  style: TextStyle(
                                    color:
                                        configTheme().textTheme.bodyMedium?.color,
                                    fontSize: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontSize,
                                    fontFamily: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontFamily,
                                    fontWeight: configTheme()
                                        .primaryTextTheme
                                        .bodySmall
                                        ?.fontWeight,
                                  ),
                                ),
                                Spacer(),
                                Row(
                                  children: [
                                    Text(qrCodeData,
                                      style: TextStyle(
                                        color:
                                            configTheme().colorScheme.onSecondary,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Container(
                                        child: appConfigService
                                                    .countryConfigCollection ==
                                                "aam"
                                            ? SvgPicture.string(
                                                '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 6.75V11.25C15 12.0784 14.3284 12.75 13.5 12.75H12M15 6.75V6.62132C15 6.22349 14.842 5.84197 14.5606 5.56066L11.6894 2.68934C11.408 2.40803 11.0265 2.25 10.6287 2.25H10.5M15 6.75H12C11.1716 6.75 10.5 6.07843 10.5 5.25V2.25M12 12.75H7.5C6.67157 12.75 6 12.0784 6 11.25V5.25M12 12.75V14.25C12 15.0784 11.3284 15.75 10.5 15.75H4.5C3.67157 15.75 3 15.0784 3 14.25V6.75C3 5.92157 3.67157 5.25 4.5 5.25H6M10.5 2.25H7.5C6.67157 2.25 6 2.92157 6 3.75V5.25" stroke="#995DFE" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>')
                                            : appConfigService
                                                        .countryConfigCollection ==
                                                    "rafco"
                                                ? SvgPicture.string(
                                                    '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 6.75V11.25C15 12.0784 14.3284 12.75 13.5 12.75H12M15 6.75V6.62132C15 6.22349 14.842 5.84197 14.5606 5.56066L11.6894 2.68934C11.408 2.40803 11.0265 2.25 10.6287 2.25H10.5M15 6.75H12C11.1716 6.75 10.5 6.07843 10.5 5.25V2.25M12 12.75H7.5C6.67157 12.75 6 12.0784 6 11.25V5.25M12 12.75V14.25C12 15.0784 11.3284 15.75 10.5 15.75H4.5C3.67157 15.75 3 15.0784 3 14.25V6.75C3 5.92157 3.67157 5.25 4.5 5.25H6M10.5 2.25H7.5C6.67157 2.25 6 2.92157 6 3.75V5.25" stroke="#EA1B23" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>')
                                                : SvgPicture.string(
                                                    '<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M15 6.75V11.25C15 12.0784 14.3284 12.75 13.5 12.75H12M15 6.75V6.62132C15 6.22349 14.842 5.84197 14.5606 5.56066L11.6894 2.68934C11.408 2.40803 11.0265 2.25 10.6287 2.25H10.5M15 6.75H12C11.1716 6.75 10.5 6.07843 10.5 5.25V2.25M12 12.75H7.5C6.67157 12.75 6 12.0784 6 11.25V5.25M12 12.75V14.25C12 15.0784 11.3284 15.75 10.5 15.75H4.5C3.67157 15.75 3 15.0784 3 14.25V6.75C3 5.92157 3.67157 5.25 4.5 5.25H6M10.5 2.25H7.5C6.67157 2.25 6 2.92157 6 3.75V5.25" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'))
                                  ],
                                ),
                              ],
                            ),
                          )),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),

                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        /// Save Qr
                        InkWell(
                          onTap: (){
                           saveQrCode();
                          },
                          child: Container(
                              height: 48.h,
                              width: 48.w,
                              decoration: BoxDecoration(
                                color: Color(0xFFF9F9F9),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Center(
                                  child: appConfigService
                                              .countryConfigCollection ==
                                          "aam"
                                      ? SvgPicture.string(
                                          '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12 16L8 12M12 16L16 12M12 16V4M4 20H20" stroke="#792AFF" stroke-opacity="0.75" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')
                                      : appConfigService
                                                  .countryConfigCollection ==
                                              "rafco"
                                          ? SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12 16L8 12M12 16L16 12M12 16V4M4 20H20" stroke="#22409A" stroke-opacity="0.5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>')
                                          : SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12 16L8 12M12 16L16 12M12 16V4M4 20H20" stroke="#6A7165" stroke-opacity="0.75" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'))),
                        ),
                        /// Share Qr
                        InkWell(
                          onTap: (){
                            shareQrCode();
                          },
                          child: Container(
                            height: 48.h,
                            width: 260.w,
                            decoration: BoxDecoration(
                              color:  configTheme().colorScheme.tertiary,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Center(
                              child: Text(
                                mrShare.tr,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleMedium
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 48.h,
                    )
                  ],
                ),
              ),
            )));
  }
}
