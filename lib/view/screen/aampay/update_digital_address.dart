import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/register/registerAddress.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/aam_pay/aampay_address.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import '../../componance/widgets/header_widgets/header_general.dart';

class UpdateDigitalAddress extends StatelessWidget {
  UpdateDigitalAddress({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return Stack(children: [
        Container(
          width: 327.w,
          margin: EdgeInsets.symmetric(vertical: 116.h, horizontal: 24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  AAMPay_address.buildUpdateAddressData(context, 'address');
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'บ้านเลขที่',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            aampayCtl.addressTextSave!.value,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),
              GestureDetector(
                onTap: () {
                  AAMPay_address.buildUpdateAddressData(context, 'moo');
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'หมู่',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            aampayCtl.mooTextSave!.value,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),
              GestureDetector(
                onTap: () {
                  AAMPay_address.buildUpdateProvince(context,
                      accountAddressProvince.tr, aampayCtl.controllerProvince);
                  // _buildUpdateAddress(context, accountAddressProvince.tr,
                  //     aampayCtl.controllerProvince);
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'จังหวัด',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            aampayCtl.controllerProvince.value.text,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),
              GestureDetector(
                onTap: () {
                  AAMPay_address.buildUpdateProvince(context,
                      accountAddressDistrict.tr, aampayCtl.controllerDistrict);
                  // _buildUpdateAddress(context, accountAddressDistrict.tr,
                  //     aampayCtl.controllerDistrict);
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'อำเภอ',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            aampayCtl.controllerDistrict.value.text,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0.30.h,
                color: Colors.black.withOpacity(0.2),
              ),
              GestureDetector(
                onTap: () {
                  AAMPay_address.buildUpdateProvince(
                      context,
                      accountAddressSubDistrict.tr,
                      aampayCtl.controllerSubDistrict);
                  // _buildUpdateAddress(context, accountAddressSubDistrict.tr,
                  //     aampayCtl.controllerSubDistrict);
                },
                child: Container(
                  width: 327.w,
                  height: 47.h,
                  color: Colors.transparent,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'ตำบล',
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,
                          ),
                        ),
                      ),
                      Container(
                        child: Obx(() {
                          return Text(
                            aampayCtl.controllerSubDistrict.value.text,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodySmall!
                                  .fontWeight,
                            ),
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 712.h),
          child: Obx(() {

            return PrimaryButton(
              title: "บันทึก",
              onPressed: () {
                // kycCtl.saveBookBank();
                if(aampayCtl.isUpdateAddress.value){
                  aampayCtl.updateAddress(context);
                }
              },
              // height: 712.h,
              buttonWidth: 327.w,
              backgroundColor: AppColors.AAMPurpleSolid,
              backgroundInactiveColor:
              configTheme().buttonTheme.colorScheme!.tertiary,
              isActive: aampayCtl.isUpdateAddress.value,
              textColor: Colors.white,
            );
          }),
        ),
        HeaderGeneral(
          title: 'ที่อยู่',
          firstIcon: SizedBox(
              width: 24.w,
              height: 24.h,
              child: SvgPicture.string(AppSvgImage.back_btn)),
          secondIcon: SizedBox(width: 24.w, height: 24.h),
          firstOnPressed: () {
            Get.back();
          },
          secondOnPressed: () {},
        ),
      ]);
    }));
  }


  _buildUpdateAddress(context, String type, controller) {
    final RegisterAddressController addressCtl =
    Get.find<RegisterAddressController>();
    final AAMPayController aamPayCtl = Get.find<AAMPayController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            height: 624.h,
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 375.w,
                  height: 48.h,
                  // color: Colors.teal,
                  // margin: EdgeInsets.only(top: 28.h),
                  child: Center(
                    child: Text(
                      type == accountAddressProvince.tr
                          ? accountAddress.tr
                          : type == accountAddressDistrict.tr
                          ? signUpChooseDistrict.tr
                          : accountAddressSubDistrict.tr,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize: 16.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w600,
                        // height: 0.19.h,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 375.w,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        width: 0.50.w,
                        strokeAlign: BorderSide.strokeAlignCenter,
                        color: const Color(0x191A1818),
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 524.h,
                  child: ListView.builder(
                      itemCount: type == accountAddressProvince.tr
                          ? addressCtl.citiesData.length
                          : type == accountAddressDistrict.tr
                          ? addressCtl.districtsData.length
                          : type == accountAddressSubDistrict.tr
                          ? addressCtl.subDistrictsData.length
                          : addressCtl.subDistrictsData.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: EdgeInsets.only(left: 24.w, right: 24.w),
                          child: InkWell(
                            onTap: () {
                              aamPayCtl.setDropdownAddress(
                                  context, type, index);
                              if (type == accountAddressProvince.tr) {
                              aamPayCtl.selectedProvince!.value =
                                    addressCtl.citiesData[index].cityNameLocal
                                        .toString();
                              aamPayCtl.selectedDistrict!.value =
                                '';
                              aamPayCtl
                                    .selectedSubDistrict!.value = '';
                              } else if (type == accountAddressDistrict.tr) {
                              aamPayCtl.selectedDistrict!.value =
                                    addressCtl.districtsData[index].districtNameLocal.toString();
                              aamPayCtl.selectedSubDistrict!.value = '';
                              } else if (type == accountAddressSubDistrict.tr) {
                              aamPayCtl
                                    .selectedSubDistrict!.value =
                                    addressCtl.subDistrictsData[index]
                                        .subDistrictNameLocal
                                        .toString();
                              }

                            },
                            child: Container(
                                width: 327.w,
                                height: 52.h,
                                decoration: BoxDecoration(
                                  // color: Colors.red,
                                  border: Border(
                                    bottom: BorderSide(
                                      color: const Color(0x191A1818),
                                      width: 0.5.w,
                                    ),
                                  ),
                                ),
                                child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      type == accountAddressProvince.tr
                                          ? addressCtl
                                          .citiesData[index].cityNameLocal
                                          .toString()
                                          : type == accountAddressDistrict.tr
                                          ? addressCtl.districtsData[index]
                                          .districtNameLocal
                                          .toString()
                                          : type ==
                                          accountAddressSubDistrict
                                              .tr
                                          ? addressCtl
                                          .subDistrictsData[index]
                                          .subDistrictNameLocal
                                          .toString()
                                          : addressCtl
                                          .subDistrictsData[index]
                                          .subDistrictNameLocal
                                          .toString(),
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 16.sp,
                                        fontFamily: 'NotoSansThai',
                                        fontWeight: FontWeight.w400,
                                        // height: 0.19.h,
                                      ),
                                    ))),
                          ),
                        );
                      }),
                )
              ],
            ),
          );
        });
  }

}
