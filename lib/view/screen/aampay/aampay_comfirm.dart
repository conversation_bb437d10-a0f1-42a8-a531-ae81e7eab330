import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../componance/themes/app_colors_gradient.dart';
import '../home/<USER>';

class AAMPayConfirmScreen extends StatelessWidget {
  AAMPayConfirmScreen({Key? key}) : super(key: key);
  final KYCController kycCtl = Get.find<KYCController>();
  @override
  Widget build(BuildContext context) {
    print('AAMPayConfirmScreen');
    print(Get.find<AAMPayController>().paypermonthall!.toJson().toString());
    return PopScope(
      canPop: false, // ป้องกันการ pop อัตโนมัติ
      onPopInvoked: (didPop) async {
        debugPrint('onPopInvoked');
      },
      child: Scaffold(
        body: Stack(
          children: [
            Container(
              decoration:
                  BoxDecoration(gradient: AppColorsGradient.appBgAAMGradient),
            ),
            Column(
              children: [
                Center(
                  child: Container(
                      width: 73.90.w,
                      height: 40.h,
                      margin: EdgeInsets.only(top: 81.0.h),
                      child:
                          Image.asset('assets/register/icon/regis_success.png')),
                ),
                SizedBox(
                  height: 24.h,
                ),
                Container(
                  width: 327.w,
                  height: 138.h,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: 56.h,
                        child: Text(
                          'ยืนยันข้อมูลขอสินเชื่อ\nเรียบร้อย',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .displayLarge!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .displayLarge!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .displayLarge!
                                .fontWeight,
                            height: 1.4.h,
                          ),
                        ),
                      ),
                      SizedBox(height: 10.h),
                      SizedBox(
                        width: 327.w,
                        child: Text(
                          'ได้รับข้อมูลการยืนยันเรียบร้อย เงินสินเชื่อของคุณ\nจะถูกโอนเข้าบัญชี ${kycCtl.formatBankAccountNumber(kycCtl.bookbankData.value.bookbank_number.toString())} ${kycCtl.bookbankData.value.bookbank_custName.toString()} \nภายใน 48 ชั่วโมงเวลาทำการ ขอบคุณค่ะ',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.****************),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodySmall!
                                .fontWeight,

                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    Get.to(HomeNavigator(), transition: Transition.fade, duration: Duration(milliseconds: 200));
                  },
                  child: Container(
                    width: 327.w,
                    height: 52.h,
                    margin: EdgeInsets.only(top: 429.0.h),
                    decoration: ShapeDecoration(
                      color: const Color(0xBF1A1818),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      'ตกลง',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontFamily: 'NotoSansThai',
                        fontWeight: FontWeight.w500,
                        height: 0.14.h,
                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
