import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/kyc/kyc.controller.dart';
import 'package:AAMG/view/componance/themes/app_colors.dart';
import 'package:AAMG/view/componance/widgets/aam_pay/aampay_componance.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:AAMG/view/screen/aampay/aampay_updateInfo.dart';
import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
// import 'package:currency_text_input_formatter/currency_text_input_formatter.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/aampay/aampay.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/AppLoading.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../home/<USER>';
import 'package:intl/intl.dart';

import '../kyc/bookbank.dart';

class AAMPAYFormScreen extends StatefulWidget {
  AAMPAYFormScreen({Key? key}) : super(key: key);

  @override
  State<AAMPAYFormScreen> createState() => _AAMPAYFormScreenState();
}

class _AAMPAYFormScreenState extends State<AAMPAYFormScreen> {
  // var format_decimal = CurrencyTextInputFormatter(
  //   NumberFormat.currency(
  //     locale: 'en_US',
  //     symbol: '',
  //     // decimalDigits: 2,
  //     customPattern: '###,###,###,###,###,###,###,###.##',
  //   ),
  // );

  var f = NumberFormat('###,###,###,###,###,###,###,###', 'en_US');
  var f_decimal = NumberFormat('###,###,###,###,###,###,###,###.##', 'en_US');
  final AAMPayController aampayCtl = Get.find<AAMPayController>();
  final KYCController kycCtl = Get.put(KYCController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () async {
      await aampayCtl.resetData();
      aampayCtl.setLoanAmountChoice();
      var chk = await AAMPAY_Componance.acceptAAMPAYPolicy(context);
      if (chk) {
        await aampayCtl.checkUpdateAddress(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          FocusScopeNode currentFocus = FocusScope.of(context);
          FocusScope.of(context).unfocus();
          FocusScope.of(context).requestFocus(FocusNode()); // ปิด keyboard

          if (!currentFocus.hasPrimaryFocus) {
            currentFocus.unfocus();
          }
        },
        child: GetBuilder<AAMPayController>(
            init: AAMPayController(),
            builder: (aampayCtl) {
              return Container(
                color: Colors.white,
                child: Stack(
                  children: [
                    SizedBox(
                      height: 106.h,
                      width: Get.width,
                      child: Padding(
                        padding: EdgeInsets.only(
                          top: 60.h,
                          left: 12.w,
                          right: 12.w,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () async {
                                var chk =
                                    await AAMPAY_Componance.buildCancleContract(
                                        context);
                                // if (chk) {
                                //
                                // }
                                //todo reset data
                                await aampayCtl.resetData();
                                Get.back();
                              },
                              child: SizedBox(
                                height: 50.h,
                                width: 50.w,
                                // color: Colors.red,
                                child: Center(
                                  child: SvgPicture.string(
                                    AppSvgImage.back_btn,
                                  ),
                                ),
                              ),
                            ),
                            Text(
                              "เอเอเอ็ม เปย์",
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .titleMedium
                                    ?.fontWeight,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                AAMPAY_Componance.buildInfoContract(context);
                              },
                              child: Container(
                                  height: 50.h,
                                  width: 50.w,
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      scale: 2,
                                      image: AssetImage(
                                          'assets/register/icon/Information.png'),
                                    ),
                                  )),
                            )
                          ],
                        ),
                      ),
                    ),
                    _buildLoanForm(context),
                    aampayCtl.isCalculating.value
                        ? AppLoading.loading(context)
                        : const SizedBox(),
                  ],
                ),
              );
            }),
      ),
    );
  }

  Widget _buildLoanForm(context) {
    final KYCController kycCtl = Get.find<KYCController>();
    return GetBuilder<AAMPayController>(
        builder: (aampayCtl) {
      return Container(
        color: Colors.white,
        width: Get.width,
        // height: Get.height,
        margin: EdgeInsets.only(
          top: 118.h,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildLoanAmount(context),
            SizedBox(
              height: 12.h,
            ),
            //TODO dropdown
            _buildDropDownPeriod(context),
            SizedBox(
              height: 12.h,
            ),
            // todo คำนวณเบื้องต้น
            _arCardPreview(context),
            SizedBox(
              height: 12.h,
            ),
            //todo ค่างวดต่อเดือน
            _buildMonthlyInstallment(context),
            SizedBox(
              height: 12.h,
            ),
            _buildBookbankDetail(context),
            SizedBox(
              height: 12.h,
            ),
            GetBuilder<KYCController>(builder: (kycCtl) {
              return PrimaryButton(
                title: "ขอรับสินเชื่อ",
                onPressed: () {
                  if (aampayCtl.selectedPeriod.value.isNotEmpty &&
                      aampayCtl.loan_amount.value.text.isNotEmpty &&
                      aampayCtl.paypermonth!.value.isNotEmpty &&
                      kycCtl.bookbankData.value.bookbank_number.toString() !=
                          "null" &&
                      kycCtl.bookbankData.value.bookbank_name.toString() !=
                          "null") {
                    AAMPAY_Componance.buildConfirmContract(context);
                  }
                },
                buttonWidth: 327.w,
                backgroundColor: AppColors.AAMPurpleSolid.withOpacity(1.0),
                backgroundInactiveColor:
                configTheme().buttonTheme.colorScheme!.tertiary,
                textColor: configTheme().buttonTheme.colorScheme!.onSecondary,
                isActive: aampayCtl.selectedPeriod.value.isNotEmpty &&
                    aampayCtl.loan_amount.value.text.isNotEmpty &&
                    aampayCtl.paypermonth!.value.isNotEmpty &&
                    kycCtl.bookbankData.value.bookbank_number.toString() !=
                        "null" &&
                    kycCtl.bookbankData.value.bookbank_name.toString() !=
                        "null"
                    ? true
                    : false,
              );
            })
            // PrimaryButton(
            //   title: "ขอรับสินเชื่อ",
            //   onPressed: () {
            //     if (aampayCtl.selectedPeriod.value.isNotEmpty &&
            //         aampayCtl.loan_amount.value.text.isNotEmpty &&
            //         aampayCtl.paypermonth!.value.isNotEmpty &&
            //         kycCtl.bookbankData.value.bookbank_number!.isNotEmpty &&
            //         kycCtl.bookbankData.value.bookbank_name!.isNotEmpty) {
            //       AAMPAY_Componance.buildConfirmContract(context);
            //     }
            //   },
            //   buttonWidth: 327.w,
            //   backgroundColor: AppColors.AAMPurpleSolid.withOpacity(1.0),
            //   backgroundInactiveColor:
            //       configTheme().buttonTheme.colorScheme!.tertiary,
            //   textColor: configTheme().buttonTheme.colorScheme!.onSecondary,
            //   isActive: aampayCtl.selectedPeriod.value.isNotEmpty &&
            //           aampayCtl.loan_amount.value.text.isNotEmpty &&
            //           aampayCtl.paypermonth!.value.isNotEmpty &&
            //           kycCtl.bookbankData.value.bookbank_number!.isNotEmpty &&
            //           kycCtl.bookbankData.value.bookbank_name!.isNotEmpty
            //       ? true
            //       : false,
            // ),
          ],
        ),
      );
    });
  }

  Widget _buildLoanAmount(context) {
    final contractCtl = Get.find<ContractListController>();
    return GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return Container(
        width: 327.w,
        // height: 152.h,
        padding: EdgeInsets.all(14.h),
        decoration: ShapeDecoration(
          color: AppColors.grey_01,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'วงเงินที่ได้รับ',
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium!.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                        height: 0,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      '฿${f.format(int.parse(Get.find<ContractListController>().contractDigi.value.remain_loan.toString()))}',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color: configTheme().textTheme.bodyMedium!.color,
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // SizedBox(
            //   height: 1.h,
            // ),
            SizedBox(
              height: 22.h,
              child: Text(
                'กรอกจำนวนเงินที่ต้องการ',
                style: TextStyle(
                  color: configTheme()
                      .textTheme
                      .bodyMedium!
                      .color!
                      .withOpacity(0.35),
                  fontSize: configTheme().primaryTextTheme.labelSmall!.fontSize,
                  fontFamily:
                      configTheme().primaryTextTheme.labelSmall!.fontFamily,
                  fontWeight:
                      configTheme().primaryTextTheme.labelSmall!.fontWeight,
                ),
              ),
            ),
            SizedBox(
              width: 299.w,
              height: 32.h,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    height: 32.h,
                    width: 15.w,
                    child: Obx(() {
                      return Text(
                        '฿',
                        style: TextStyle(
                          color: aampayCtl.loan_amount.value.text.isEmpty ||
                                  aampayCtl.loan_amount.value.text == '0'
                              ? configTheme().textTheme.bodyMedium!.color
                              : configTheme().colorScheme.primary,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontWeight,
                        ),
                      );
                    }),
                  ),
                  Container(
                    height: 32.h,
                    width: 250.w,
                    alignment: Alignment.center,
                    child: TextFormField(
                        scrollPadding: EdgeInsets.zero,
                        textAlignVertical: TextAlignVertical.center,
                        textAlign: TextAlign.start,
                        inputFormatters: [
                          CurrencyTextInputFormatter.currency(
                            decimalDigits: 0,
                            symbol: '',
                            // maxValue: int.parse(contractCtl
                            //     .contractDigi.value.remain_loan
                            //     .toString())
                          )
                        ],
                        showCursor: true,
                        cursorWidth: 2.w,
                        cursorColor: configTheme().colorScheme.primary,
                        cursorOpacityAnimates: true,
                        keyboardType: TextInputType.number,
                        minLines: 1,
                        autofocus: false,
                        readOnly: false,
                        focusNode: aampayCtl.loanAmountFocus,
                        controller: aampayCtl.loan_amount.value,
                        decoration: InputDecoration(
                            border: InputBorder.none,
                            isCollapsed: true,
                            contentPadding: EdgeInsets.zero,
                            labelStyle: TextStyle(
                              color: configTheme()
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.3),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontWeight,
                            ),
                            hintText: '0',
                            hintStyle: TextStyle(
                              color: configTheme()
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.2),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontSize,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontWeight,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .displayMedium!
                                  .fontFamily,
                            )),
                        style: TextStyle(
                          color: aampayCtl.loan_amount.value.text.isEmpty ||
                                  aampayCtl.loan_amount.value.text == '0'
                              ? configTheme()
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.3)
                              : configTheme().colorScheme.primary,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .displayMedium!
                              .fontWeight,
                        ),
                      onTapOutside: (value) {
                          print("onTapOutside");
                          aampayCtl.setLoanAmount(aampayCtl.loan_amount.value.text);
                      },
                      onFieldSubmitted: (value) {
                          print("onFieldSubmitted");
                        aampayCtl.setLoanAmount(value);
                      },
                    ),
                  ),
                  SizedBox(
                    width: 7.w,
                  ),
                  aampayCtl.loan_amount.value.text.isEmpty ||
                          aampayCtl.loan_amount.value.text == '0'
                      ? const SizedBox()
                      : GestureDetector(
                          onTap: () {
                            aampayCtl.calculateLoan(0, 'delete');
                          },
                          child: SizedBox(
                            width: 24.w,
                            height: 24.h,
                            child: SvgPicture.string(AppSvgImage.delete),
                          ),
                        ),
                ],
              ),
            ),
            Container(
              alignment: Alignment.topCenter,
              height: 1.h,
              width: 299.w,
              child: Obx(() {
                return Divider(
                  color: aampayCtl.loan_amount.value.text.toString() == "0"
                      ? const Color(0xFFE5E5E5)
                      : configTheme().colorScheme.primary,
                  thickness: 1.w,
                );
              }),
            ),
            SizedBox(
              height: 10.h,
            ),

            SizedBox(
              width: 299.w,
              height: 32.h,
              child: ListView.builder(
                  padding: EdgeInsets.zero,
                  scrollDirection: Axis.horizontal,
                  itemCount: aampayCtl.loanAmountList.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        aampayCtl.calculateLoan(
                            aampayCtl.loanAmountList[index], 'plus');
                      },
                      child: Container(
                        width: 66.04.w,
                        margin: EdgeInsets.only(right: 10.w),
                        height: double.infinity,
                        decoration: ShapeDecoration(
                          color: configTheme().colorScheme.background,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                                width: 1,
                                color: configTheme()
                                    .buttonTheme
                                    .colorScheme!
                                    .secondary
                                    .withOpacity(0.15)),
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 61,
                              height: 16.h,
                              child: Text(
                                f.format(aampayCtl.loanAmountList[index]),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildDropDownPeriod(context) {
    return GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return SizedBox(
        width: 327.w,
        height: 54.h,
        // decoration: ShapeDecoration(
        //   shape: RoundedRectangleBorder(
        //     side: BorderSide(width: 1.w, color: const Color(0x141A1818)),
        //     borderRadius: BorderRadius.circular(12.r),
        //   ),
        // ),
        child: InkWell(
          onTap: () async {
            if (aampayCtl.requestloanCal!.value >= 1000 &&
                aampayCtl.requestloanCal!.value != 0) {
              await aampayCtl.checkdrop(context);
              if (aampayCtl.openDropdown!.value) {
                aampayCtl.setDropdownActive(true);
                _buildBottomSheetPeriod(context);
              }
            }
          },
          child: Stack(
            children: [
              Container(
                width: 327.w,
                height: 58.h,
                padding: const EdgeInsets.symmetric(horizontal: 14),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                        width: 1.w,
                        color: aampayCtl.isActivePeriod!.value
                            ? const Color(0x19792AFF).withOpacity(1.0)
                            : const Color(0x19792AFF)),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 22.h,
                      child: Text(
                        'ระยะเวลาผ่อนชำระ',
                        style: TextStyle(
                          color: const Color(0x7F1A1818),
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodySmall
                              ?.fontWeight,
                        ),
                      ),
                    ),
                    Container(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          aampayCtl.selectedPeriod.value.isNotEmpty
                              ? _listSelectedPeriods(context,
                                  '${aampayCtl.dropdownchoice![aampayCtl.selectedPeriodIndex.value]}')
                              : Row(
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        'เลือกระยะเวลา',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                          fontSize: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontSize,
                                          fontFamily: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontFamily,
                                          fontWeight: configTheme()
                                              .primaryTextTheme
                                              .bodyMedium
                                              ?.fontWeight,
                                          // height: 0.14.h,
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 4.w),
                                    SizedBox(
                                      width: 22.w,
                                      height: 22.h,
                                      child: Center(
                                        child: SvgPicture.string(
                                          aampayCtl.isActivePeriod.value
                                              ? '<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M10.8083 10.3094L14.7353 14.2804C14.8036 14.3502 14.8851 14.4057 14.9751 14.4435C15.0651 14.4814 15.1617 14.5009 15.2593 14.5009C15.3569 14.5009 15.4536 14.4814 15.5436 14.4435C15.6336 14.4057 15.7151 14.3502 15.7833 14.2804C15.9222 14.1386 16 13.9479 16 13.7494C16 13.5509 15.9222 13.3603 15.7833 13.2184L11.3343 8.71842C11.2001 8.58255 11.0182 8.50428 10.8273 8.50017C10.6363 8.49606 10.4513 8.56645 10.3113 8.69642L5.83133 13.2144C5.69268 13.3564 5.61505 13.547 5.61505 13.7454C5.61505 13.9439 5.69268 14.1344 5.83133 14.2764C5.89959 14.3462 5.9811 14.4017 6.07109 14.4395C6.16107 14.4774 6.25771 14.4969 6.35533 14.4969C6.45295 14.4969 6.54959 14.4774 6.63957 14.4395C6.72955 14.4017 6.81107 14.3462 6.87933 14.2764L10.8083 10.3094Z" fill="#995DFE"/></svg>'
                                              : '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                                          allowDrawingOutsideViewBox: true,
                                          color: configTheme()
                                              .textTheme
                                              .bodyMedium
                                              ?.color,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _arCardPreview(context) {
    return GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return Container(
        width: 327.w,
        // height: 106.h,
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: BorderSide(width: 1.w, color: const Color(0x19792AFF)),
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'จำนวนเงินที่จะได้รับ',
                      style: TextStyle(
                        color: configTheme()
                            .textTheme
                            .bodyMedium!
                            .color!
                            .withOpacity(0.5),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Obx(() {
                      return Text(
                        '฿${aampayCtl.loan_amount.value.text == '0' ? "0" : aampayCtl.loan_amount.value.text}',
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium!.color,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontWeight,
                        ),
                      );
                    }),
                  )
                ],
              ),
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'อัตราดอกเบี้ยลดต้นลดลดดอก/ปี',
                      style: TextStyle(
                        color: configTheme()
                            .textTheme
                            .bodyMedium!
                            .color!
                            .withOpacity(0.5),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Obx(() {
                      return Text(
                        '${aampayCtl.Fee!.value}%',
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: AppColors.AAMPurpleSolid,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontWeight,
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'ค่าอากรแสตมป์',
                      style: TextStyle(
                        color: configTheme()
                            .textTheme
                            .bodyMedium!
                            .color!
                            .withOpacity(0.5),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Obx(() {
                      return Text(
                        '฿${aampayCtl.tax_contract.value}',
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: aampayCtl.loan_amount.value.text == '0' ||
                                  aampayCtl.selectedPeriod.value
                                      .toString()
                                      .isEmpty ||
                                  aampayCtl.interest_remain!.value.isEmpty
                              ? configTheme()
                                  .textTheme
                                  .bodyMedium!
                                  .color!
                                  .withOpacity(1.0)
                              : AppColors.AAMPurpleSolid,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontWeight,
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'ดอกเบี้ยโดยรวม',
                      style: TextStyle(
                        color: configTheme()
                            .textTheme
                            .bodyMedium!
                            .color!
                            .withOpacity(0.5),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Obx(() {
                      return Text(
                        '฿${aampayCtl.loan_amount.value.text == '0' || aampayCtl.selectedPeriod.value.toString().isEmpty || aampayCtl.interest_remain!.value.isEmpty ? "0.00" : f.format(int.parse(aampayCtl.interest_remain!.value.toString()))
                        // (int.parse(
                        //   aampayCtl.interest_remain!.value.toString()))
                        }',
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: aampayCtl.loan_amount.value.text == '0' ||
                                  aampayCtl.selectedPeriod.value
                                      .toString()
                                      .isEmpty ||
                                  aampayCtl.interest_remain!.value.isEmpty
                              ? configTheme()
                                  .textTheme
                                  .bodyMedium!
                                  .color!
                                  .withOpacity(1.0)
                              : AppColors.AAMPurpleSolid,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium!
                              .fontWeight,
                        ),
                      );
                    }),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
          ],
        ),
      );
    });
  }

  Widget _buildMonthlyInstallment(context) {
    return GetBuilder<AAMPayController>(builder: (aampayCtl) {
      return GestureDetector(
      onTap: (){
        AAMPAY_Componance.buildViewPeriodsAAMPayArCard(context,aampayCtl.selectedPeriod.value,aampayCtl.paypermonthall!.value);
      },
        child: Container(
          width: 327.w,
          height: 50.h,
          padding: EdgeInsets.symmetric(horizontal: 14.w),
          decoration: ShapeDecoration(
            shape: RoundedRectangleBorder(
              side: const BorderSide(width: 1, color: Color(0x19792AFF)),
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: SizedBox(
            width: 299.w,
            height: 22.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: 22.h,
                  child: Text(
                    'ยอดชำระรายเดือน',
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium!.color,
                      fontSize:
                          configTheme().primaryTextTheme.bodyMedium!.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.bodyMedium!.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.bodyMedium!.fontWeight,
                    ),
                  ),
                ),
                SizedBox(
                  height: 22.h,
                  child: Obx(() {
                    return Text(
                      aampayCtl.loan_amount.value.text == '0' ||
                              aampayCtl.selectedPeriod.value == "" ||
                              aampayCtl.paypermonth!.value.isEmpty
                          ? '฿0.00'
                          : '฿${f.format(int.parse(aampayCtl.paypermonth!.value))} X${aampayCtl.selectedPeriod.value}',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color: aampayCtl.loan_amount.value.text == '0' ||
                                aampayCtl.selectedPeriod.value == "" ||
                                aampayCtl.paypermonth!.value.isEmpty
                            ? configTheme().textTheme.bodyMedium!.color
                            : AppColors.AAMPurpleSolid,
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily:
                            configTheme().primaryTextTheme.bodyMedium!.fontFamily,
                        fontWeight:
                            configTheme().primaryTextTheme.bodyMedium!.fontWeight,
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildBookbankDetail(context) {
    return GetBuilder<KYCController>(builder: (kycCtl) {
      return Container(
        width: 327.w,
        height: 159.h,
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: const BorderSide(width: 1, color: Color(0x19792AFF)),
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 299.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text: 'ข้อมูลบัญชีธนาคาร\n',
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontWeight,
                            ),
                          ),
                          TextSpan(
                            text: 'สำหรับโอนเงินเข้า',
                            style: TextStyle(
                              color: const Color(0x7F1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .labelSmall!
                                  .fontWeight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      print('edit bank');
                      Get.to(() => const AAMPayUpdateInfo());
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            width: 20.w,
                            height: 20.h,
                            clipBehavior: Clip.antiAlias,
                            decoration: const BoxDecoration(),
                            child: SvgPicture.string(
                              '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_420_15514)"><path d="M17.5 10V14.5C17.5 16.15 16.15 17.5 14.5 17.5H5.49998C3.84998 17.5 2.5 16.15 2.5 14.5V5.50001C2.5 3.85001 3.84998 2.5 5.49998 2.5H10" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M15.4171 7.6418L12.0004 11.0584C11.8504 11.2084 11.6754 11.2918 11.4671 11.3085L9.42541 11.5001C8.89208 11.5501 8.44209 11.0918 8.49209 10.5585L8.67539 8.57513C8.69206 8.3668 8.77543 8.18345 8.92543 8.04178L12.3754 4.5918L15.4171 7.6418V7.6418Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/><path d="M17.25 5.8166L15.4167 7.64161L12.375 4.5916L14.2 2.7666C14.5333 2.43327 15.0834 2.43327 15.4167 2.7666L17.25 4.5916C17.575 4.92494 17.575 5.48327 17.25 5.8166V5.8166Z" stroke="#1A1818" stroke-width="1.5" stroke-linejoin="round"/></g><defs><clipPath id="clip0_420_15514"><rect width="20" height="20" fill="white"/></clipPath></defs></svg>',
                            ),
                          ),
                          SizedBox(width: 6.w),
                          Text(
                            'แก้ไข',
                            style: TextStyle(
                              color: const Color(0xFF1A1818),
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium!
                                  .fontWeight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
            Container(
              width: 299.w,
              decoration: const ShapeDecoration(
                shape: RoundedRectangleBorder(
                  side: BorderSide(
                    width: 1,
                    strokeAlign: BorderSide.strokeAlignCenter,
                    color: Color(0x0C792AFF),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 6),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'ธนาคาร',
                      style: TextStyle(
                        color: const Color(0x7F1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      kycCtl.bookbankData.value.bookbank_name ?? '',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'ชื่อบัญชี',
                      style: TextStyle(
                        color: const Color(0x7F1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      kycCtl.bookbankData.value.bookbank_custName ?? '',
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.h),
            SizedBox(
              width: 299.w,
              height: 22.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      'เลขบัญชี',
                      style: TextStyle(
                        color: const Color(0x7F1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodySmall!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodySmall!
                            .fontWeight,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 22.h,
                    child: Text(
                      kycCtl.formatBankAccountNumber(kycCtl
                              .bookbankData.value.bookbank_number
                              .toString() ??
                          ''),
                      textAlign: TextAlign.right,
                      style: TextStyle(
                        color: const Color(0xFF1A1818),
                        fontSize:
                            configTheme().primaryTextTheme.bodyMedium!.fontSize,
                        fontFamily: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontFamily,
                        fontWeight: configTheme()
                            .primaryTextTheme
                            .bodyMedium!
                            .fontWeight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  _buildBottomSheetPeriod(context) {
    final aampayCtl = Get.find<AAMPayController>();
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.white.withOpacity(1.0),
        barrierColor: Colors.transparent,
        builder: (context) {
          return Container(
            width: Get.width,
            height: 288.h,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.background,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              shadows: const [
                BoxShadow(
                  color: Color(0x33000000),
                  blurRadius: 50,
                  offset: Offset(0, 4),
                  spreadRadius: 0,
                )
              ],
            ),
            child: Column(
              children: [
                Container(
                  height: 34.h,
                  alignment: Alignment.center,
                  child: SvgPicture.string(AppSvgImage.close_bar),
                ),
                SizedBox(
                  height: 26.h,
                ),
                Container(
                    margin: EdgeInsets.only(left: 20.5.w, right: 20.5.w),
                    child: StaggeredGrid.count(
                      crossAxisCount: 2,
                      mainAxisSpacing: 16.h,
                      crossAxisSpacing: 16.w,
                      children: List.generate(
                        aampayCtl.dropdownchoice!.length,
                        (index) {
                          return serviceMenuCard(
                              context, aampayCtl.dropdownchoice![index], index);
                        },
                      ),
                    )),
              ],
            ),
          );
        });
  }

  serviceMenuCard(context, title, int index) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        Get.find<AAMPayController>()
            .selectPeriod(context, title.toString(), index);
      },
      child: Container(
        width: 159.w,
        height: 56.h,
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            side: const BorderSide(width: 1, color: Color(0x141A1818)),
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        alignment: Alignment.center,
        child: Text(
          title + " " + menuGetLoanMoth.tr,
          style: TextStyle(
            color: configTheme().textTheme.bodyMedium?.color,
            fontSize: configTheme().primaryTextTheme.bodyLarge?.fontSize,
            fontFamily: configTheme().primaryTextTheme.bodyLarge?.fontFamily,
            fontWeight: configTheme().primaryTextTheme.bodyLarge?.fontWeight,
          ),
        ),
      ),
    );
  }

  _listSelectedPeriods(context, String title) {
    return Container(
      width: 97.w,
      height: 34.h,
      padding: EdgeInsets.only(left: 14.w, right: 6.w),
      decoration: ShapeDecoration(
        color: const Color(0xFF995DFE),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        shadows: [
          const BoxShadow(
            color: Color(0x72792AFF),
            blurRadius: 10,
            offset: Offset(2, 4),
            spreadRadius: 0,
          )
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 22.h,
            child: Text(
              "$title ${menuGetLoanMoth.tr}",
              style: TextStyle(
                color: Colors.white,
                fontSize: configTheme().primaryTextTheme.bodyMedium?.fontSize,
                fontFamily:
                    configTheme().primaryTextTheme.bodyMedium?.fontFamily,
                fontWeight:
                    configTheme().primaryTextTheme.bodyMedium?.fontWeight,
              ),
            ),
          ),
          SizedBox(
            width: 22.w,
            height: 22.h,
            child: Center(
              child: SvgPicture.string(
                '<svg width="11" height="6" viewBox="0 0 11 6" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M5.19167 4.19058L1.26467 0.219581C1.19641 0.149795 1.1149 0.0943465 1.02491 0.0564914C0.93493 0.0186362 0.838293 -0.000862598 0.740672 -0.000862598C0.643051 -0.000862598 0.546413 0.0186362 0.45643 0.0564914C0.366448 0.0943465 0.284934 0.149795 0.216671 0.219581C0.0777825 0.361433 0 0.552056 0 0.750581C0 0.949106 0.0777825 1.13973 0.216671 1.28158L4.66567 5.78158C4.79991 5.91745 4.98176 5.99572 5.17271 5.99983C5.36367 6.00394 5.54872 5.93355 5.68867 5.80358L10.1687 1.28558C10.3073 1.14361 10.3849 0.953029 10.3849 0.754581C10.3849 0.556133 10.3073 0.365558 10.1687 0.223583C10.1004 0.153796 10.0189 0.0983481 9.92891 0.060493C9.83893 0.0226378 9.74229 0.00313711 9.64467 0.00313711C9.54705 0.00313711 9.45041 0.0226378 9.36043 0.060493C9.27045 0.0983481 9.18893 0.153796 9.12067 0.223583L5.19167 4.19058Z" fill="#1A1818"/></svg>',
                allowDrawingOutsideViewBox: true,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
