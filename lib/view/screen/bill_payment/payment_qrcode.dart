import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:AAMG/view/screen/bill_payment/payment_onProcess.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';

import '../../../controller/bill_payment/billPayment.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../../service/AppService.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/theme.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/animation/shimmer_effect.dart';
import '../../componance/widgets/app_pop_up/uploadImg_pop_up.dart';
import '../../componance/widgets/dashLine.dart';
import '../../componance/widgets/header_widgets/header_general.dart';
import 'payment_success.dart';

class PaymentQrcode extends StatelessWidget {
  PaymentQrcode({super.key});

  final ContractListController contractCtl = Get.find<ContractListController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GetBuilder<BillPaymentController>(
              // autoRemove: true,
              builder: (billCtl) {
                return Container(
                  height: Get.height,
                  width: Get.width,
                  padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 106.h),
                  child: Stack(
                    children: [
                      Container(
                        height: 506.h,
                        width: Get.width,
                        decoration: ShapeDecoration(
                          color: Colors.white,
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                                width: 1.w,
                                color: configTheme()
                                    .appBarTheme
                                    .titleTextStyle!
                                    .color!
                                    .withOpacity(0.08)),
                            borderRadius: BorderRadius.circular(14.r),
                          ),
                        ),
                        margin: EdgeInsets.only(top: 14.h),
                        child: Container(
                          margin: EdgeInsets.only(top: 14.h),
                          child: Column(
                            children: <Widget>[
                              Container(
                                margin: EdgeInsets.symmetric(horizontal: 14.w),
                                child: Column(
                                  children: <Widget>[
                                    //TODO ชื่อสัญญา
                                    SizedBox(
                                      height: 42.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: <Widget>[
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Text(
                                                    homeNoContract.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                Get.find<ContractListController>()
                                                    .contractList
                                                    .length ==
                                                    0
                                                    ? AnimatedShimmer(
                                                    width: 100.w):
                                                SizedBox(
                                                  height: 18.h,
                                                  child: Text(
                                                    contractCtl
                                                        .contractList[Get.find<
                                                                BillPaymentController>()
                                                            .selectedContractIndex
                                                            .value]
                                                        .ctt_code
                                                        .toString(),
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          // TODO ประเภทสัญญา
                                          SizedBox(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                Get.find<ContractListController>()
                                                    .contractList
                                                    .length ==
                                                    0
                                                    ? AnimatedShimmer(
                                                    width: 100.w):
                                                SizedBox(
                                                  child: SizedBox(
                                                    height: 24.h,
                                                    child: Text(
                                                      // checkFormatGuarunteetype(
                                                      //     contractCtl
                                                      //         .contractList[Get.find<
                                                      //                 BillPaymentController>()
                                                      //             .selectedContractIndex
                                                      //             .value]
                                                      //         .guarantee_type_name
                                                      //         .toString()),
                                                      contractCtl
                                                          .contractList[Get.find<
                                                          BillPaymentController>()
                                                          .selectedContractIndex
                                                          .value]
                                                          .ctt_code
                                                          .toString()
                                                          .substring(6, 8) ==
                                                          "DT"
                                                          ? "เอเอเอ็ม เปย์":
                                                      contractCtl
                                                          .contractList[Get.find<
                                                          BillPaymentController>()
                                                          .selectedContractIndex
                                                          .value]
                                                          .guarantee_type
                                                          .toString() ==
                                                          "1"
                                                          ? contractCtl.guaranteeList[0]
                                                          : contractCtl
                                                          .contractList[Get.find<
                                                          BillPaymentController>()
                                                          .selectedContractIndex
                                                          .value]
                                                          .guarantee_type
                                                          .toString() ==
                                                          "2"
                                                          ? contractCtl.guaranteeList[1]
                                                          : appConfigService
                                                          .countryConfigCollection ==
                                                          'aam' ||
                                                          appConfigService
                                                              .countryConfigCollection ==
                                                              'rafco' &&
                                                              contractCtl
                                                                  .contractList[Get
                                                                  .find<
                                                                  BillPaymentController>()
                                                                  .selectedContractIndex
                                                                  .value]
                                                                  .guarantee_type
                                                                  .toString() ==
                                                                  "3"
                                                          ? contractCtl.guaranteeList[2]
                                                          : contractCtl.guaranteeList[3],
                                                      style: TextStyle(
                                                        color: configTheme()
                                                            .textTheme
                                                            .bodyMedium
                                                            ?.color
                                                            ?.withOpacity(1),
                                                        fontSize: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontSize,
                                                        fontFamily: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontFamily,
                                                        fontWeight: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontWeight,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 12.w,
                                                ),
                                                Get.find<ContractListController>()
                                                    .contractList
                                                    .length ==
                                                    0
                                                    ? Container(
                                                    width: 100.w):
                                                Container(
                                                  alignment:
                                                      Alignment.centerRight,
                                                  height: 34.h,
                                                  width: 34.w,
                                                  child: SvgPicture.string(
                                                    contractCtl
                                                        .contractList[Get.find<
                                                        BillPaymentController>()
                                                        .selectedContractIndex
                                                        .value]
                                                        .ctt_code
                                                        .toString()
                                                        .substring(6, 8) ==
                                                        "DT"
                                                        ? AppSvgImage.icon_aampay:
                                                    contractCtl
                                                                .contractList[Get.find<
                                                                        BillPaymentController>()
                                                                    .selectedContractIndex
                                                                    .value]
                                                                .guarantee_type
                                                                .toString() ==
                                                            "1"
                                                        ? contractCtl
                                                                .guaranteeImgList![
                                                            0]
                                                        : contractCtl
                                                                    .contractList[Get.find<BillPaymentController>()
                                                                        .selectedContractIndex
                                                                        .value]
                                                                    .guarantee_type
                                                                    .toString() ==
                                                                "2"
                                                            ? contractCtl
                                                                    .guaranteeImgList![
                                                                1]
                                                            : contractCtl
                                                                        .contractList[Get.find<BillPaymentController>()
                                                                            .selectedContractIndex
                                                                            .value]
                                                                        .guarantee_type
                                                                        .toString() ==
                                                                    "3"
                                                                ? contractCtl
                                                                        .guaranteeImgList![
                                                                    2]
                                                                : contractCtl
                                                                    .guaranteeImgList![3],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    DashedLine(
                                      dashWidth: 3.0, // ความยาว dash
                                      dashHeight: 1.0, // ความหนา dash
                                      dashSpace:
                                          1.0, // ช่องว่างระหว่างแต่ละ dash
                                      color: configTheme()
                                          .appBarTheme
                                          .titleTextStyle!
                                          .color!
                                          .withOpacity(0.08), // สีของ dash
                                    ),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    //TODO วันที่กำหนดชำระค่างวด
                                    SizedBox(
                                      height: 44.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: <Widget>[
                                          //TODO due date กำหนดชำระค่างวด
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  height: 18.h,
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Text(
                                                    billDueDate.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 2.h,
                                                ),
                                                Get.find<ContractListController>()
                                                    .contractList
                                                    .length ==
                                                    0
                                                    ? AnimatedShimmer(
                                                    width: 100.w):
                                                SizedBox(
                                                  height: 24.h,
                                                  child: Text(
                                                    billCtl.dueDateLocalFormat.value,
                                                    // contractCtl
                                                    //     .contractList[Get.find<
                                                    //             BillPaymentController>()
                                                    //         .selectedContractIndex
                                                    //         .value]
                                                    //     .due_date
                                                    //     .toString(),
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                          //TODO วันที่ทำรายการ
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Container(
                                                  height: 18.h,
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Text(
                                                    transactionDate.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 2.h,
                                                ),
                                                SizedBox(
                                                  height: 24.h,
                                                  child: Text(
                                                    billCtl
                                                        .transectionDate.value
                                                        .toString(),
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    DashedLine(
                                      dashWidth: 3.0, // ความยาว dash
                                      dashHeight: 1.0, // ความหนา dash
                                      dashSpace:
                                          1.0, // ช่องว่างระหว่างแต่ละ dash
                                      color: configTheme()
                                          .appBarTheme
                                          .titleTextStyle!
                                          .color!
                                          .withOpacity(0.08), // สีของ dash
                                    ),
                                    SizedBox(
                                      height: 5.h,
                                    ),
                                    //TODO จำนวนเงินที่ต้องชำระ
                                    SizedBox(
                                      height: 44.h,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: <Widget>[
                                          //TODO count down time
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Container(
                                                  height: 18.h,
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Text(
                                                    menuGetLoanPleasePay.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .colorScheme
                                                          .error
                                                          .withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 2.h,
                                                ),
                                                SizedBox(
                                                  height: 24.h,
                                                  child: Obx(() {
                                                    return Text(
                                                      billCtl.count_qrExpired
                                                          .value,
                                                      style: TextStyle(
                                                        color: configTheme()
                                                            .colorScheme
                                                            .error
                                                            .withOpacity(1),
                                                        fontSize: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontSize,
                                                        fontFamily: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontFamily,
                                                        fontWeight: configTheme()
                                                            .primaryTextTheme
                                                            .bodyMedium
                                                            ?.fontWeight,
                                                      ),
                                                    );
                                                  }),
                                                )
                                              ],
                                            ),
                                          ),
                                          //TODO ยอดที่ต้องชำระ with currency
                                          SizedBox(
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Container(
                                                  height: 18.h,
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Text(
                                                    payAmountCurrency.tr,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(0.5),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .labelSmall
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 2.h,
                                                ),
                                                Get.find<ContractListController>()
                                                    .contractList
                                                    .length ==
                                                    0
                                                    ? AnimatedShimmer(
                                                    width: 100.w):
                                                SizedBox(
                                                  height: 24.h,
                                                  child: Text(
                                                    // AppService.formatCurrencyWithDecimal(contractCtl
                                                    //     .contractList[Get.find<
                                                    //             BillPaymentController>()
                                                    //         .selectedContractIndex
                                                    //         .value]
                                                    //     .nextpay
                                                    //     .toString()),
                                                      billCtl.amountController.value.text,
                                                    style: TextStyle(
                                                      color: configTheme()
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color
                                                          ?.withOpacity(1),
                                                      fontSize: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontSize,
                                                      fontFamily: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontFamily,
                                                      fontWeight: configTheme()
                                                          .primaryTextTheme
                                                          .bodyMedium
                                                          ?.fontWeight,
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              //TODO รูป QR code
                              buildQRImage(context),
                            ],
                          ),
                        ),
                      ),
                      //TODO option ชำระเงินด้วย QR code
                      Container(
                        margin: EdgeInsets.only(top: 540.h),
                        child: Column(
                          children: <Widget>[
                            //TODO funtion upload Slip
                            billCtl.isUploadSlipSuccess.value == false
                                ? PrimaryButtonWithIcon(
                                    title: confirm_slip.tr,
                                    buttonWidth: Get.width,
                                    backgroundColor: appConfigService
                                                .countryConfigCollection ==
                                            'rplc'
                                        ? AppColors.primaryRPLC_Grey
                                            .withOpacity(0.08)
                                        : configTheme()
                                            .buttonTheme
                                            .colorScheme!
                                            .secondary
                                            .withOpacity(0.08),
                                    isActive: true,
                                    textColor:
                                        configTheme().colorScheme.onPrimary,
                                    icon: SvgPicture.string(
                                        //TODO check เงือ่นไขอัปโหลด Slip แล้วเปลี่ยน icon
                                        AppSvgImage.confirm_slip_rplc,
                                        color: appConfigService
                                                    .countryConfigCollection ==
                                                'rplc'
                                            ? AppColors.primaryRPLC_Grey
                                                .withOpacity(1)
                                            : configTheme()
                                                .buttonTheme
                                                .colorScheme!
                                                .secondary
                                                .withOpacity(1)),
                                    onPressed: () {
                                      UploadimgPopUp.buildUploadImage(context,
                                          () {
                                        billCtl.cameraImgPayment(context);
                                      }, () {
                                        billCtl.chooseImgPayment(context);
                                      });
                                    })
                                //TODO alreay uploaded Slip
                                : PrimaryButtonWithIcon(
                                    title: uploaded_slip.tr,
                                    buttonWidth: Get.width,
                                    backgroundColor:
                                        appConfigService.countryConfigCollection ==
                                                'rplc'
                                            ? AppColors.primaryRPLC_Grey
                                                .withOpacity(1)
                                            : configTheme()
                                                .buttonTheme
                                                .colorScheme!
                                                .secondary
                                                .withOpacity(1),
                                    isActive: true,
                                    textColor: Colors.white,
                                    icon: SvgPicture.string(
                                      //TODO check เงือ่นไขอัปโหลด Slip แล้วเปลี่ยน icon
                                      AppSvgImage.upload_slip_rplc,
                                    ),
                                    onPressed: () {
                                      UploadimgPopUp.buildUploadImage(context,
                                          () {
                                        billCtl.cameraImgPayment(context);
                                      }, () {
                                        billCtl.chooseImgPayment(context);
                                      });
                                    }),
                            //TODO funtion save QR code image
                            PrimaryButtonWithIcon(
                                height: 12.h,
                                // appConfigService.countryConfigCollection ==
                                //     'rplc'
                                //     ? 12.h
                                //     : 0,
                                title: save_qrcode.tr,
                                buttonWidth: Get.width,
                                backgroundColor:
                                    appConfigService.countryConfigCollection ==
                                            'rplc'
                                        ? AppColors.primaryRPLC_Grey
                                            .withOpacity(0.08)
                                        : configTheme()
                                            .buttonTheme
                                            .colorScheme!
                                            .secondary
                                            .withOpacity(0.08),
                                isActive: true,
                                textColor: configTheme().colorScheme.onPrimary,
                                icon: SvgPicture.string(
                                    appConfigService.countryConfigCollection ==
                                            'aam'
                                        ? AppSvgImage.save_qr_aam
                                        : appConfigService
                                                    .countryConfigCollection ==
                                                'rplc'
                                            ? AppSvgImage.save_qr_rplc
                                            : AppSvgImage.save_qr_rafco),
                                onPressed: () {
                                  //TODO function save QR CODE
                                  billCtl.saveQRCodeImage();
                                }),
                          ],
                        ),
                      ),
                      //TODO save qrcode image success pop-up
                      billCtl.isSaveQRImg.value
                          ? Container(
                              width: 115.w,
                              height: 45.h,
                              margin: EdgeInsets.symmetric(horizontal: 106.w),
                              alignment: Alignment.center,
                              decoration: ShapeDecoration(
                                color: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.r),
                                  side: BorderSide(
                                    color: configTheme()
                                        .highlightColor
                                        .withOpacity(0.5),
                                    width: 1.w,
                                  ),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SizedBox(
                                      child: SvgPicture.string(appConfigService
                                                  .countryConfigCollection ==
                                              'aam'
                                          ? AppSvgImage.checked_aam
                                          : appConfigService
                                                      .countryConfigCollection ==
                                                  'rplc'
                                              ? AppSvgImage.checked_rplc
                                              : AppSvgImage.checked_rafco)),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                  Text(
                                    saved.tr,
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color
                                          ?.withOpacity(1),
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontWeight,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : Container()
                    ],
                  ),
                );
              }),
          HeaderGeneral(
            title: billPaymentMenu.tr,
            firstIcon: SizedBox(
                width: 24.w,
                height: 24.h,
                child: SvgPicture.string(AppSvgImage.back_btn)),
            secondIcon: GetBuilder<BillPaymentController>(builder: (billCtl) {
              return Container(
                width: 84.w,
                height: 36.h,
                alignment: Alignment.center,
                decoration: ShapeDecoration(
                  color: billCtl.isUploadSlipSuccess!.value
                      ? appConfigService.countryConfigCollection == 'aam'
                          ? configTheme().focusColor.withOpacity(1.0)
                          : configTheme().highlightColor.withOpacity(1)
                      : appConfigService.countryConfigCollection == 'aam'
                          ? configTheme().focusColor.withOpacity(0.5)
                          : configTheme().highlightColor.withOpacity(0.5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'Done',
                  style: TextStyle(
                    color: Colors.white.withOpacity(1),
                    fontSize:
                        configTheme().primaryTextTheme.labelMedium?.fontSize,
                    fontFamily:
                        configTheme().primaryTextTheme.labelMedium?.fontFamily,
                    fontWeight:
                        configTheme().primaryTextTheme.labelMedium?.fontWeight,
                  ),
                ),
              );
            }),
            firstOnPressed: () {
              Get.back();
            },
            secondOnPressed: () {
              if (Get
                  .find<BillPaymentController>()
                  .isUploadSlipSuccess!
                  .value) {
              Get.find<BillPaymentController>()
                  .getCurrentTimeTransection(); // วันที่ทำรายการ
              //TODO pay pending on process page
              Get.to(() => PaymentOnprocess(),
                  transition: Transition.rightToLeft);
              }
            },
          )
        ],
      ),
    );
  }

  Widget buildQRImage(BuildContext context) {
    final String? imageUrl =
        Get.find<BillPaymentController>().qrCode_Url?.value;

    return Container(
      height: 332.h,
      width: Get.width,
      child: imageUrl == null || imageUrl.isEmpty
          ? Center(child: CircularProgressIndicator())
          : appConfigService.countryConfigCollection == 'aam'
              ? Container(
                  color: Colors.white,
                  height: 332.h,
                  width: 327.w,
                  child: Column(
                    children: [
                      Container(
                        height: 53.h,
                        child: Image.asset(
                          'assets/promptpay_header.png',
                          fit: BoxFit.fill,
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.only(top: 15.h, left: 63.w, right: 63.w),
                        child: Image.network(
                          width: 200.w,
                          height: 200.h,
                          imageUrl,
                          fit: BoxFit.fill,
                          loadingBuilder: (BuildContext context, Widget child,
                              ImageChunkEvent? loadingProgress) {
                            if (loadingProgress == null) {
                              return child; // แสดงภาพเมื่อโหลดเสร็จ
                            } else {
                              return Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          (loadingProgress.expectedTotalBytes ??
                                              1)
                                      : null,
                                ),
                              );
                            }
                          },
                          errorBuilder: (BuildContext context, Object exception,
                              StackTrace? stackTrace) {
                            return Center(
                              child: Icon(Icons.error,
                                  size: 50, color: Colors.red),
                            );
                          },
                        ),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      SvgPicture.string(
                        AppSvgImage.promptpay_name,
                        fit: BoxFit.fill,
                      ),
                    ],
                  ),
                )
              : Image.network(
                  imageUrl,
                  fit: BoxFit.fill,
                  loadingBuilder: (BuildContext context, Widget child,
                      ImageChunkEvent? loadingProgress) {
                    if (loadingProgress == null) {
                      return child; // แสดงภาพเมื่อโหลดเสร็จ
                    } else {
                      return Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  (loadingProgress.expectedTotalBytes ?? 1)
                              : null,
                        ),
                      );
                    }
                  },
                  errorBuilder: (BuildContext context, Object exception,
                      StackTrace? stackTrace) {
                    return Center(
                      child: Icon(Icons.error, size: 50, color: Colors.red),
                    );
                  },
                ),
    );
  }

  String checkFormatGuarunteetype(String value){
    if(value.contains("สินเชื่อ")){
      return value.replaceAll("สินเชื่อ", "");
    }else{
      return value;
    }
  }
}
