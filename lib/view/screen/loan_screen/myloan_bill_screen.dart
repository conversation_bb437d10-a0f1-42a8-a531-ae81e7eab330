import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../controller/AppConfigService.dart';
import '../../componance/widgets/header_widgets/header_widget.dart';

class MyloanBillScreen extends StatelessWidget {
   MyloanBillScreen({Key? key}) : super(key: key);

   final GlobalKey webViewKey = GlobalKey();

  InAppWebViewController? webViewController;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
      crossPlatform: InAppWebViewOptions(
        useShouldOverrideUrlLoading: true,
        clearCache: true,
        mediaPlaybackRequiresUserGesture: true,
      ),
      android: AndroidInAppWebViewOptions(
        useHybridComposition: true,
        allowFileAccess: true,
      ),
      ios: IOSInAppWebViewOptions(
        allowsInlineMediaPlayback: true,
      ));

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    // Get.put(AppConfigService(context: context));
    return Scaffold(
      body: GetBuilder<MyloanController>(
        builder: (myloanCtl) {
          return Container(
              child: Stack(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 106.h),
                    child: InAppWebView(
                      key: webViewKey,
                      initialUrlRequest: URLRequest(
                          url: Uri.parse(
                              myloanCtl.selected_bill!.value.toString())),
                      initialOptions: options,
                      // pullToRefreshController: pullToRefreshController,
                      onWebViewCreated: (controller) {
                        webViewController = controller;
                      },
                      onConsoleMessage: (controller, consoleMessage) {
                        print(consoleMessage.message);
                        if (consoleMessage.message == "BACK_TO_MAIN") {
                          print(consoleMessage.message);
                          Get.back();
                        }
                      },
                      onLoadStart: (controller, url) {

                      },
                      androidOnPermissionRequest: (controller, origin,
                          resources) async {
                        return PermissionRequestResponse(
                            resources: resources,
                            action: PermissionRequestResponseAction.GRANT);
                      },
                      shouldOverrideUrlLoading: (controller,
                          navigationAction) async {
                        var uri = navigationAction.request.url!;

                        if (![
                          "http",
                          "https",
                          "file",
                          "chrome",
                          "data",
                          "javascript",
                          "about"
                        ].contains(uri.scheme)) {

                        }

                        return NavigationActionPolicy.ALLOW;
                      },
                      onLoadStop: (controller, url) async {

                      },
                      onLoadError: (controller, url, code, message) {
                      },
                      onProgressChanged: (controller, progress) {

                      },
                      onUpdateVisitedHistory: (controller, url, androidIsReload) {

                      },
                    ),
                  ),

                  HeaderWidget(
                    title: menuGetLoanReceipt.tr,
                      onPressed: () {
                        Get.back();
                      }
                  )
                ],
              )
          );
        },
      ),
    );
  }
}
