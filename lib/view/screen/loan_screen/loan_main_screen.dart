import 'package:AAMG/controller/aampay/aampay.controller.dart';
import 'package:AAMG/controller/contract/contractlist.controller.dart';
import 'package:AAMG/controller/contract/myloan.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/home/<USER>';
import 'package:AAMG/view/screen/register/register_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/bill_payment/billPayment.controller.dart';
import '../../../controller/transalation/transalation.controller.dart';
import '../../componance/themes/app_colors.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/utils/AppSvgImage.dart';
import '../../componance/widgets/app_pop_up/alert_popup.dart';
import '../../componance/widgets/app_pop_up/request_loan/request_success.dart';
import '../../componance/widgets/button_widgets/primary_button.dart';
import '../../componance/widgets/contract/contract_pay_widget.dart';
import '../../componance/widgets/header_widgets/header_icon.dart';
import '../bill_payment/select_payment_screen.dart';
import 'loan_request_screen.dart';
import 'loan_status.dart';
import 'myloan_detail_screen.dart';

class LoanMainScreen extends StatelessWidget {
  const LoanMainScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return Scaffold(
      backgroundColor: Colors.white,
      body: GetBuilder<ContractListController>(
        init: ContractListController(),
        builder: (contractListCtl) {
          return Stack(
            children: [
              contractListCtl.contractList.isEmpty &&
                      contractListCtl.contractStatus.isEmpty
                  ? _buildLoanDefault(context) //TODO ยังไม่มีรายการขอกู้เงิน
                  : _buildLoanRequest(context), //TODO มีรายการขอกู้เงิน
              Container(
                height: 52.h,
                margin: EdgeInsets.only(top: 712.0.h, left: 24.w, right: 24.w),
                child: PrimaryButton(
                  title: menuGetLoanPlease.tr,
                  onPressed: () async {
                    if (Get.find<HomeController>().isGuest!.value) {
                      showDialog(
                          context: context,
                          useSafeArea: true,
                          builder: (_) => const RegisterPage());
                      // Get.offAll(const RegisterPage());
                    } else {
                      //TODO เช็คข้อมุลที่อยู่ก่อนเข้ากระบวนการขอสินเชื่อ
                      final profileCtl = Get.find<ProfileController>();
                      if (profileCtl.profile.value.amphur == null ||
                          profileCtl.profile.value.province == null) {
                        print('ไม่มีข้อมูลที่อยู่');
                        AlertPopup.AlertUpdateAddress(context);
                        // LoanPopUp.AlertRequestSuccess(context);
                      } else {
                        _buildWarningDetail(context, menuGetLoanCondition.tr,
                            menuGetLoanConditionDes.tr);
                      }
                    }
                  },
                  backgroundColor:
                      configTheme().buttonTheme.colorScheme?.background,
                  textColor: configTheme().colorScheme.background,
                  height: 0.h,
                  buttonWidth: 327.0.w,
                  isActive: true,
                ),
              ),
              Container(
                height: 88.h,
                width: Get.width,
                // color: Colors.teal,
                // margin: EdgeInsets.only(top: 68.h),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 68.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          // Get.back();
                          // showDialog(context: context, builder: (_)=>HomePage());
                          // Navigator.push(context, MaterialPageRoute(builder: (_)=>HomeNavigator()));
                          // Navigator.pop(context);
                          Get.to(HomeNavigator());
                        },
                        child: Container(
                          height: 50.h,
                          width: 50.w,
                          // color: Colors.red,
                          child: Center(
                            child: SvgPicture.string(
                              AppSvgImage.back_btn,
                            ),
                          ),
                        ),
                      ),
                      Text(
                        menuGetLoanPlease.tr,
                        style: TextStyle(
                          color: configTheme().textTheme.bodyMedium?.color,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .titleMedium
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .titleMedium
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .titleMedium
                              ?.fontWeight,
                          // height: 0.09,
                        ),
                      ),
                      Container(
                          height: 50.h,
                          width: 50.w,
                          // color: Colors.red,
                          decoration: const BoxDecoration(
                            // color: Colors.red,
                            image: DecorationImage(
                              scale: 2,
                              image: AssetImage(
                                  'assets/register/icon/Information.png'),
                              // fit: BoxFit.contain,
                            ),
                          ))
                    ],
                  ),
                ),
              )
              // GestureDetector(
              //   onTap: () {
              //     Get.to(const HomePage());
              //   },
              //   child: HeaderIcon(
              //       title: menuGetLoanPlease.tr,
              //       onPressed: () {
              //         Get.to(const HomePage());
              //       },
              //       icon: Image.asset(
              //         'assets/register/icon/Information.png',
              //         fit: BoxFit.contain,
              //         width: 24.w,
              //         height: 24.h,
              //       )),
              // )
            ],
          );
        },
      ),
    );
  }

  Widget _buildLoanDefault(context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin: EdgeInsets.only(top: 200.h),
          // width: 206,
          // height: 206,
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                    width: appConfigService.countryConfigCollection == "aam"
                        ? 135.w
                        : 375.w,
                    alignment: Alignment.topCenter,
                    child: Image.asset(
                      appConfigService.countryConfigCollection == "aam"
                          ? AppImageAssets.aam_loan_apply
                          : appConfigService.countryConfigCollection == "rplc"
                              ? AppImageAssets.rplc_loan_apply
                              : AppImageAssets.rafco_loan_apply,
                      height: 122.h,
                    )),
                SizedBox(
                  height: 10.65.h,
                ),
                Container(
                  child: Text(
                    menuGetLoanNotList.tr,
                    style: TextStyle(
                      color: configTheme().textTheme.bodyMedium?.color,
                      fontSize:
                          configTheme().primaryTextTheme.titleLarge?.fontSize,
                      fontFamily:
                          configTheme().primaryTextTheme.titleLarge?.fontFamily,
                      fontWeight:
                          configTheme().primaryTextTheme.titleLarge?.fontWeight,
                    ),
                  ),
                ),
                Container(
                  // height: 48.h,
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: menuGetLoanNotListDes.tr,
                          style: TextStyle(
                            color: configTheme()
                                .textTheme
                                .bodyMedium
                                ?.color
                                ?.withOpacity(0.5),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontWeight,
                          ),
                        ),
                        TextSpan(
                          text: menuGetLoanCilk.tr,
                          style: TextStyle(
                            color: configTheme().colorScheme.primary,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .bodyMedium
                                ?.fontWeight,
                          ),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoanRequest(context) {
    return Container(
        margin: EdgeInsets.only(top: 110.h, left: 24.w, right: 24.w),
        width: Get.width,
        height: Get.height,
        // height: 954.h,
        child: SingleChildScrollView(
          child: Container(
            child:
                GetBuilder<ContractListController>(builder: (contractListCtl) {
              return Column(
                children: [
                  Container(
                    height: Get.height,
                    margin: EdgeInsets.only(bottom: 139.h),
                    child: ListView.builder(
                        padding: EdgeInsets.zero,
                        // itemCount: contractListCtl.contractList.length + contractListCtl.contractStatus.length, //TODO จำนวนสัญญาที่เดินอยู่ + จำนวนสัญญาที่อยู่ระหว่างพิจารณา
                        itemCount: contractListCtl.contractList.length + 1,
                        // เพิ่ม 1 เพื่อรองรับ _buildPendingLoan
                        itemBuilder: (context, index) {
                          // print('loan status itemCount : ${contractListCtl.contractList.length + contractListCtl.contractStatus.length}');
                          if (index < contractListCtl.contractList.length) {
                            return Column(
                              children: [
                                _buildMyLoan(context, index),
                              ],
                            );
                          } else {
                            return // todo มีรายการขอสินเชื่อที่อยู่ระหว่างพิจารณา
                                contractListCtl.contractStatus.isNotEmpty
                                    ? _buildPendingLoan(context)
                                    : Container();
                          }
                        }),
                  ),
                ],
              );
            }),
          ),
        ));
  }

  Widget _buildPendingLoan(context) {
    final ContractListController contractListCtl =
        Get.find<ContractListController>();
    AppConfigService appConfigService = Get.find<AppConfigService>();
    return Container(
      height: 239.h * contractListCtl.contractStatus.length +
          16.h * contractListCtl.contractStatus.length,
      margin: EdgeInsets.only(bottom: 200.h),
      child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: contractListCtl.contractStatus.length,
          physics:
              const NeverScrollableScrollPhysics(), // ปิดการเลื่อนใช้ ScrollView ข้างนอกแทน/**/
          shrinkWrap: true, // สำคัญหากใช้ใน ScrollView อื่น
          itemBuilder: (context, i) {
            if (contractListCtl.contractStatus.isEmpty) {
              return Container();
            } else {
              // print("sddsdsdsdsdsdsdsdsds");
              // print(contractListCtl.contractStatus[i].guarantee_type.toString());
              // print(contractListCtl.contractStatus[i].loan_amount.toString());
              // print(contractListCtl.contractStatus[i].loan_periods.toString());

              return Column(
                children: [
                  Container(
                      width: 327.w,
                      height: 239.h,
                      padding: EdgeInsets.symmetric(
                          horizontal: 18.w, vertical: 17.h),
                      decoration: ShapeDecoration(
                        color: Colors.white,
                        shape: RoundedRectangleBorder(
                          side: BorderSide(
                              width: 1.w, color: const Color(0x141A1818)),
                          borderRadius: BorderRadius.circular(14.r),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            height: 44.h,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      width: 44.w,
                                      height: 44.h,
                                      // color: configTheme().colorScheme.secondary,
                                      child: SvgPicture.string(
                                        contractListCtl.contractStatus[i]
                                                    .guarantee_type
                                                    .toString() ==
                                                "1"
                                            ? contractListCtl
                                                .guaranteeImgList![0]
                                            : contractListCtl.contractStatus[i]
                                                        .guarantee_type
                                                        .toString() ==
                                                    "2"
                                                ? contractListCtl
                                                    .guaranteeImgList![1]
                                                : contractListCtl
                                                            .contractStatus[i]
                                                            .guarantee_type
                                                            .toString() ==
                                                        "3"
                                                    ? contractListCtl
                                                        .guaranteeImgList![2]
                                                    : contractListCtl
                                                        .guaranteeImgList![3],
                                      ),
                                    ),
                                    SizedBox(width: 12.w),
                                    SizedBox(
                                      width: 145.w,
                                      height: 44.h,
                                      child: Text.rich(
                                        TextSpan(
                                          children: [
                                            // ข้อความแรก (ขนาดฟอนต์คงที่)
                                            TextSpan(
                                              text:
                                                  '${contractListCtl.contractStatus[i].guarantee_type.toString() == "1" ? contractListCtl.guaranteeList[0] : contractListCtl.contractStatus[i].guarantee_type.toString() == "2" ? contractListCtl.guaranteeList[1] : appConfigService.countryConfigCollection == 'aam' || appConfigService.countryConfigCollection == 'rafco' && contractListCtl.contractStatus[i].guarantee_type.toString() == "3" ? contractListCtl.guaranteeList[3] : contractListCtl.guaranteeList[2]}\n',
                                              style: TextStyle(
                                                color: const Color(0xFF1A1818),
                                                fontSize: 14.sp,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            // ข้อความที่สอง (ใช้ FittedBox เพื่อให้ปรับขนาดอัตโนมัติ)
                                            WidgetSpan(
                                              child: FittedBox(
                                                fit: BoxFit
                                                    .scaleDown, // ลดขนาดข้อความเมื่อเกินขอบเขต
                                                alignment: Alignment.centerLeft,
                                                child: Text(
                                                  contractListCtl
                                                              .contractStatus[i]
                                                              .guarantee_type
                                                              .toString() ==
                                                          "1"
                                                      ? contractListCtl
                                                          .guaranteeDescList[0]
                                                      : contractListCtl
                                                                  .contractStatus[
                                                                      i]
                                                                  .guarantee_type
                                                                  .toString() ==
                                                              "2"
                                                          ? contractListCtl
                                                                  .guaranteeDescList[
                                                              1]
                                                          : contractListCtl
                                                                      .contractStatus[
                                                                          i]
                                                                      .guarantee_type
                                                                      .toString() ==
                                                                  "3"
                                                              ? contractListCtl
                                                                      .guaranteeDescList[
                                                                  2]
                                                              : contractListCtl
                                                                  .guaranteeDescList[2],
                                                  style: TextStyle(
                                                    color:
                                                        const Color(0x7F1A1818),
                                                    fontSize: 14
                                                        .sp, // เริ่มต้นที่ 14.sp
                                                    fontFamily: TextStyleTheme
                                                        .text_Regular
                                                        .fontFamily,
                                                    fontWeight: FontWeight.w400,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 7.w),
                                  ],
                                ),
                                SizedBox(
                                  height: 44.h,
                                  child: Text.rich(
                                    TextSpan(
                                      children: [
                                        TextSpan(
                                          text:
                                              '฿${contractListCtl.formatCurrency(contractListCtl.contractStatus[i].loan_amount.toString())}\n',
                                          style: TextStyle(
                                            color: const Color(0xFF1A1818),
                                            fontSize: 14,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w700,
                                          ),
                                        ),
                                        WidgetSpan(
                                          child: FittedBox(
                                            fit: BoxFit
                                                .scaleDown, // ลดขนาดข้อความเมื่อเกินขอบเขต
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              menuGetLoanAmountAdd.tr,
                                              style: TextStyle(
                                                color: const Color(0x7F1A1818),
                                                fontSize: 14,
                                                fontFamily: TextStyleTheme
                                                    .text_Regular.fontFamily,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 18.h),
                          Container(
                            decoration: ShapeDecoration(
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  width: 1.w,
                                  strokeAlign: BorderSide.strokeAlignCenter,
                                  color: const Color(0x141A1818),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(height: 13.h),
                          Container(
                            height: 54.h,
                            width: Get.width,
                            child: Column(
                              children: [
                                Container(
                                  height: 22.h,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 138.w,
                                        height: 22.h,
                                        child: Text(
                                          menuGetLoanTime.tr,
                                          style: TextStyle(
                                            color: const Color(0x7F1A1818),
                                            fontSize: 14,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 22.h,
                                        child: Text(
                                          '${contractListCtl.contractStatus[i].loan_periods.toString()} ${menuGetLoanMoth.tr}',
                                          textAlign: TextAlign.right,
                                          style: TextStyle(
                                            color: const Color(0xFF1A1818),
                                            fontSize: 14,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(height: 10.h),
                                Container(
                                  height: 22.h,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 22.h,
                                        child: Text(
                                          menuGetLoanStatus.tr,
                                          style: TextStyle(
                                            color: const Color(0x7F1A1818),
                                            fontSize: 14,
                                            fontFamily: TextStyleTheme
                                                .text_Regular.fontFamily,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                                      SizedBox(
                                        height: 22.h,
                                        child: FittedBox(
                                          fit: BoxFit
                                              .scaleDown, // ลดขนาดข้อความเมื่อเกินขอบเขต
                                          alignment: Alignment.centerRight,
                                          child: Text(
                                            contractListCtl.loanStatusStepList![
                                                        i] ==
                                                    1
                                                ? menuGetLoanStatusSend.tr
                                                : contractListCtl
                                                                .loanStatusStepList![
                                                            i] ==
                                                        2
                                                    ? menuGetLoanStatusCheck.tr
                                                    : contractListCtl
                                                                    .loanStatusStepList![
                                                                i] ==
                                                            3
                                                        ? menuGetLoanStatusConsider
                                                            .tr
                                                        : contractListCtl
                                                                        .loanStatusStepList![
                                                                    i] ==
                                                                4
                                                            ? menuGetLoanStatusPassConsider
                                                                .tr
                                                            : contractListCtl
                                                                            .loanStatusStepList![
                                                                        i] ==
                                                                    0
                                                                ? menuGetLoanStatusReject
                                                                    .tr
                                                                : menuGetLoanStatusApprove
                                                                    .tr,
                                            textAlign: TextAlign.right,
                                            style: TextStyle(
                                              color: configTheme()
                                                  .colorScheme
                                                  .onSecondary,
                                              fontSize: 14,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 20.h),
                          InkWell(
                            onTap: () async {
                              await contractListCtl.selectedStatusContract(i);
                              Get.to(() => LoanStatus());
                            },
                            child: Container(
                              width: 291.w,
                              height: 52.h,
                              decoration: ShapeDecoration(
                                color: configTheme()
                                    .colorScheme
                                    .onSecondary
                                    .withOpacity(0.05),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Container(
                                    decoration: ShapeDecoration(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(50),
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        menuGetLoanFollowStatus.tr,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: const Color(0xFF1A1818),
                                          fontSize: 14,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 4.w),
                                  Container(
                                    width: 24.w,
                                    height: 24.h,
                                    child: SvgPicture.string(
                                      appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'aam'
                                          ? AppSvgImage.aam_loan_follow_status
                                          : appConfigService
                                                      .countryConfigCollection
                                                      .toString() ==
                                                  'rplc'
                                              ? AppSvgImage
                                                  .rplc_loan_follow_status
                                              : AppSvgImage
                                                  .rafco_loan_follow_status,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      )),
                  SizedBox(height: 16.h)
                ],
              );
            }
          }),
    );
  }

  Widget _buildMyLoan(context, int index) {
    Get.lazyPut(() => BillPaymentController(), fenix: true);

    return GetBuilder<ContractListController>(builder: (contractListCtl) {
      return Container(
        margin: EdgeInsets.only(bottom: 16.h),
        child: Container(
          width: 327.w,
          height: 371.h,
          padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 18.h),
          decoration: ShapeDecoration(
            color: Colors.white,
            shape: RoundedRectangleBorder(
              side: BorderSide(width: 1.w, color: const Color(0x141A1818)),
              borderRadius: BorderRadius.circular(14.r),
            ),
          ),
          child: Column(
            children: [
              Container(
                // height: 44.h,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 44.w,
                          height: 44.h,
                          child: contractListCtl
                                  .contractList[index].guarantee_type
                                  .toString()
                                  .isNotEmpty
                              ? SvgPicture.string(
                                  contractListCtl.contractList[index]
                                              .guarantee_type
                                              .toString() ==
                                          "1"
                                      ? contractListCtl.guaranteeImgList![0]
                                      : contractListCtl.contractList[index]
                                                  .guarantee_type
                                                  .toString() ==
                                              "2"
                                          ? contractListCtl.guaranteeImgList![1]
                                          : contractListCtl.contractList[index]
                                                      .guarantee_type
                                                      .toString() ==
                                                  "3"
                                              ? contractListCtl
                                                  .guaranteeImgList![2]
                                              : contractListCtl
                                                  .guaranteeImgList![3],
                                )
                              : Container(),
                        ),
                        SizedBox(width: 12.w),
                        SizedBox(
                          // width: 145,
                          height: 44.h,
                          child: Obx(() {
                            return Text.rich(
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: contractListCtl.contractList[index]
                                                .guarantee_type
                                                .toString() ==
                                            '1'
                                        ? contractListCtl.guaranteeList[0]
                                        : contractListCtl.contractList[index]
                                                    .guarantee_type
                                                    .toString() ==
                                                '2'
                                            ? contractListCtl.guaranteeList[1]
                                            : contractListCtl
                                                        .contractList[index]
                                                        .guarantee_type
                                                        .toString() ==
                                                    '3'
                                                ? contractListCtl
                                                    .guaranteeList[2]
                                                : contractListCtl
                                                    .guaranteeList[2],
                                    style: TextStyle(
                                      color: configTheme()
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontSize: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontSize,
                                      fontFamily: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontFamily,
                                      fontWeight: configTheme()
                                          .primaryTextTheme
                                          .bodyMedium
                                          ?.fontWeight,
                                    ),
                                  ),
                                  const TextSpan(text: '\n'),
                                  WidgetSpan(
                                      child: FittedBox(
                                    fit: BoxFit.scaleDown,
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      contractListCtl.contractList[index]
                                                  .guarantee_type
                                                  .toString() ==
                                              '1'
                                          ? contractListCtl.guaranteeDescList[0]
                                          : contractListCtl.contractList[index]
                                                      .guarantee_type
                                                      .toString() ==
                                                  '2'
                                              ? contractListCtl
                                                  .guaranteeDescList[1]
                                              : contractListCtl
                                                          .contractList[index]
                                                          .guarantee_type
                                                          .toString() ==
                                                      '3'
                                                  ? contractListCtl
                                                      .guaranteeDescList[2]
                                                  : contractListCtl
                                                      .guaranteeDescList[2],
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(0.5),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodySmall
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ))
                                ],
                              ),
                            );
                          }),
                        ),
                        SizedBox(width: 7.w),
                      ],
                    ),
                    SizedBox(
                      // height: 44.h,
                      child: Obx(() {
                        return Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text:
                                    '฿${contractListCtl.contractList[index].loan_amount.toString()}\n',
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyLarge
                                      ?.fontWeight,
                                ),
                              ),
                              TextSpan(
                                text: menuGetLoanAmountAdd.tr,
                                style: TextStyle(
                                  color: configTheme()
                                      .textTheme
                                      .bodyMedium
                                      ?.color
                                      ?.withOpacity(0.5),
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodySmall
                                      ?.fontWeight,
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.right,
                        );
                      }),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 18.h),
              Container(
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(
                      width: 1.w,
                      strokeAlign: BorderSide.strokeAlignCenter,
                      color: const Color(0x141A1818),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 13.h),
              Container(
                width: 291.w,
                height: 118.h,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      height: 22.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              menuGetLoanInterest.tr,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              '0.75%',
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Container(
                      width: 291.w,
                      height: 22.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              menuGetLoanInstallment.tr,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              '${contractListCtl.contractList[index].paid_periods}/${contractListCtl.contractList[index].periods}',
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodyMedium
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Container(
                      width: 291.w,
                      height: 22.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              menuGetLoanDate.tr,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 22.h,
                            child: Obx(() {
                              return Text(
                                contractListCtl.setDueDateLocale(
                                    contractListCtl.contractList[index].due_date
                                        .toString(),
                                    Get.find<TransalationController>()
                                                .location
                                                .value ==
                                            'English'
                                        ? 'en'
                                        : Get.find<TransalationController>()
                                                        .location
                                                        .value ==
                                                    'Thailand' ||
                                                appConfigService
                                                        .countryConfigCollection ==
                                                    'aam'
                                            ? 'th'
                                            : Get.find<TransalationController>()
                                                            .location
                                                            .value ==
                                                        'Lao' ||
                                                    appConfigService
                                                            .countryConfigCollection ==
                                                        'rplc'
                                                ? 'lo'
                                                : Get.find<TransalationController>()
                                                                .location
                                                                .value ==
                                                            'Cambodian' ||
                                                        appConfigService
                                                                .countryConfigCollection ==
                                                            'rafco'
                                                    ? 'km'
                                                    : 'en'),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Container(
                      width: 291.w,
                      height: 22.h,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 22.h,
                            child: Text(
                              menuGetLoanNo.tr,
                              style: TextStyle(
                                color: configTheme()
                                    .textTheme
                                    .bodyMedium
                                    ?.color
                                    ?.withOpacity(0.5),
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .bodySmall
                                    ?.fontWeight,
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 22.h,
                            child: Obx(() {
                              return Text(
                                contractListCtl.contractList[index].ctt_code
                                    .toString(),
                                textAlign: TextAlign.right,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .bodyMedium
                                      ?.fontWeight,
                                ),
                              );
                            }),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20.h),
              InkWell(
                onTap: () async {
                  print('จ่ายค่างวด');
                  // final MyloanController myloanCtl =
                  //     Get.find<MyloanController>();
                  // await myloanCtl.setDataPayment(
                  //     index,
                  //     contractListCtl.contractList[index].ctt_code.toString(),
                  //     contractListCtl.contractList[index].nextpay.toString());
                  // ContractPayWidget.alertQRPayment(context); //TODO : แก้ไขเป็นหน้าใหม่

                  if (Get.find<ContractListController>()
                      .contractList
                      .isNotEmpty) {
                    await Get.find<BillPaymentController>().setInitailContract(
                        index,
                        contractListCtl.contractList[index].ctt_code
                            .toString());
                    Get.to(() => SelectPaymentScreen());
                  }
                },
                child: Container(
                  width: 291.w,
                  height: 52.h,
                  decoration: ShapeDecoration(
                    color:
                        configTheme().colorScheme.onSecondary.withOpacity(0.05),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        menuGetLoanPay.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: configTheme().colorScheme.primary,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontWeight,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                          width: 24.w,
                          height: 24.h,
                          child: SvgPicture.string(appConfigService
                                      .countryConfigCollection
                                      .toString() ==
                                  'aam'
                              ? AppSvgImage.aam_scan_loan
                              : appConfigService.countryConfigCollection
                                          .toString() ==
                                      'rplc'
                                  ? '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.1 12C3.1 9.62604 3.10127 7.90617 3.27765 6.59393C3.45168 5.29918 3.78625 4.49123 4.3883 3.88823C4.99074 3.2863 5.79856 2.9517 7.09355 2.77765C8.40591 2.60127 10.126 2.6 12.5 2.6C14.874 2.6 16.5943 2.60127 17.9067 2.77765C19.2019 2.95173 20.0093 3.28637 20.6106 3.88812L20.6107 3.88826C21.2132 4.49074 21.5481 5.29855 21.7222 6.5936C21.8987 7.90594 21.9 9.62603 21.9 12C21.9 14.374 21.8987 16.0943 21.7222 17.4067C21.548 18.7018 21.2132 19.5091 20.611 20.1104L20.6106 20.1109C20.0087 20.7132 19.2012 21.0481 17.9063 21.2222C16.5941 21.3987 14.874 21.4 12.5 21.4C10.126 21.4 8.40619 21.3987 7.09398 21.2222C5.79923 21.0481 4.99129 20.7133 4.38826 20.1107C3.78635 19.5088 3.45171 18.7013 3.27765 17.4063C3.10127 16.0941 3.1 14.374 3.1 12Z" fill="url(#paint0_linear_3890_23339)" stroke="#1A1818" stroke-width="1.2"/><path d="M10.504 5.75196C10.6025 5.75144 10.6999 5.73152 10.7907 5.69334C10.8815 5.65517 10.9639 5.59948 11.0332 5.52947C11.1024 5.45945 11.1572 5.37648 11.1944 5.28528C11.2316 5.19409 11.2505 5.09646 11.25 4.99796C11.2495 4.89947 11.2296 4.80205 11.1914 4.71126C11.1532 4.62046 11.0975 4.53808 11.0275 4.46881C10.9575 4.39953 10.8745 4.34473 10.7833 4.30752C10.6921 4.27032 10.5945 4.25144 10.496 4.25196C9.414 4.25796 8.516 4.28196 7.783 4.41096C7.031 4.54196 6.388 4.79296 5.861 5.29396C5.406 5.72696 5.101 6.16596 4.936 6.79696C4.786 7.36896 4.759 8.07196 4.752 8.99496C4.75054 9.19388 4.82816 9.38522 4.96778 9.5269C5.1074 9.66859 5.29759 9.749 5.4965 9.75046C5.69541 9.75192 5.88676 9.6743 6.02844 9.53468C6.17012 9.39506 6.25054 9.20488 6.252 9.00596C6.26 8.05696 6.292 7.53996 6.387 7.17796C6.467 6.87496 6.593 6.66796 6.896 6.37996C7.138 6.14996 7.468 5.98796 8.041 5.88796C8.633 5.78496 9.411 5.75796 10.504 5.75196ZM14.504 4.25196C14.3051 4.2509 14.1139 4.3289 13.9725 4.46881C13.8311 4.60871 13.7511 4.79905 13.75 4.99796C13.7489 5.19688 13.8269 5.38806 13.9668 5.52947C14.1067 5.67087 14.2971 5.7509 14.496 5.75196C15.589 5.75796 16.367 5.78496 16.959 5.88796C17.532 5.98796 17.862 6.14996 18.104 6.37996C18.407 6.66896 18.534 6.87496 18.613 7.17796C18.708 7.53996 18.741 8.05796 18.748 9.00596C18.7495 9.20488 18.8299 9.39506 18.9716 9.53468C19.0417 9.60382 19.1248 9.65845 19.2161 9.69548C19.3073 9.7325 19.405 9.75119 19.5035 9.75046C19.602 9.74974 19.6994 9.72963 19.7901 9.69127C19.8808 9.65291 19.9631 9.59706 20.0322 9.5269C20.1014 9.45675 20.156 9.37367 20.193 9.28239C20.23 9.19112 20.2487 9.09345 20.248 8.99496C20.241 8.07196 20.214 7.36896 20.064 6.79696C19.898 6.16696 19.594 5.72696 19.139 5.29396C18.612 4.79296 17.969 4.54196 17.217 4.41096C16.484 4.28196 15.586 4.25796 14.504 4.25196ZM5.5 11.25C5.30109 11.25 5.11032 11.329 4.96967 11.4696C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3896 4.96967 12.5303C5.11032 12.6709 5.30109 12.75 5.5 12.75H19.5C19.6989 12.75 19.8897 12.6709 20.0303 12.5303C20.171 12.3896 20.25 12.1989 20.25 12C20.25 11.8011 20.171 11.6103 20.0303 11.4696C19.8897 11.329 19.6989 11.25 19.5 11.25H5.5ZM6.252 14.995C6.25128 14.8965 6.23116 14.7991 6.1928 14.7084C6.15445 14.6177 6.0986 14.5354 6.02844 14.4662C5.95829 14.3971 5.8752 14.3425 5.78393 14.3054C5.69266 14.2684 5.59499 14.2497 5.4965 14.2505C5.39801 14.2512 5.30062 14.2713 5.20991 14.3097C5.11919 14.348 5.03691 14.4039 4.96778 14.474C4.89865 14.5442 4.84401 14.6273 4.80699 14.7185C4.76996 14.8098 4.75128 14.9075 4.752 15.006C4.759 15.929 4.786 16.632 4.936 17.204C5.102 17.834 5.406 18.274 5.861 18.707C6.388 19.207 7.031 19.458 7.783 19.59C8.516 19.718 9.414 19.743 10.496 19.749C10.5945 19.7495 10.6921 19.7306 10.7833 19.6934C10.8745 19.6562 10.9575 19.6014 11.0275 19.5321C11.0975 19.4628 11.1532 19.3805 11.1914 19.2897C11.2296 19.1989 11.2495 19.1015 11.25 19.003C11.2505 18.9045 11.2316 18.8068 11.1944 18.7156C11.1572 18.6245 11.1024 18.5415 11.0332 18.4715C10.9639 18.4014 10.8815 18.3458 10.7907 18.3076C10.6999 18.2694 10.6025 18.2495 10.504 18.249C9.411 18.243 8.633 18.216 8.041 18.112C7.468 18.012 7.138 17.851 6.896 17.62C6.593 17.332 6.466 17.125 6.387 16.823C6.292 16.461 6.26 15.943 6.252 14.995ZM20.248 15.006C20.2495 14.8071 20.1718 14.6157 20.0322 14.474C19.8926 14.3323 19.7024 14.2519 19.5035 14.2505C19.3046 14.249 19.1132 14.3266 18.9716 14.4662C18.8299 14.6059 18.7495 14.7961 18.748 14.995C18.74 15.943 18.708 16.461 18.613 16.823C18.533 17.125 18.407 17.332 18.104 17.62C17.862 17.85 17.532 18.012 16.959 18.112C16.367 18.216 15.589 18.242 14.496 18.249C14.3975 18.2495 14.3001 18.2694 14.2093 18.3076C14.1185 18.3458 14.0361 18.4014 13.9668 18.4715C13.8269 18.6129 13.7489 18.8041 13.75 19.003C13.7511 19.2019 13.8311 19.3922 13.9725 19.5321C14.1139 19.672 14.3051 19.75 14.504 19.749C15.586 19.743 16.484 19.719 17.217 19.589C17.969 19.459 18.612 19.208 19.139 18.707C19.594 18.273 19.899 17.834 20.064 17.204C20.214 16.632 20.241 15.929 20.248 15.006Z" fill="white"/><defs><linearGradient id="paint0_linear_3890_23339" x1="12.5" y1="2" x2="12.5" y2="22" gradientUnits="userSpaceOnUse"><stop stop-color="#FFC30E"/><stop offset="1" stop-color="#C08F00"/></linearGradient></defs></svg>'
                                  : '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M3.1 12C3.1 9.62604 3.10127 7.90617 3.27765 6.59393C3.45168 5.29918 3.78625 4.49123 4.3883 3.88823C4.99074 3.2863 5.79856 2.9517 7.09355 2.77765C8.40591 2.60127 10.126 2.6 12.5 2.6C14.874 2.6 16.5943 2.60127 17.9067 2.77765C19.2019 2.95173 20.0093 3.28637 20.6106 3.88812L20.6107 3.88826C21.2132 4.49074 21.5481 5.29855 21.7222 6.5936C21.8987 7.90594 21.9 9.62603 21.9 12C21.9 14.374 21.8987 16.0943 21.7222 17.4067C21.548 18.7018 21.2132 19.5091 20.611 20.1104L20.6106 20.1109C20.0087 20.7132 19.2012 21.0481 17.9063 21.2222C16.5941 21.3987 14.874 21.4 12.5 21.4C10.126 21.4 8.40619 21.3987 7.09398 21.2222C5.79923 21.0481 4.99129 20.7133 4.38826 20.1107C3.78635 19.5088 3.45171 18.7013 3.27765 17.4063C3.10127 16.0941 3.1 14.374 3.1 12Z" fill="url(#paint0_linear_3781_26961)" stroke="#1A1818" stroke-width="1.2"/><path d="M10.504 5.75196C10.6025 5.75144 10.6999 5.73152 10.7907 5.69334C10.8815 5.65517 10.9639 5.59948 11.0332 5.52947C11.1024 5.45945 11.1572 5.37648 11.1944 5.28528C11.2316 5.19409 11.2505 5.09646 11.25 4.99796C11.2495 4.89947 11.2296 4.80205 11.1914 4.71126C11.1532 4.62046 11.0975 4.53808 11.0275 4.46881C10.9575 4.39953 10.8745 4.34473 10.7833 4.30752C10.6921 4.27032 10.5945 4.25144 10.496 4.25196C9.414 4.25796 8.516 4.28196 7.783 4.41096C7.031 4.54196 6.388 4.79296 5.861 5.29396C5.406 5.72696 5.101 6.16596 4.936 6.79696C4.786 7.36896 4.759 8.07196 4.752 8.99496C4.75054 9.19388 4.82816 9.38522 4.96778 9.5269C5.1074 9.66859 5.29759 9.749 5.4965 9.75046C5.69541 9.75192 5.88676 9.6743 6.02844 9.53468C6.17012 9.39506 6.25054 9.20488 6.252 9.00596C6.26 8.05696 6.292 7.53996 6.387 7.17796C6.467 6.87496 6.593 6.66796 6.896 6.37996C7.138 6.14996 7.468 5.98796 8.041 5.88796C8.633 5.78496 9.411 5.75796 10.504 5.75196ZM14.504 4.25196C14.3051 4.2509 14.1139 4.3289 13.9725 4.46881C13.8311 4.60871 13.7511 4.79905 13.75 4.99796C13.7489 5.19688 13.8269 5.38806 13.9668 5.52947C14.1067 5.67087 14.2971 5.7509 14.496 5.75196C15.589 5.75796 16.367 5.78496 16.959 5.88796C17.532 5.98796 17.862 6.14996 18.104 6.37996C18.407 6.66896 18.534 6.87496 18.613 7.17796C18.708 7.53996 18.741 8.05796 18.748 9.00596C18.7495 9.20488 18.8299 9.39506 18.9716 9.53468C19.0417 9.60382 19.1248 9.65845 19.2161 9.69548C19.3073 9.7325 19.405 9.75119 19.5035 9.75046C19.602 9.74974 19.6994 9.72963 19.7901 9.69127C19.8808 9.65291 19.9631 9.59706 20.0322 9.5269C20.1014 9.45675 20.156 9.37367 20.193 9.28239C20.23 9.19112 20.2487 9.09345 20.248 8.99496C20.241 8.07196 20.214 7.36896 20.064 6.79696C19.898 6.16696 19.594 5.72696 19.139 5.29396C18.612 4.79296 17.969 4.54196 17.217 4.41096C16.484 4.28196 15.586 4.25796 14.504 4.25196ZM5.5 11.25C5.30109 11.25 5.11032 11.329 4.96967 11.4696C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3896 4.96967 12.5303C5.11032 12.6709 5.30109 12.75 5.5 12.75H19.5C19.6989 12.75 19.8897 12.6709 20.0303 12.5303C20.171 12.3896 20.25 12.1989 20.25 12C20.25 11.8011 20.171 11.6103 20.0303 11.4696C19.8897 11.329 19.6989 11.25 19.5 11.25H5.5ZM6.252 14.995C6.25128 14.8965 6.23116 14.7991 6.1928 14.7084C6.15445 14.6177 6.0986 14.5354 6.02844 14.4662C5.95829 14.3971 5.8752 14.3425 5.78393 14.3054C5.69266 14.2684 5.59499 14.2497 5.4965 14.2505C5.39801 14.2512 5.30062 14.2713 5.20991 14.3097C5.11919 14.348 5.03691 14.4039 4.96778 14.474C4.89865 14.5442 4.84401 14.6273 4.80699 14.7185C4.76996 14.8098 4.75128 14.9075 4.752 15.006C4.759 15.929 4.786 16.632 4.936 17.204C5.102 17.834 5.406 18.274 5.861 18.707C6.388 19.207 7.031 19.458 7.783 19.59C8.516 19.718 9.414 19.743 10.496 19.749C10.5945 19.7495 10.6921 19.7306 10.7833 19.6934C10.8745 19.6562 10.9575 19.6014 11.0275 19.5321C11.0975 19.4628 11.1532 19.3805 11.1914 19.2897C11.2296 19.1989 11.2495 19.1015 11.25 19.003C11.2505 18.9045 11.2316 18.8068 11.1944 18.7156C11.1572 18.6245 11.1024 18.5415 11.0332 18.4715C10.9639 18.4014 10.8815 18.3458 10.7907 18.3076C10.6999 18.2694 10.6025 18.2495 10.504 18.249C9.411 18.243 8.633 18.216 8.041 18.112C7.468 18.012 7.138 17.851 6.896 17.62C6.593 17.332 6.466 17.125 6.387 16.823C6.292 16.461 6.26 15.943 6.252 14.995ZM20.248 15.006C20.2495 14.8071 20.1718 14.6157 20.0322 14.474C19.8926 14.3323 19.7024 14.2519 19.5035 14.2505C19.3046 14.249 19.1132 14.3266 18.9716 14.4662C18.8299 14.6059 18.7495 14.7961 18.748 14.995C18.74 15.943 18.708 16.461 18.613 16.823C18.533 17.125 18.407 17.332 18.104 17.62C17.862 17.85 17.532 18.012 16.959 18.112C16.367 18.216 15.589 18.242 14.496 18.249C14.3975 18.2495 14.3001 18.2694 14.2093 18.3076C14.1185 18.3458 14.0361 18.4014 13.9668 18.4715C13.8269 18.6129 13.7489 18.8041 13.75 19.003C13.7511 19.2019 13.8311 19.3922 13.9725 19.5321C14.1139 19.672 14.3051 19.75 14.504 19.749C15.586 19.743 16.484 19.719 17.217 19.589C17.969 19.459 18.612 19.208 19.139 18.707C19.594 18.273 19.899 17.834 20.064 17.204C20.214 16.632 20.241 15.929 20.248 15.006Z" fill="white"/><defs><linearGradient id="paint0_linear_3781_26961" x1="12.5" y1="2" x2="12.5" y2="22" gradientUnits="userSpaceOnUse"><stop stop-color="#F2484F"/><stop offset="1" stop-color="#EA1B23"/></linearGradient></defs></svg>'))
                    ],
                  ),
                ),
              ),
              SizedBox(height: 12.h),
              InkWell(
                onTap: () {
                  print('สินเชื่อของฉัน');
                  Get.to(() => const MyloanDetailScreen());
                },
                child: Container(
                  width: 291.w,
                  height: 52.h,
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                          width: 1.w, color: const Color(0x141A1818)),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        homeLoan.tr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: configTheme().colorScheme.primary,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .bodyMedium
                              ?.fontWeight,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Container(
                          width: 24.w,
                          height: 24.h,
                          child: SvgPicture.string(appConfigService
                                      .countryConfigCollection
                                      .toString() ==
                                  'aam'
                              ? AppSvgImage.aam_loan_doc
                              : appConfigService.countryConfigCollection
                                          .toString() ==
                                      'rplc'
                                  ? AppSvgImage.rplc_loan_doc
                                  : AppSvgImage.rafco_loan_doc)),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  _buildWarningDetail(context, String title, String desc) {
    return showModalBottomSheet(
        useSafeArea: true,
        context: context,
        isScrollControlled: true,
        // backgroundColor: configTheme().colorScheme.background.withOpacity(1.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0.r),
            topRight: Radius.circular(12.0.r),
          ),
        ),
        builder: (context) {
          return Container(
            width: Get.width,
            height: 364.h,
            padding: const EdgeInsets.only(bottom: 20),
            decoration: ShapeDecoration(
              color: configTheme().colorScheme.background,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
            ),
            child: Stack(
              children: [
                Container(
                  alignment: Alignment.center,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: double.infinity,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              height: 34.h,
                              alignment: Alignment.center,
                              child: SvgPicture.string(AppSvgImage.close_bar),
                            ),
                            SizedBox(
                              height: 24.h,
                            ),
                            Image.asset(
                              appConfigService.countryConfigCollection == "aam"
                                  ? AppImageAssets.aam_loan_warning
                                  : appConfigService.countryConfigCollection ==
                                          "rplc"
                                      ? AppImageAssets.rplc_loan_warning
                                      : AppImageAssets.rafco_popup_loan,
                              height: 70.h,
                            ),
                            SizedBox(
                              height: 15.h,
                            ),
                            SizedBox(
                              width: 327,
                              // height: 20,
                              child: Text(
                                menuGetLoanCondition.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .titleLarge
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                            SizedBox(height: 10.h),
                            SizedBox(
                              width: 327,
                              // height: 47,
                              child: Text(
                                menuGetLoanConditionDes.tr,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color:
                                      configTheme().textTheme.bodyMedium?.color,
                                  fontSize: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontSize,
                                  fontFamily: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontFamily,
                                  fontWeight: configTheme()
                                      .primaryTextTheme
                                      .labelSmall
                                      ?.fontWeight,
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 44.h,
                            ),
                            PrimaryButton(
                                title: menuGetLoanRegis.tr,
                                onPressed: () {
                                  Get.to(() => const LoanRequestScreen());
                                },
                                buttonWidth: 327.w,
                                backgroundColor: configTheme()
                                    .buttonTheme
                                    .colorScheme
                                    ?.background,
                                textColor: configTheme().colorScheme.background,
                                isActive: true)
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
