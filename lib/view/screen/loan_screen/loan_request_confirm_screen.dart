import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/widgets/button_widgets/primary_button.dart';
import 'package:AAMG/view/screen/loan_screen/loan_status.dart';

import '../../../controller/AppConfigService.dart';
import '../../../controller/request_loan/loan.controller.dart';
import '../../componance/widgets/header_widgets/header_widget.dart';

class LoanRequestConfirmScreen extends StatelessWidget {
  const LoanRequestConfirmScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // AppConfigService appConfigService =
    //     Get.put(AppConfigService(context: context));
    return Scaffold(
      body: GetBuilder<LoanController>(builder: (loanCtl) {
        return Container(
            height: Get.height,
            width: Get.width,
            color: Colors.white,
            child: Column(children: [
              HeaderWidget(
                  title: menuGetLoanAmount.tr,
                  onPressed: () {
                    Get.back();
                  }),
              Container(
                  width: 327.w,
                  height: 50.h,
                  padding: EdgeInsets.only(left: 14.w, right: 14.w),
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(width: 1.w, color: Color(0x141A1818)),
                      borderRadius: BorderRadius.circular(14),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        // width: 90,
                        height: 22.h,
                        child: Text(
                          menuGetLoanAmountAdd.tr,
                          style: TextStyle(
                            color: configTheme().textTheme.bodyMedium?.color,
                            fontSize: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontWeight,
                          ),
                        ),
                      ),
                      // const SizedBox(width: 10),
                      Container(
                        height: 22.h,
                        child: Obx(() {
                          return Center(
                            child: Text(
                              '฿${loanCtl.loan_amount.value.text}',
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                color:
                                    configTheme().textTheme.bodyMedium?.color,
                                fontSize: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontSize,
                                fontFamily: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontFamily,
                                fontWeight: configTheme()
                                    .primaryTextTheme
                                    .titleLarge
                                    ?.fontWeight,
                              ),
                            ),
                          );
                        }),
                      )
                    ],
                  )),
              SizedBox(height: 12.h),
              Container(
                  width: 327.w,
                  height: 102.h,
                  padding: EdgeInsets.only(top: 14.h, left: 14.w, right: 14.w),
                  decoration: ShapeDecoration(
                    shape: RoundedRectangleBorder(
                      side: BorderSide(width: 1.w, color: Color(0x141A1818)),
                      borderRadius: BorderRadius.circular(14),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        homeLoanGuarantee.tr,
                        style: TextStyle(
                          color: configTheme()
                              .textTheme
                              .bodyMedium
                              ?.color
                              ?.withOpacity(0.5),
                          fontSize: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontWeight,
                        ),
                      ),
                      Container(
                        width: Get.width,
                        child: Row(
                          // mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 254.w,
                              // height: 42,
                              child: Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text:
                                          '${loanCtl.guaranteeList[loanCtl.selectedGuaranteeIndex.value]}\n',
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color,
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .bodyMedium
                                            ?.fontWeight,
                                      ),
                                    ),
                                    TextSpan(
                                      text: loanCtl.guaranteeListDesc[
                                          loanCtl.selectedGuaranteeIndex.value],
                                      style: TextStyle(
                                        color: configTheme()
                                            .textTheme
                                            .bodyMedium
                                            ?.color
                                            ?.withOpacity(0.5),
                                        fontSize: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontSize,
                                        fontFamily: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontFamily,
                                        fontWeight: configTheme()
                                            .primaryTextTheme
                                            .labelSmall
                                            ?.fontWeight,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              width: 34.w,
                              height: 34.h,
                              child: SvgPicture.string(
                                  appConfigService.countryConfigCollection ==
                                          "aam"
                                      ? loanCtl.guaranteeIconListSelected[
                                          loanCtl.selectedGuaranteeIndex.value]
                                      : appConfigService
                                                  .countryConfigCollection ==
                                              "rplc"
                                          ? loanCtl.guaranteeIconListSelectedRPLC[loanCtl.selectedGuaranteeIndex.value]
                                          : loanCtl.guaranteeIconListSelectedRafco[loanCtl.selectedGuaranteeIndex.value],
                              ),
                              // child: Image.asset(
                              //   appConfigService.countryConfigCollection ==
                              //           "aam"
                              //       ? loanCtl.guaranteeIconListSelected[
                              //           loanCtl.selectedGuaranteeIndex.value]
                              //       : appConfigService
                              //                   .countryConfigCollection ==
                              //               "rplc"
                              //           ? loanCtl.guaranteeIconListSelectedRPLC[loanCtl.selectedGuaranteeIndex.value]
                              //           : loanCtl.guaranteeIconListSelectedRafco[loanCtl.selectedGuaranteeIndex.value],
                              //   fit: BoxFit.fill,
                              // ),
                            ),
                          ],
                        ),
                      )
                    ],
                  )),
              SizedBox(height: 12.h),
              Container(
                width: 327.w,
                height: 50.h,
                padding: const EdgeInsets.only(top: 14, left: 14, right: 14),
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(width: 1.w, color: Color(0x141A1818)),
                    borderRadius: BorderRadius.circular(14),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          menuGetLoanTime.tr,
                          style: TextStyle(
                            color: configTheme()
                                .textTheme
                                .bodyMedium
                                ?.color
                                ?.withOpacity(0.5),
                            fontSize: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontSize,
                            fontFamily: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontFamily,
                            fontWeight: configTheme()
                                .primaryTextTheme
                                .labelSmall
                                ?.fontWeight,
                          ),
                        ),
                        Obx(() {
                          return Text(
                            '${loanCtl.selectedPeriod.value} ${menuGetLoanMoth.tr}',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: configTheme().textTheme.bodyMedium?.color,
                              fontSize: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontSize,
                              fontFamily: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontFamily,
                              fontWeight: configTheme()
                                  .primaryTextTheme
                                  .bodyMedium
                                  ?.fontWeight,
                            ),
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
              Spacer(),
              Container(
                height: 98.h,
                width: 327.w,
                decoration: ShapeDecoration(
                  shape: RoundedRectangleBorder(
                    side: BorderSide(width: 1, color: Color(0x141A1818)),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                ),
                child: Padding(
                  padding:  EdgeInsets.only(left: 14.w, right: 14.w,top: 16.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      appConfigService.countryConfigCollection.toString() =='aam'
                          ? SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_1551_7425)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#792AFF" stroke-opacity="0.75" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#1A1818" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_1551_7425"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')
                          : appConfigService.countryConfigCollection.toString() =='rafco'
                          ? SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1685_13648)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#EA1B23" stroke-opacity="0.75" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.1001V12.8901" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#1A1818" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_1685_13648"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')
                          : SvgPicture.string('<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_1717_29534)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#FFC20E" stroke-opacity="0.75" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09961V12.8896" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#1A1818" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_1717_29534"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>'),
                      SizedBox(width: 8.w,),
                      Text(
                        menuGetLoanDesCalculate.tr,
                        style: TextStyle(
                          color: configTheme().colorScheme.tertiary,
                          fontSize: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontSize,
                          fontFamily: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontFamily,
                          fontWeight: configTheme()
                              .primaryTextTheme
                              .labelSmall
                              ?.fontWeight,
                        ),
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(height: 26.h),
              PrimaryButton(
                title: menuGetLoanConfirm.tr,
                onPressed: () async {
                  // print('loanCtl.selectedGuaranteeIndex.value ${loanCtl.selectedGuaranteeIndex.value}');
                  // Get.to(() => const LoanStatus());
                  //TODO save ข้อมูล สนใจขอข้อมูลสินเชื่อ
                  await loanCtl.saveRequestLoan(context);
                  // loanCtl.selectedGuarantee.value = '';
                  // loanCtl. loan_amount.value.text ='';
                  // loanCtl.selectedPeriod.value ='';
                },
                buttonWidth: 327.w,
                backgroundColor:
                    configTheme().buttonTheme.colorScheme?.background,
                backgroundInactiveColor:
                    configTheme().buttonTheme.colorScheme?.tertiary,
                textColor: configTheme().colorScheme.background,
                isActive: true,
              ),
              SizedBox(
                height: 48.h,
              )
            ]));
      }),
    );
  }
}
