import 'package:AAMG/app_config.dart';
import 'package:AAMG/controller/config/appConfig.controller.dart';
import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/mr/mr.controller.dart';
import 'package:AAMG/controller/notification/notification.controllet.dart';
import 'package:AAMG/controller/profile/profile.controller.dart';
import 'package:AAMG/controller/steam.controller.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:AAMG/view/componance/widgets/app_pop_up/policy_popups/terms_condition.dart';
import 'package:AAMG/view/screen/mr/mr_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

import '../../../controller/branch/branch.controller.dart';
import '../../../controller/chatinapp/chatinapp.controller.dart';
import '../../../controller/session/session.controller.dart';
import '../../../controller/transalation/translation_key.dart';
import '../../componance/widgets/app_pop_up/home_popups.dart';
import '../../componance/widgets/app_pop_up/update_pop_up/patchPopup.dart';
import '../notify/notify_page.dart';
import 'home_page.dart';

// class HomeNavigator extends StatefulWidget {
//   const HomeNavigator({Key? key}) : super(key: key);
//
//   @override
//   State<HomeNavigator> createState() => _HomeNavigatorState();
// }
//
// class _HomeNavigatorState extends State<HomeNavigator>
//     with TickerProviderStateMixin {
//   List<PersistentBottomNavBarItem> _navBarsItems() {
//     return [
//       PersistentBottomNavBarItem(
//         icon: appConfigService.countryConfigCollection.toString() =="aam"?
//         SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12.5 18L12.5 15" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/> <path d="M2.85139 13.2135C2.49837 10.9162 2.32186 9.76763 2.75617 8.74938C3.19047 7.73112 4.15403 7.03443 6.08114 5.64106L7.52099 4.6C9.91829 2.86667 11.1169 2 12.5 2C13.8831 2 15.0817 2.86667 17.479 4.6L18.9189 5.64106C20.846 7.03443 21.8095 7.73112 22.2438 8.74938C22.6781 9.76763 22.5016 10.9162 22.1486 13.2135L21.8476 15.1724C21.3471 18.4289 21.0969 20.0572 19.929 21.0286C18.7611 22 17.0537 22 13.6388 22H11.3612C7.94633 22 6.23891 22 5.071 21.0286C3.90309 20.0572 3.65287 18.4289 3.15243 15.1724L2.85139 13.2135Z" stroke="#FF9300" stroke-width="1.5" stroke-linejoin="round"/></svg>')
//         :appConfigService.countryConfigCollection.toString() =="rafco"
//             ? SvgPicture.string('<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M11.5002 17L11.5002 14" stroke="#22409A" stroke-width="1.5" stroke-linecap="round"/> <path d="M1.85157 12.2135C1.49855 9.91624 1.32204 8.76763 1.75635 7.74938C2.19065 6.73112 3.15421 6.03443 5.08132 4.64106L6.52117 3.6C8.91847 1.86667 10.1171 1 11.5002 1C12.8832 1 14.0819 1.86667 16.4792 3.6L17.919 4.64106C19.8462 6.03443 20.8097 6.73112 21.244 7.74938C21.6783 8.76763 21.5018 9.91624 21.1488 12.2135L20.8478 14.1724C20.3473 17.4289 20.0971 19.0572 18.9292 20.0286C17.7613 21 16.0538 21 12.639 21H10.3614C6.94652 21 5.23909 21 4.07118 20.0286C2.90327 19.0572 2.65305 17.4289 2.15261 14.1724L1.85157 12.2135Z" stroke="#22409A" stroke-width="1.5" stroke-linejoin="round"/></svg>')
//         : SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12.5 18L12.5 15" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round"/> <path d="M2.85139 13.2135C2.49837 10.9162 2.32186 9.76763 2.75617 8.74938C3.19047 7.73112 4.15403 7.03443 6.08114 5.64106L7.52099 4.6C9.91829 2.86667 11.1169 2 12.5 2C13.8831 2 15.0817 2.86667 17.479 4.6L18.9189 5.64106C20.846 7.03443 21.8095 7.73112 22.2438 8.74938C22.6781 9.76763 22.5016 10.9162 22.1486 13.2135L21.8476 15.1724C21.3471 18.4289 21.0969 20.0572 19.929 21.0286C18.7611 22 17.0537 22 13.6388 22H11.3612C7.94633 22 6.23891 22 5.071 21.0286C3.90309 20.0572 3.65287 18.4289 3.15243 15.1724L2.85139 13.2135Z" stroke="#FFC20E" stroke-width="1.5" stroke-linejoin="round"/></svg>'),
//         // Image.asset(
//         //     appConfigService.countryConfigCollection.toString() =="aam"
//         //         ? 'assets/Home.png'
//         //         : appConfigService.countryConfigCollection.toString() =="rafco"
//         //         ?'assets/rafco-home-02.png'
//         //         :'assets/Home.png'
//         //     , width: 24.w, height: 24.h),
//         inactiveIcon: SvgPicture.string('<svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M11.5 17L11.5 14" stroke="#1A1818" stroke-opacity="0.5" stroke-width="1.5" stroke-linecap="round"/> <path d="M1.85139 12.2135C1.49837 9.91624 1.32186 8.76763 1.75617 7.74938C2.19047 6.73112 3.15403 6.03443 5.08114 4.64106L6.52099 3.6C8.91829 1.86667 10.1169 1 11.5 1C12.8831 1 14.0817 1.86667 16.479 3.6L17.9189 4.64106C19.846 6.03443 20.8095 6.73112 21.2438 7.74938C21.6781 8.76763 21.5016 9.91624 21.1486 12.2135L20.8476 14.1724C20.3471 17.4289 20.0969 19.0572 18.929 20.0286C17.7611 21 16.0537 21 12.6388 21H10.3612C6.94633 21 5.23891 21 4.071 20.0286C2.90309 19.0572 2.65287 17.4289 2.15243 14.1724L1.85139 12.2135Z" stroke="#1A1818" stroke-opacity="0.5" stroke-width="1.5" stroke-linejoin="round"/></svg>'),
//         activeColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//         inactiveColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//       ),
//       PersistentBottomNavBarItem(
//         icon:
//           SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M9.5 13C11.7091 13 13.5 11.2091 13.5 9C13.5 6.79086 11.7091 5 9.5 5C7.29086 5 5.5 6.79086 5.5 9C5.5 11.2091 7.29086 13 9.5 13ZM9.5 13C5.63401 13 2.5 15.6863 2.5 19M9.5 13C13.366 13 16.5 15.6863 16.5 19M15.5 5C17.7091 5 19.5 6.79086 19.5 9C19.5 10.6787 18.4659 12.1159 17 12.7092V13L17.5 13.2485C20.3915 13.9861 22.5 16.282 22.5 19" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
//             // Image.asset( appConfigService.countryConfigCollection.toString() =="aam"
//             //     ?'assets/notify_active.png'
//             //     :appConfigService.countryConfigCollection.toString() =="rafco"
//             //     ?'assets/notify_active_rafco.png'
//             //     : 'assets/notify_active.png', width: 24.w, height: 24.h),
//         inactiveIcon: SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M9.5 13C11.7091 13 13.5 11.2091 13.5 9C13.5 6.79086 11.7091 5 9.5 5C7.29086 5 5.5 6.79086 5.5 9C5.5 11.2091 7.29086 13 9.5 13ZM9.5 13C5.63401 13 2.5 15.6863 2.5 19M9.5 13C13.366 13 16.5 15.6863 16.5 19M15.5 5C17.7091 5 19.5 6.79086 19.5 9C19.5 10.6787 18.4659 12.1159 17 12.7092V13L17.5 13.2485C20.3915 13.9861 22.5 16.282 22.5 19" stroke="#1A1818" stroke-opacity="0.5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'),
//         activeColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//         inactiveColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//       ),
//       PersistentBottomNavBarItem(
//         icon: SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.94784 7.96942C7.26219 5.14032 9.65349 3 12.5 3V3C15.3465 3 17.7378 5.14032 18.0522 7.96942L18.304 10.2356C18.3072 10.2645 18.3088 10.279 18.3104 10.2933C18.4394 11.4169 18.8051 12.5005 19.3836 13.4725C19.3909 13.4849 19.3984 13.4973 19.4133 13.5222L19.9914 14.4856C20.5159 15.3599 20.7782 15.797 20.7216 16.1559C20.6839 16.3946 20.561 16.6117 20.3757 16.7668C20.0971 17 19.5873 17 18.5678 17H6.43223C5.41268 17 4.90291 17 4.62434 16.7668C4.43897 16.6117 4.31609 16.3946 4.27841 16.1559C4.22179 15.797 4.48407 15.3599 5.00862 14.4856L5.58665 13.5222C5.60161 13.4973 5.60909 13.4849 5.61644 13.4725C6.19488 12.5005 6.56064 11.4169 6.68959 10.2933C6.69123 10.279 6.69283 10.2645 6.69604 10.2356L6.94784 7.96942Z" stroke="#FF9300" stroke-width="1.5"/> <path d="M9.60222 17.6647C9.77315 18.6215 10.1498 19.467 10.6737 20.0701C11.1976 20.6731 11.8396 21 12.5 21C13.1604 21 13.8024 20.6731 14.3263 20.0701C14.8502 19.467 15.2269 18.6215 15.3978 17.6647" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/></svg>'),
//         // Image.asset(appConfigService.countryConfigCollection.toString() =="aam"
//         //     ?'assets/notify_active.png'
//         //     :appConfigService.countryConfigCollection.toString() =="rafco"
//         //     ?'assets/notify_active_rafco.png'
//         //     : 'assets/notify_active.png', width: 24.w, height: 24.h),
//         inactiveIcon: SvgPicture.string('<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.94784 7.96942C7.26219 5.14032 9.65349 3 12.5 3V3C15.3465 3 17.7378 5.14032 18.0522 7.96942L18.304 10.2356C18.3072 10.2645 18.3088 10.279 18.3104 10.2933C18.4394 11.4169 18.8051 12.5005 19.3836 13.4725C19.3909 13.4849 19.3984 13.4973 19.4133 13.5222L19.9914 14.4856C20.5159 15.3599 20.7782 15.797 20.7216 16.1559C20.6839 16.3946 20.561 16.6117 20.3757 16.7668C20.0971 17 19.5873 17 18.5678 17H6.43223C5.41268 17 4.90291 17 4.62434 16.7668C4.43897 16.6117 4.31609 16.3946 4.27841 16.1559C4.22179 15.797 4.48407 15.3599 5.00862 14.4856L5.58665 13.5222C5.60161 13.4973 5.60909 13.4849 5.61644 13.4725C6.19488 12.5005 6.56064 11.4169 6.68959 10.2933C6.69123 10.279 6.69283 10.2645 6.69604 10.2356L6.94784 7.96942Z" stroke="#1A1818" stroke-opacity="0.5" stroke-width="1.5"/> <path d="M9.60222 17.6647C9.77315 18.6215 10.1498 19.467 10.6737 20.0701C11.1976 20.6731 11.8396 21 12.5 21C13.1604 21 13.8024 20.6731 14.3263 20.0701C14.8502 19.467 15.2269 18.6215 15.3978 17.6647" stroke="#1A1818" stroke-opacity="0.5" stroke-width="1.5" stroke-linecap="round"/></svg>'),
//         // Image.asset('assets/Notify.png', width: 24.w, height: 24.h),
//         activeColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//         inactiveColorPrimary: Colors.white.withOpacity(0.8999999761581421),
//       ),
//     ];
//   }
//
//   late DateTime currentBackPressTime;
//   final SteamEvent steamControll = Get.put(SteamEvent());
//
//   //TODO ###################
//   //tabbar
//   TabController? homeTabController;
//   int currentIndex = 0;
//   void _handleTabSelection() {
//     setState(() {
//       currentIndex = homeTabController!.index;
//     });
//   }
//
//   @override
//   void initState() {
//     // TODO: implement initState
//     super.initState();
//     homeTabController = TabController(length: 3, initialIndex: 0, vsync: this);
//     homeTabController!.addListener(_handleTabSelection);
//     Future.delayed(Duration.zero, () async {
//       checkGuestData();
//       // AAMPolicy.alertTermAndPolicy(context);
//     });
//   }
//
//   void checkGuestData() async {
//     GetStorage storage = GetStorage();
//     var isGuest = await storage.read('isGuest');
//     var isLogin = await storage.read('token');
//     // print('###### ${isLogin}');
//     if (isGuest != true) {
//       final ProfileController profileController = Get.find<ProfileController>();
//       // print(profileController.checkPolicy!.value);
//       await profileController.getProfile();
//       if (profileController.checkPolicy!.value == false &&
//           profileController.updatePolicy!.value.isNotEmpty) {
//         //TODO check accept policy
//         var chk_accept_policy = await AAMPolicy.alertTermAndPolicy(context);
//
//         if (chk_accept_policy) {
//           //TODO check contract & notification permission
//           var chk_contract = await checkContractPermission();
//           if (chk_contract) {
//             checkNotificationPermission();
//           }
//         }
//       }
//     }
//   }
//
//   checkContractPermission() async {
//     bool hasContractsPermission =
//         await Get.find<AppConfigController>().checkContractsPermission();
//     if (hasContractsPermission) {
//       print('Permission granted');
//       // Permission granted, proceed with notifications
//       return true;
//     } else {
//       print('Permission denied');
//       // Permission denied or not yet requested, handle accordingly
//       var chk = await HomePopUp.alertAllowContact(context);
//       return chk;
//     }
//   }
//
//   checkNotificationPermission() async {
//     bool hasNotificationPermission =
//         await Get.find<AppConfigController>().checkNotificationPermission();
//     if (hasNotificationPermission) {
//       print('Permission granted');
//       // Permission granted, proceed with notifications
//     } else {
//       print('Permission denied');
//       // Permission denied or not yet requested, handle accordingly
//       HomePopUp.alertAllowNotification(context);
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return PersistentTabView(
//       context,
//       navBarHeight: 72.h,
//       controller: Get.put(NavigatorContoller()).tabController,
//       screens: screens,
//       items: _navBarsItems(),
//       confineInSafeArea: true,
//       padding: NavBarPadding.only(left: 73.w,right: 73.w),
//       backgroundColor: Colors.white.withOpacity(0.8999999761581421),
//       handleAndroidBackButtonPress: true,
//       resizeToAvoidBottomInset: true,
//       stateManagement: true,
//       hideNavigationBarWhenKeyboardShows: true,
//       decoration: NavBarDecoration(
//         boxShadow: [
//           BoxShadow(
//             offset: const Offset(0, 0),
//             color: Colors.black.withOpacity(0.04),
//             blurRadius: 30.0,
//           ),
//         ]
//       //     border: Border(
//       //     top: BorderSide(
//       //     color: Color(0x191A1818).withOpacity(0.5),
//       //     width: 0.5,
//       //   ),
//       // )
//       ),
//       popAllScreensOnTapOfSelectedTab: true,
//       itemAnimationProperties: const ItemAnimationProperties(
//         duration: Duration(milliseconds: 200),
//         curve: Curves.ease,
//       ),
//       screenTransitionAnimation: const ScreenTransitionAnimation(
//         animateTabTransition: true,
//         curve: Curves.ease,
//         duration: Duration(milliseconds: 200),
//       ),
//       navBarStyle: NavBarStyle.style3,
//     );
//   }
//
//   // List<Widget> screens = [HomePage(),NotifyPage()];
//   List<Widget> screens = [HomePage(),MRPage() ,NotifyPage()];
//
//   //Tab bar
//   buildTabBarView() {
//     return TabBarView(
//       physics: const NeverScrollableScrollPhysics(),
//       controller: homeTabController,
//       children: [HomePage(), Text('sddsdsd')],
//     );
//   }
//
//   buildFloatingButton() {
//     return Column(
//       mainAxisAlignment: MainAxisAlignment.end,
//       children: [
//         Container(
//           width: Get.width,
//           height: 72.h,
//           // padding: EdgeInsets.only(bottom: 10.h),
//           color: Colors.blue,
//           // decoration: BoxDecoration(
//           //   gradient: LinearGradient(
//           //     begin: const Alignment(0.00, -1.00),
//           //     end: const Alignment(0, 1),
//           //     colors: [
//           //       Colors.white.withOpacity(0.8999999761581421),
//           //       Colors.white
//           //     ],
//           //   ),
//           // ),
//           child: TabBar(
//             controller: homeTabController,
//             indicatorColor: Colors.transparent,
//             indicator: const BoxDecoration(),
//             tabs: [
//               Tab(
//                   child: currentIndex == 0
//                       ? Container(
//                           height: 21,
//                           width: 21,
//                           color: Colors.red,
//                         )
//                       : Container()),
//               Tab(
//                   child: currentIndex == 1
//                       ? Container(
//                           height: 21,
//                           width: 21,
//                           color: Colors.cyan,
//                         )
//                       : Container()),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
// }

class HomeNavigator extends StatefulWidget {
  @override
  State<HomeNavigator> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<HomeNavigator> {
  final NavigationController navigationController =
      Get.put(NavigationController());
  GetStorage storage = GetStorage();

  final List<Widget> _screens = [
    HomePage(),
    MRPage(),
    NotifyPage(),
  ];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () async {
      // _checkForUpdates();
      checkGuestData();
      // await Future.delayed(Duration(seconds: 1));
      // await storage.read('isShow');
      // await storage.read('isShowTutorial');
      // AAMPolicy.alertTermAndPolicy(context);
    });
  }

  //TODO pop loading patch
  Future<void> _checkForUpdates() async {
    final shorebirdCodePush = ShorebirdCodePush();
    final isUpdateAvailable =
        await shorebirdCodePush.isNewPatchAvailableForDownload();
    if (isUpdateAvailable) {
      // Show the update popup.
      showDialog(
          barrierDismissible: false,
          context: context,
          useSafeArea: false,
          builder: (_) => PatchPopUp());
    }
  }

  Future<void> checkGuestData() async {
    try {
      var isGuest = await storage.read('isGuest');
      var isLogin = await storage.read('token');
      debugPrint('isGuest: $isGuest');
      if (isGuest != true) {
        final ProfileController profileController = Get.put(ProfileController());
        await profileController.getProfile();
        await profileController.separateAddress();

        if (_shouldCheckPolicy(profileController)) {
          var chkAcceptPolicy = await TermsAndConditionWidget.alertTermAndPolicy(context);
          if (chkAcceptPolicy && !kIsWeb) {
            await checkContractPermission().then((value) => print(value));
            checkNotificationPermission();
          }
        } else {
          print("dadasdsddsd else ####");
          if(!kIsWeb){
            var contract = await checkContractPermission();
            if(contract){
              print("already accepted contract");
            }
            checkNotificationPermission();
          }
        }

      }
    } catch (error) {
      // Handle error appropriately
      print('Error checking guest data: $error');
      // Get.put(SessionController()).logout(context); //TODO: Destroy Session And Logout
    }
  }

  bool _shouldCheckPolicy(ProfileController profileController) {
    return profileController.checkPolicy!.value == false &&
        profileController.updatePolicy!.value.isNotEmpty;
  }

  checkContractPermission() async {
    final box = GetStorage();
    bool hasContractsPermission =
        await Get.find<AppConfigController>().checkContractsPermission();
    // bool hasShownPrompt = box.read('hasShownPrompt') ?? false;
    // print('hasShownPrompt : ${hasShownPrompt}');
    // print("hasContractsPermission:  $hasContractsPermission");
    // if (hasShownPrompt) {
    //   return false; // ไม่ต้องแสดง prompt อีก
    // }
    if (hasContractsPermission) {
      print('Contract Permission granted');
      // Permission granted, proceed with notifications
      return true;
    } else {
      print('Contract Permission denied');
      // Permission denied or not yet requested, handle accordingly
      var chk;
      if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
        chk = await HomePopUp.alertAllowContactAAM(context);
      } else if (AppConfig.of(context).countryConfigCollection.toString() ==
          'rplc') {
        chk = await HomePopUp.alertAllowContactRPLC(context);
      } else {
        chk = await HomePopUp.alertAllowContactRAFCO(context);
      }
      box.write('hasShownPrompt', true);
      return chk;
    }
  }

  checkNotificationPermission() async {
    bool hasNotificationPermission =
        await Get.find<AppConfigController>().checkNotificationPermission();

    if (hasNotificationPermission) {
      print('Notification Permission granted');
      // Permission granted, proceed with notifications
    } else {
      print('Notification Permission denied');
      // Permission denied or not yet requested, handle accordingly
      if (AppConfig.of(context).countryConfigCollection.toString() == 'aam') {
        HomePopUp.alertAllowNotificationAAM(context);
      } else if (AppConfig.of(context).countryConfigCollection.toString() ==
          'rplc') {
        HomePopUp.alertAllowNotificationRPLC(context);
      } else {
        HomePopUp.alertAllowNotificationRAFCO(context);
      }
    }
  }

  void _showTutorialPopup() {
    Get.dialog(
      AlertDialog(
        title: Text("Tutorial"),
        content: Text("This is a tutorial popup for MR tab."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text("Close"),
          ),
        ],
      ),
    );
  }

  final MRController mrController = Get.find<MRController>();
  final NotificationController notiController = Get.find<NotificationController>();
  final ProfileController profileController = Get.find<ProfileController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IndexedStack(
          index: navigationController.currentIndex.value,
          children: _screens,
          alignment: Alignment.center,
        );
      }),
      bottomNavigationBar: Obx(() {
        return Container(
          height: 80.h,
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
          ),
          child: BottomNavigationBar(
            elevation: 0,
            currentIndex: navigationController.currentIndex.value,
            onTap: (index) {
              navigationController.changeIndex(index);
              if (!Get.find<HomeController>().isGuest!.value) {
                if (index == 1) {
                  mrController.getMRData();
                  mrController.getReferFriend();
                  // profileController.getProfile();
                } else if (index == 2) {
                  notiController.getNotification(context);
                }
              }
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: configTheme().bottomNavigationBarTheme.selectedItemColor,
            unselectedItemColor: configTheme().bottomNavigationBarTheme.unselectedItemColor,
            items: [
              _buildNavItem(
                icon: '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M12.5 18L12.5 15" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/> <path d="M2.85139 13.2135C2.49837 10.9162 2.32186 9.76763 2.75617 8.74938C3.19047 7.73112 4.15403 7.03443 6.08114 5.64106L7.52099 4.6C9.91829 2.86667 11.1169 2 12.5 2C13.8831 2 15.0817 2.86667 17.479 4.6L18.9189 5.64106C20.846 7.03443 21.8095 7.73112 22.2438 8.74938C22.6781 9.76763 22.5016 10.9162 22.1486 13.2135L21.8476 15.1724C21.3471 18.4289 21.0969 20.0572 19.929 21.0286C18.7611 22 17.0537 22 13.6388 22H11.3612C7.94633 22 6.23891 22 5.071 21.0286C3.90309 20.0572 3.65287 18.4289 3.15243 15.1724L2.85139 13.2135Z" stroke="#FF9300" stroke-width="1.5" stroke-linejoin="round"/></svg>',
                label: bottomBarHome.tr,
                isActive: navigationController.currentIndex.value == 0,
              ),
              _buildNavItem(
                icon: '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M9.5 13C11.7091 13 13.5 11.2091 13.5 9C13.5 6.79086 11.7091 5 9.5 5C7.29086 5 5.5 6.79086 5.5 9C5.5 11.2091 7.29086 13 9.5 13ZM9.5 13C5.63401 13 2.5 15.6863 2.5 19M9.5 13C13.366 13 16.5 15.6863 16.5 19M15.5 5C17.7091 5 19.5 6.79086 19.5 9C19.5 10.6787 18.4659 12.1159 17 12.7092V13L17.5 13.2485C20.3915 13.9861 22.5 16.282 22.5 19" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>',
                label: bottomBarMR.tr,
                isActive: navigationController.currentIndex.value == 1,
              ),
              _buildNavItem(
                icon: '<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M6.94784 7.96942C7.26219 5.14032 9.65349 3 12.5 3V3C15.3465 3 17.7378 5.14032 18.0522 7.96942L18.304 10.2356C18.3072 10.2645 18.3088 10.279 18.3104 10.2933C18.4394 11.4169 18.8051 12.5005 19.3836 13.4725C19.3909 13.4849 19.3984 13.4973 19.4133 13.5222L19.9914 14.4856C20.5159 15.3599 20.7782 15.797 20.7216 16.1559C20.6839 16.3946 20.561 16.6117 20.3757 16.7668C20.0971 17 19.5873 17 18.5678 17H6.43223C5.41268 17 4.90291 17 4.62434 16.7668C4.43897 16.6117 4.31609 16.3946 4.27841 16.1559C4.22179 15.797 4.48407 15.3599 5.00862 14.4856L5.58665 13.5222C5.60161 13.4973 5.60909 13.4849 5.61644 13.4725C6.19488 12.5005 6.56064 11.4169 6.68959 10.2933C6.69123 10.279 6.69283 10.2645 6.69604 10.2356L6.94784 7.96942Z" stroke="#FF9300" stroke-width="1.5"/> <path d="M9.60222 17.6647C9.77315 18.6215 10.1498 19.467 10.6737 20.0701C11.1976 20.6731 11.8396 21 12.5 21C13.1604 21 13.8024 20.6731 14.3263 20.0701C14.8502 19.467 15.2269 18.6215 15.3978 17.6647" stroke="#FF9300" stroke-width="1.5" stroke-linecap="round"/></svg>',
                label: bottomBarNotify.tr,
                isActive: navigationController.currentIndex.value == 2,
              ),
            ],
          ),
        );
      }),
    );
  }


  BottomNavigationBarItem _buildNavItem({
    required String icon,
    required String label,
    required bool isActive,
  }) {
    return BottomNavigationBarItem(
      icon: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 24.h,  // Icon's fixed height
            child: SvgPicture.string(
              icon,
              fit: BoxFit.cover,
              color: isActive
                  ? configTheme().bottomNavigationBarTheme.selectedItemColor
                  : configTheme().bottomNavigationBarTheme.unselectedItemColor,
            ),
          ),
          SizedBox(height: 4.h,),
          FittedBox(
            fit: BoxFit
                .scaleDown,
            alignment: Alignment.center,
            child: Text(
              label,
              style: TextStyle(
                fontFamily: configTheme().primaryTextTheme.bodySmall!.fontFamily,
                fontSize: 10.sp,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                color: isActive
                    ? configTheme().bottomNavigationBarTheme.selectedItemColor
                    : configTheme().bottomNavigationBarTheme.unselectedItemColor,
              ),
            ),
          ),
        ],
      ),
      label: '',
    );
  }
}
