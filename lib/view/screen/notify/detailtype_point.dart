import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:get/get.dart';

import '../../../controller/notification/notification.controllet.dart';
import '../../componance/AppBackgound.dart';
import '../../componance/themes/app_textstyle.dart';
import '../../componance/utils/AppImageAssets.dart';
import '../../componance/widgets/componance_widget.dart';
class detail_type_point extends StatefulWidget {
  const detail_type_point( this.id,this.title, this.status, this.detail, this.day, this.ctt_code, this.index,this.idNotification, this.readed, this.typeNotification,
      {super.key});
  final String? id;
  final String? title;
  final String? status;
  final String? detail;
  final String? day;
  final String? ctt_code;
  final int? index;
  final  idNotification;
  final  readed;
  final  typeNotification;

  @override
  State<detail_type_point> createState() => _detail_type_pointState();
}

class _detail_type_pointState extends State<detail_type_point> {
  @override
  Widget build(BuildContext context) {
    List<String> parts = widget.day!.split(" - ");
    final title = widget.title; // เรียกใช้ข้อมูล title จาก widget
    final status = widget.status; // เรียกใช้ข้อมูล status จาก widget
    final detail = widget.detail; // เรียกใช้ข้อมูล detail จาก widget
    String day = parts[0];
    final  typeNotification =widget.typeNotification;

    final id = widget.id;
    final   readed = widget.readed;
    String time = parts[1];
    final index = widget.index; // เรียกใช้ข้อมูล index จาก widget
    final ctt_code = widget.ctt_code; // เรียกใช้ข้อมูล index จาก widget

    return  Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: SizedBox(
              height: 954.h,
              child: GetBuilder<NotificationController>(builder: (notiCtl) {
                return Column(
                  children: [
                    AppBackground.backgroundColorHomePage(context),
                    detailBody(id,title, status, detail, day, ctt_code, index,time,typeNotification)

                  ],
                );
              }),
            ),
          ),
          headerNoti(context),

        ],
      ),
    );
  }
  Widget _buildNotiEmptyPage(context) {
    return Container(
      width: Get.width,
      height: Get.height,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 123.w,
            height: 122.h,
            child: Image.asset(AppImageAssets.aam_empty_noti),
          ),
          SizedBox(height: 10.h),
          Container(
            width: 375.w,
            height: 24.h,
            child: Text(
              'ไม่มีรายการแจ้งเตือน',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 16,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          Container(
            width: 375.w,
            height: 21.h,
            child: Text(
              'ไม่พบข้อมูลแจ้งเตือนในหมวดนี้',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 14,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  headerNoti(context) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {
      return Stack(
        children: [
          Container(
            width: 375.w,
            height: 136.h,
            padding: EdgeInsets.only(
              top: 56.h,
              left: 12.w,
              right: 12.w,
              bottom: 12.h,
            ),
            color: Colors.white.withOpacity(0.9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                    onPressed: () {


                      Get.back();
                    },
                    icon: Icon(Icons.arrow_back)),


                SizedBox(height: 34.h,width: 34.w, child:
                Image.asset('assets/notify/icon2.png')

                  ,),
                SizedBox(width: 10.w,),


                Container(
                  alignment: Alignment.center,
                  child: Text(
                    'ระบบทั่วไป',
                    style: TextStyle(
                      color: Color(0xFF1A1818),
                      fontSize: 14,
                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: Get.width,
            height: 32.h,
          ),
          Container(
            width: 375.w,
            height: 136.h,
            child: ComponanceWidget.buildDivider(),
          ),
        ],
      );
    });
  }


  Widget detailBody(
      id,
      title,
      status,
      detail,
      day,
      ctt_code,
      index,
      time,
      typeNotification
      ) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {


      return SizedBox(
        height: Get.height,
        width: Get.width,
        child: SingleChildScrollView(
          child: Column(

            children: [
              Container(
                height: Get.height,
                width: Get.width,

                child: Obx(()=>
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: ListView.builder (
                          itemCount: notiCtl.notificationList!.where((item) => item.idNotification == id).length,
                          itemBuilder: (BuildContext context, int index) {
                            // Access the notification at the current index


                            return Container(
                              height: Get.height,

                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  // ยังไม่ได้ชำระ

                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Center(child: Text(day)),
                                  ),

                                  Column(
                                    children: [
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.end
                                        ,mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(right: 8.0),
                                            child: Text(time),
                                          ),
                                          Container(

                                            width: 260.w,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.circular(8),
                                              border: Border(
                                                left: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                top: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                right: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                bottom: BorderSide(
                                                    width: 1.50.w, color: Color(0xFF792AFF).withOpacity(0.1)),
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.transparent,
                                                  blurRadius: 10,
                                                  offset: Offset(2, 4),
                                                  spreadRadius: 0,
                                                )
                                              ],
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.all(16.0),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    title,
                                                    style: TextStyle(
                                                      color: Color(0xFF1A1818),
                                                      fontSize: 14,
                                                      fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                                      fontWeight: FontWeight.w700,
                                                    ),
                                                  ),
                                                  Column(
                                                    children: [
                                                      Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [

                                                          Container(
                                                            width: 240.w,
                                                            child: Column(
                                                              children: [
                                                                Column(
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                                  children: [
                                                                    Text(
                                                                      detail,
                                                                      textAlign: TextAlign.start,
                                                                      maxLines: 3, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                                                      overflow: TextOverflow
                                                                          .ellipsis, // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
                                                                      style: TextStyle(
                                                                        color: Color(0xFF1A1818),
                                                                        fontSize: 14,
                                                                        fontFamily:
                                                                        TextStyleTheme.text_Regular.fontFamily,
                                                                        fontWeight: FontWeight.w400,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),

                                                    ],
                                                  ),
                                                  Padding(
                                                    padding: const EdgeInsets.only(top: 12.0),
                                                    child: Container(
                                                      width: Get.width,
                                                      height: 36.h,
                                                      decoration: BoxDecoration(
                                                        color: Color(0xFF792AFF),
                                                        borderRadius: BorderRadius.circular(8),
                                                        border: Border(
                                                          left: BorderSide(color: Color(0xFF792AFF)),
                                                          top: BorderSide(color: Color(0xFF792AFF)),
                                                          right: BorderSide(color: Color(0xFF792AFF)),
                                                          bottom: BorderSide(
                                                              width: 1.50.w, color: Color(0xFF792AFF)),
                                                        ),
                                                        boxShadow: [
                                                          BoxShadow(
                                                            color: Color(0x72792AFF),
                                                            blurRadius: 10,
                                                            offset: Offset(2, 4),
                                                            spreadRadius: 0,
                                                          )
                                                        ],
                                                      ),
                                                      child: Row(
                                                        crossAxisAlignment: CrossAxisAlignment.center,
                                                        mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          SizedBox(height: 24.h,width: 24.w, child:
                                                          Image.asset('assets/notify/Chat_alt_3_light.png')

                                                            ,),
                                                          SizedBox(width: 4.w,),
                                                          Container(
                                                            alignment: Alignment.center,
                                                            child: Text(
                                                              'ติดต่อสอบถาม',
                                                              textAlign: TextAlign.center,
                                                              style: TextStyle(
                                                                color: Colors.white,
                                                                fontSize: 12,
                                                                fontFamily: TextStyleTheme
                                                                    .text_Regular.fontFamily,
                                                                fontWeight: FontWeight.w600,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),

                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 10.h),
                                      Container(
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.end
                                          ,mainAxisAlignment: MainAxisAlignment.end,
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.only(right: 8.0),
                                              child: Text(time),
                                            ),
                                            Container(

                                              width: 260.w,
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                borderRadius: BorderRadius.circular(8),
                                                border: Border(
                                                  left: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                  top: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                  right: BorderSide(color: Color(0xFF792AFF).withOpacity(0.1)),
                                                  bottom: BorderSide(
                                                      width: 1.50.w, color: Color(0xFF792AFF).withOpacity(0.1)),
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.transparent,
                                                    blurRadius: 10,
                                                    offset: Offset(2, 4),
                                                    spreadRadius: 0,
                                                  )
                                                ],
                                              ),
                                              child: Padding(
                                                padding: const EdgeInsets.all(16.0),
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                      width: 240.w,
                                                      height: 28.h,
                                                      padding: EdgeInsets.zero, // หรือใช้ EdgeInsets.all(0) ก็ได้
                                                      child: Text(
                                                        title,
                                                        style: TextStyle(
                                                          color: Color(0xFF1A1818),
                                                          fontSize: 14,
                                                          fontFamily: TextStyleTheme.text_Regular.fontFamily,
                                                          fontWeight: FontWeight.w700,
                                                        ),
                                                      ),
                                                    ),
                                                    Container(
                                                      width: 240.w,
                                                      child: Column(
                                                        children: [
                                                          Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            mainAxisAlignment: MainAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                detail,
                                                                textAlign: TextAlign.start,
                                                                maxLines: 3, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                                                overflow: TextOverflow
                                                                    .ellipsis, // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
                                                                style: TextStyle(
                                                                  color: Color(0xFF1A1818),
                                                                  fontSize: 14,
                                                                  fontFamily:
                                                                  TextStyleTheme.text_Regular.fontFamily,
                                                                  fontWeight: FontWeight.w400,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),

                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],),
                            );
                          }
                      ),
                    ),
                ),
              ),

            ],
          ),
        ),
      );
    }
    );
  }


}