import 'package:AAMG/controller/home/<USER>';
import 'package:AAMG/controller/navigator.controller.dart';
import 'package:AAMG/controller/transalation/translation_key.dart';
import 'package:AAMG/view/componance/themes/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:AAMG/view/componance/themes/app_textstyle.dart';
import 'package:AAMG/view/componance/utils/AppImageAssets.dart';
import 'package:AAMG/view/componance/utils/AppSvgImage.dart';
import 'package:get_storage/get_storage.dart';

import '../../../controller/notification/notification.controllet.dart';
import '../../componance/AppBackgound.dart';
import '../../componance/widgets/componance_widget.dart';
import '../register/register_page.dart';
import 'detailtype_news.dart';
import 'detailtype_point.dart';
import 'detailtype_service.dart';

class NotifyPage extends StatefulWidget {
  const NotifyPage({Key? key}) : super(key: key);

  @override
  State<NotifyPage> createState() => _NotifyPageState();
}

class _NotifyPageState extends State<NotifyPage> {
  bool isDeleted = false;
  int tricker_count = 0;
  var isGuest = false;

  final NavigationController navigationController = Get.find<NavigationController>();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future.delayed(Duration.zero, () async {
      // Register NotifyController with lazyPut and fenix: true
      Get.lazyPut<NotificationController>(() => NotificationController(),
          fenix: true);
      //  // Ensure that the NotifyController is initialized
      //  final NotificationController controller = Get.find();
      // Get.put(NotificationController()).getNotification(context);
    });

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   // checkGuestData();
    // });
  }

  // void checkGuestData() async {
  //   GetStorage storage = GetStorage();
  //   isGuest = await storage.read('isGuest');
  //
  //   if (isGuest != true) {
  //     notiCtl.getNotification(context);
  //   } else {
  //     notiCtl.Loading.value = false;
  //     notiCtl.update();
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () {
          //TODO โหลดข้อมูลใหม่
          Get.find<NotificationController>().getNotification(context);
          return Future.value(true);
        },
        child: Stack(
          children: [
            // Background color or image
            AppBackground.backgroundColorHomePage(context),
            Container(
              child: SizedBox(
                height:
                    MediaQuery.of(context).size.height, // Ensure full height 954
                child: GetBuilder<NotificationController>(
                  builder: (notiCtl) {
                    print('Loading: ${notiCtl.Loading.value}');
                    // Check loading status
                    if (notiCtl.Loading.value) {
                      return Center(child: CircularProgressIndicator());
                    }
        
                    // Display notification list
                    return Center(child: _buildNotiPage(context));
                  },
                ),
              ),
            ),
            headerNoti(context),
          ],
        ),
      ),
    );
  }

  headerNoti(context) {
    return GetBuilder<NotificationController>(builder: (notiCtl) {
      final box = GetStorage();
      return Stack(
        children: [
          Container(
            width: 375.w,
            height: 136.h,
            padding: EdgeInsets.only(
              top: 56.h,
              left: 24.w,
              right: 24.w,
              bottom: 12.h,
            ),
            color: Colors.white.withOpacity(0.9),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: Get.width,
                  height: 24.h,
                  child: Row(
                    // mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        child: Text(
                          notification.tr,
                          style: TextStyle(
                            color: Color(0xFF1A1818),
                            fontSize: 14,
                            fontFamily: TextStyleTheme.text_Regular.fontFamily,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          DeleteReaded(context, () {
                            notiCtl.removeReadZeroNotifications();
                          }),
                          // SizedBox(
                          //   width: 15.w,
                          // ),
                          // appConfigService.countryConfigCollection.toString() ==
                          //     "aam"
                          //     ? InkWell(
                          //   onTap: () {
                          //     setState(() {
                          //       print('NotificationPopUp');
                          //       var i = box.read('isNotificationPopupShown');
                          //       print(i);
                          //       if(i == true){
                          //         i = box.write("isNotificationPopupShown", null);
                          //         print("iPopUp");
                          //         print(i);
                          //         setState(() {
                          //         navigationController.showNotificationPopup();
                          //         });
                          //       }
                          //       // print("MR");
                          //       // var i = box.read('isMRPopupShown');
                          //       // print(i);
                          //       // if (i == true) {
                          //       //   i = box.write("isMRPopupShown", null);
                          //       //   print(i);
                          //       //   navigationController.update();
                          //       //   navigationController.showPopupMR();
                          //       // }
                          //     });
                          //   },
                          //   child: Container(
                          //       height: 24.h,
                          //       width: 24.w,
                          //       child: SvgPicture.string(
                          //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3242_15175)"> <path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 8.10001V12.89" stroke="#792AFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/> <path d="M12 16.5H12.01" stroke="#792AFF" stroke-width="2" stroke-linecap="round"/></g><defs> <clipPath id="clip0_3242_15175"> <rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                          // )
                          //     : appConfigService.countryConfigCollection
                          //     .toString() ==
                          //     "rafco"
                          //     ? InkWell(
                          //   onTap: () {
                          //     setState(() {
                          //       print('NotificationPopUp');
                          //       var i = box.read('isNotificationPopupShown');
                          //       print(i);
                          //       if(i == true){
                          //         i = box.write("isNotificationPopupShown", null);
                          //         print("iPopUp");
                          //         print(i);
                          //         setState(() {
                          //           navigationController.showNotificationPopup();
                          //         });
                          //       }
                          //       // print("MR");
                          //       // var i = box.read('isMRPopupShown');
                          //       // print(i);
                          //       // if (i == true) {
                          //       //   i = box.write("isMRPopupShown", null);
                          //       //   print(i);
                          //       //   navigationController.update();
                          //       //   navigationController.showPopupMR();
                          //       // }
                          //     });
                          //   },
                          //       child: Container(
                          //       child: SvgPicture.string(
                          //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_3898_67041)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.10001V12.89" stroke="#EA1B23" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#EA1B23" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3898_67041"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                          //     )
                          //     : InkWell(
                          //   onTap: () {
                          //     setState(() {
                          //       print('NotificationPopUp');
                          //       var i = box.read('isNotificationPopupShown');
                          //       print(i);
                          //       if(i == true){
                          //         i = box.write("isNotificationPopupShown", null);
                          //         print("iPopUp");
                          //         print(i);
                          //         setState(() {
                          //           navigationController.showNotificationPopup();
                          //         });
                          //       }
                          //       // print("MR");
                          //       // var i = box.read('isMRPopupShown');
                          //       // print(i);
                          //       // if (i == true) {
                          //       //   i = box.write("isMRPopupShown", null);
                          //       //   print(i);
                          //       //   navigationController.update();
                          //       //   navigationController.showPopupMR();
                          //       // }
                          //     });
                          //   },
                          //       child: Container(
                          //       child: SvgPicture.string(
                          //           '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_3696_32814)"><path d="M21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12Z" stroke="#1A1818" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 8.09998V12.89" stroke="#FFC20E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M12 16.5H12.01" stroke="#FFC20E" stroke-width="2" stroke-linecap="round"/></g><defs><clipPath id="clip0_3696_32814"><rect width="24" height="24" fill="white"/></clipPath></defs></svg>')),
                          //     )
                        ],
                      ),

                    ],
                  ),
                ),
                SizedBox(height: 12.h),
                Container(
                  width: Get.width,
                  height: 32.h,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      GetBuilder<NotificationController>(builder: (notiCtl) {
                        // สร้าง List ของ Widgets สำหรับแสดงตัวอินดิเคเตอร์แจ้งเตือน
                        List<Widget> notificationIndicators = notiCtl
                            .notificationList!
                            .where((notification) =>
                                notification.typeNotification == '0' ||
                                notification.typeNotification == "" ||
                                notification.typeNotification == "-" ||
                                notification.typeNotification == "Nextpayaut" ||
                                notification.typeNotification == "claimrewar" ||
                                notification.typeNotification == "requestIns" ||
                                notification.typeNotification == "localNoti" ||
                                notification.typeNotification == null ||
                                notification.typeNotification == "evaluation" ||
                                notification.typeNotification == "bill" &&
                                    notification.statusnotification == '')
                            .map((notification) {
                          return Container(
                            alignment: Alignment.topRight,
                            margin: EdgeInsets.only(top: 6.h, right: 6.w),
                            child: Opacity(
                              opacity: 1,
                              child: Container(
                                width: 6,
                                height: 6,
                                decoration: ShapeDecoration(
                                  color: appConfigService
                                              .countryConfigCollection
                                              .toString() ==
                                          'aam'
                                      ? Color(0xFFFF9300)
                                      : appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'rplc'
                                          ? Color(0xFFFFC20E)
                                          : Color(0xFF22409A),
                                  shape: OvalBorder(),
                                ),
                              ),
                            ),
                          );
                        }).toList();
                        print(notificationIndicators.length);
                        return Container(
                          width: 89.w,
                          height: 32.h,
                          decoration: BoxDecoration(
                            color: tricker_count == 0
                                ? appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? Color(0xFF792AFF)
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? Color(0xFFFFC20E)
                                        : Color(0xFFEA1B23)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            // border: tricker_count == 0
                            //     ? Border(
                            //         left: BorderSide(
                            //             color: appConfigService
                            //                         .countryConfigCollection
                            //                         .toString() ==
                            //                     'aam'
                            //                 ? Color(0xFF792AFF)
                            //                 : appConfigService
                            //                             .countryConfigCollection
                            //                             .toString() ==
                            //                         'rplc'
                            //                     ? Color(0xFFFFC20E)
                            //                     : Color(0xFFEA1B23)),
                            //         top: BorderSide(
                            //             color: appConfigService
                            //                         .countryConfigCollection
                            //                         .toString() ==
                            //                     'aam'
                            //                 ? Color(0xFF792AFF)
                            //                 : appConfigService
                            //                             .countryConfigCollection
                            //                             .toString() ==
                            //                         'rplc'
                            //                     ? Color(0xFFFFC20E)
                            //                     : Color(0xFFEA1B23)),
                            //         right: BorderSide(
                            //             color: appConfigService
                            //                         .countryConfigCollection
                            //                         .toString() ==
                            //                     'aam'
                            //                 ? Color(0xFF792AFF)
                            //                 : appConfigService
                            //                             .countryConfigCollection
                            //                             .toString() ==
                            //                         'rplc'
                            //                     ? Color(0xFFFFC20E)
                            //                     : Color(0xFFEA1B23)),
                            //         bottom: BorderSide(
                            //             width: 1.50,
                            //             color: Color(0xFF1A1818).withOpacity(1)
                            //             // color: appConfigService.countryConfigCollection.toString() == 'aam' ?Color(0xFF792AFF) : appConfigService.countryConfigCollection.toString() == 'rplc' ? Color(0xFFFFC20E) : Color(0xFFEA1B23)),
                            //             ))
                            //     : Border(
                            //         left: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         top: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         right: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         bottom: BorderSide(
                            //             width: 1.50.w,
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //       ),
                            boxShadow: [
                              BoxShadow(
                                color: tricker_count == 0
                                    ? Color(0x72792AFF).withOpacity(0.1)
                                    : Colors.transparent,
                                blurRadius: 10,
                                offset: Offset(2, 4),
                                spreadRadius: 0,
                              )
                            ],
                          ),
                          child: Stack(
                            children: [
                              InkWell(
                                onTap: () {
                                  notiCtl.setTrickerTypeNoti(0);
                                  setState(() {
                                    tricker_count = 0;
                                  });
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        notificationService.tr,
                                        textAlign: TextAlign.start,
                                        maxLines:
                                            2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: tricker_count == 0
                                              ? Colors.white.withOpacity(1)
                                              : Colors.black.withOpacity(1),
                                          fontSize: 14,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    // แสดงตัวอินดิเคเตอร์แจ้งเตือน
                                    ...notificationIndicators,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      SizedBox(width: 8.w),
                      GetBuilder<NotificationController>(builder: (notiCtl) {
                        // สร้าง List ของ Widgets สำหรับแสดงตัวอินดิเคเตอร์แจ้งเตือน
                        List<Widget> notificationIndicators = notiCtl
                            .notificationList!
                            .where((notification) =>
                                notification.typeNotification == "claimlike" ||
                                notification.typeNotification == "claimlike_" ||
                                notification.typeNotification == "recommend_" ||
                                notification.typeNotification ==
                                        "recommend_mr" &&
                                    notification.statusnotification == '')
                            .map((notification) {
                          return Container(
                            alignment: Alignment.topRight,
                            margin: EdgeInsets.only(top: 6.h, right: 6.w),
                            child: Opacity(
                              opacity: 1,
                              child: Container(
                                width: 6,
                                height: 6,
                                decoration: ShapeDecoration(
                                  color: appConfigService
                                              .countryConfigCollection
                                              .toString() ==
                                          'aam'
                                      ? Color(0xFFFF9300)
                                      : appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'rplc'
                                          ? Color(0xFFFFC20E)
                                          : Color(0xFF22409A),
                                  shape: OvalBorder(),
                                ),
                              ),
                            ),
                          );
                        }).toList();
                        return Container(
                          width: 89.w,
                          height: 32.h,
                          decoration: BoxDecoration(
                            color: tricker_count == 1
                                ? appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? Color(0xFF792AFF)
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? Color(0xFFFFC20E)
                                        : Color(0xFFEA1B23)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            // border: tricker_count == 1
                            //     ? Border(
                            //         left: BorderSide(color: Color(0xFF792AFF)),
                            //         top: BorderSide(color: Color(0xFF792AFF)),
                            //         right: BorderSide(color: Color(0xFF792AFF)),
                            //         bottom: BorderSide(
                            //             width: 1.50.w,
                            //             color: Color(0xFF792AFF)),
                            //       )
                            //     : Border(
                            //         left: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         top: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         right: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         bottom: BorderSide(
                            //             width: 1.50.w,
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //       ),
                            boxShadow: [
                              BoxShadow(
                                color: tricker_count == 1
                                    ? Color(0x72792AFF).withOpacity(0.1)
                                    : Colors.transparent,
                                blurRadius: 10,
                                offset: Offset(2, 4),
                                spreadRadius: 0,
                              )
                            ],
                          ),
                          child: Stack(
                            children: [
                              InkWell(
                                onTap: () {
                                  notiCtl.setTrickerTypeNoti(1);
                                  setState(() {
                                    tricker_count = 1;
                                  });
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        notificationPoint.tr,
                                        textAlign: TextAlign.start,
                                        maxLines:
                                            2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: tricker_count == 1
                                              ? Colors.white
                                              : Colors.black,
                                          fontSize: 14,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    // แสดงตัวอินดิเคเตอร์แจ้งเตือน
                                    ...notificationIndicators,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                      SizedBox(width: 8.w),
                      GetBuilder<NotificationController>(builder: (notiCtl) {
                        // สร้าง List ของ Widgets สำหรับแสดงตัวอินดิเคเตอร์แจ้งเตือน
                        List<Widget> notificationIndicators = notiCtl
                            .notificationList!
                            .where((notification) =>
                                notification.typeNotification == "notify" ||
                                notification.typeNotification == "news" &&
                                    notification.statusnotification == '')
                            .map((notification) {
                          return Container(
                            alignment: Alignment.topRight,
                            margin: EdgeInsets.only(top: 6.h, right: 6.w),
                            child: Opacity(
                              opacity: 1,
                              child: Container(
                                width: 6,
                                height: 6,
                                decoration: ShapeDecoration(
                                  color: appConfigService
                                              .countryConfigCollection
                                              .toString() ==
                                          'aam'
                                      ? Color(0xFFFF9300)
                                      : appConfigService.countryConfigCollection
                                                  .toString() ==
                                              'rplc'
                                          ? Color(0xFFFFC20E)
                                          : Color(0xFF22409A),
                                  shape: OvalBorder(),
                                ),
                              ),
                            ),
                          );
                        }).toList();
                        return Container(
                          width: 89.w,
                          height: 32.h,
                          decoration: BoxDecoration(
                            color: tricker_count == 2
                                ? appConfigService.countryConfigCollection
                                            .toString() ==
                                        'aam'
                                    ? Color(0xFF792AFF)
                                    : appConfigService.countryConfigCollection
                                                .toString() ==
                                            'rplc'
                                        ? Color(0xFFFFC20E)
                                        : Color(0xFFEA1B23)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            // border: tricker_count == 2
                            //     ? Border(
                            //         left: BorderSide(color: Color(0xFF792AFF)),
                            //         top: BorderSide(color: Color(0xFF792AFF)),
                            //         right: BorderSide(color: Color(0xFF792AFF)),
                            //         bottom: BorderSide(
                            //             width: 1.50.w,
                            //             color: Color(0xFF792AFF)),
                            //       )
                            //     : Border(
                            //         left: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         top: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         right: BorderSide(
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //         bottom: BorderSide(
                            //             width: 1.50.w,
                            //             color:
                            //                 Color(0xFF1A1818).withOpacity(0.1)),
                            //       ),
                            boxShadow: [
                              BoxShadow(
                                color: tricker_count == 2
                                    ? Color(0x72792AFF).withOpacity(0.1)
                                    : Colors.transparent,
                                blurRadius: 10,
                                offset: Offset(2, 4),
                                spreadRadius: 0,
                              )
                            ],
                          ),
                          child: Stack(
                            children: [
                              // Container(
                              //   alignment: Alignment.topRight,
                              //   margin: EdgeInsets.only(top: 6.h, right: 6.w),
                              //   child: Opacity(
                              //     opacity: 1,
                              //     child: Container(
                              //       width: 6,
                              //       height: 6,
                              //       decoration: ShapeDecoration(
                              //         color: Color(0xFFFF9300),
                              //         shape: OvalBorder(),
                              //       ),
                              //     ),
                              //   ),
                              // ),
                              InkWell(
                                onTap: () {
                                  notiCtl.setTrickerTypeNoti(2);
                                  setState(() {
                                    tricker_count = 2;
                                  });
                                },
                                child: Stack(
                                  children: [
                                    Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        notificationPromotion.tr,
                                        textAlign: TextAlign.start,
                                        maxLines:
                                            2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          color: tricker_count == 2
                                              ? Colors.white
                                              : Colors.black,
                                          fontSize: 14,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                    // แสดงตัวอินดิเคเตอร์แจ้งเตือน
                                    ...notificationIndicators,
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                )
              ],
            ),
          ),
          Container(
            width: 375.w,
            height: 136.h,
            child: ComponanceWidget.buildDivider(),
          ),
        ],
      );
    });
  }

  Widget _buildNotiEmptyPage(context) {
    return Container(
      width: Get.width,
      height: Get.height,
      alignment: Alignment.topCenter,
      margin: EdgeInsets.only(top: 216.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 123.w,
            height: 122.h,
            child: Image.asset(appConfigService.countryConfigCollection
                        .toString() ==
                    'aam'
                ? AppImageAssets.aam_empty_noti
                : appConfigService.countryConfigCollection.toString() == 'rplc'
                    ? AppImageAssets.rplc_empty_noti
                    : AppImageAssets.rafco_empty_noti),
          ),
          SizedBox(height: 10.h),
          Container(
            width: 375.w,
            height: 24.h,
            child: Text(
              notificationNoneList.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 16,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          Container(
            width: 375.w,
            height: 21.h,
            child: Text(
              notificationNoneListCategory.tr,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Color(0xFF1A1818),
                fontSize: 14,
                fontFamily: TextStyleTheme.text_Regular.fontFamily,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotiPage(BuildContext context) {
    return Container(
      color: Colors.white,
      width: Get.width,
      height: 740.h,
      child: GetBuilder<NotificationController>(
        builder: (notiCtl) {
          if (isGuest == true) {
            return _buildNotiEmptyPage(context);
          }
          if (tricker_count == 0) {
            return notiCtl.notificationList!.isEmpty
                ? _buildNotiEmptyPage(context)
                : _buildServicePage(context);
          } else if (tricker_count == 1) {
            return notiCtl.notificationList!.isEmpty
                ? _buildNotiEmptyPage(context)
                : _buildPointsPage(context);
          } else if (tricker_count == 2) {
            return notiCtl.notificationList!.isEmpty
                ? _buildNotiEmptyPage(context)
                : _buildNewsPage(context);
          } else {
            return Container();
          }
        },
      ),
    );
  }

  Widget _alertBox(
    BuildContext context,
    String title,
    String typeNoti,
    String detail,
    String day,
    String ctt_code,
    int index,
    Widget pages,
    String read,
    String typeNotification,
    function,
  ) {
    if (typeNotification == "" ||
        typeNotification == "-" ||
        typeNotification == null ||
        typeNotification == "Nextpayaut" ||
        typeNotification == "claimrewar" ||
        typeNotification == "requestIns" ||
        //TODO RPLC, RAFCO
        typeNotification == "localNoti" ||
        typeNotification == "evaluation" ||
        typeNotification == "bill") {
      return Container(
        child: Column(
          children: [
            InkWell(
              onTap: () async {
                print(read);

                await function();

                Get.to(() => pages);
              },
              child: Padding(
                padding: EdgeInsets.only(top: 16),
                child: Container(
                  width: 327.w,
                  height: typeNotification == "Nextpayaut" ? 196.h : 146.h,
                  decoration: BoxDecoration(
                      gradient: LinearGradient(colors: [
                        Color(0xFFFFFFFF).withOpacity(0.9),
                        Color(0xFFFFFFFF)
                      ]),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color:
                              Color(0x0A000000).withOpacity(0.04), // สีของเงา
                          offset: Offset(3, 6), // ตำแหน่งเงาแนวนอนและแนวตั้ง
                          blurRadius: 18, // รัศมีของเงา
                          spreadRadius: 0, // รัศมีการกระจายของเงา
                        ),
                      ],
                      border: Border(
                        bottom: BorderSide(
                          color: configTheme()
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.05),
                          width: 1,
                        ),
                        top: BorderSide(
                          color: configTheme()
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.05),
                          width: 1,
                        ),
                        left: BorderSide(
                          color: configTheme()
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.05),
                          width: 1,
                        ),
                        right: BorderSide(
                          color: configTheme()
                              .colorScheme
                              .onSecondary
                              .withOpacity(0.05),
                          width: 1,
                        ),
                      )),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 42.h,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '$typeNoti',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: configTheme().colorScheme.onSecondary,
                                  fontSize: 12,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              read != 'read'
                                  ? Container(
                                      width: 6.0,
                                      height: 6.0,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: configTheme()
                                            .focusColor, // เปลี่ยนสีตามที่ต้องการ
                                      ),
                                    )
                                  : Container()
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 92.h,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Container(
                                height: 20.h,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '$title',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Color(0xFF1A1818),
                                    fontSize: 14,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: Get.width,
                                height: 44.h,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  // ให้แสดงข้อความของแต่ละแถวที่จุดเริ่มต้น (start)
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '$detail',
                                        textAlign: TextAlign.start,
                                        maxLines: 2,
                                        // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                        overflow: TextOverflow.ellipsis,
                                        // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
                                        style: TextStyle(
                                          color: Color(0xFF1A1818),
                                          fontSize: 14,
                                          fontFamily: TextStyleTheme
                                              .text_Regular.fontFamily,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                height: 20.h,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  '$day',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: Color(0xFF1A1818).withOpacity(0.5),
                                    fontSize: 12,
                                    fontFamily:
                                        TextStyleTheme.text_Regular.fontFamily,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        typeNotification == "Nextpayaut"
                            ? Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 130.w,
                                      height: 36.h,
                                      decoration: BoxDecoration(
                                        color: Color(0xFF792AFF),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border(
                                          left: BorderSide(
                                              color: Color(0xFF792AFF)),
                                          top: BorderSide(
                                              color: Color(0xFF792AFF)),
                                          right: BorderSide(
                                              color: Color(0xFF792AFF)),
                                          bottom: BorderSide(
                                              width: 1.50.w,
                                              color: Color(0xFF792AFF)),
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Color(0x72792AFF),
                                            blurRadius: 10,
                                            offset: Offset(2, 4),
                                            spreadRadius: 0,
                                          )
                                        ],
                                      ),
                                      child: Stack(
                                        children: [
                                          Container(
                                            margin: EdgeInsets.only(
                                                top: 6.h, right: 6.w),
                                            child: Opacity(
                                              opacity: 0,
                                              child: Container(
                                                width: 6,
                                                height: 6,
                                                decoration: ShapeDecoration(
                                                  color: Color(0xFFFF9300),
                                                  shape: OvalBorder(),
                                                ),
                                              ),
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              setState(() {
                                                tricker_count = 0;
                                              });
                                            },
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SizedBox(
                                                  height: 24.h,
                                                  width: 24.w,
                                                  child: Image.asset(
                                                      'assets/notify/solar_scanner-bold-duotone.png'),
                                                ),
                                                SizedBox(
                                                  width: 4.w,
                                                ),
                                                Container(
                                                  alignment: Alignment.center,
                                                  child: Text(
                                                    'จ่ายค่างวด',
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                      fontFamily: TextStyleTheme
                                                          .text_Regular
                                                          .fontFamily,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Container()
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // InkWell(
            //   onTap: () async {
            //     await function();
            //
            //     Get.to(() => pages);
            //   },
            //   child: Padding(
            //     padding: EdgeInsets.only(top: 16),
            //     child: Container(
            //       decoration: BoxDecoration(
            //         gradient: LinearGradient(colors: [
            //           Color(0xFFFFFFFF).withOpacity(0.9),
            //           Color(0xFFFFFFFF)
            //         ]),
            //         borderRadius: BorderRadius.circular(12),
            //         boxShadow: [
            //           BoxShadow(
            //             color: read != 'read'
            //                 ? Colors.transparent
            //                 : Color(0x0A000000), // สีของเงา
            //             offset: Offset(3, 6), // ตำแหน่งเงาแนวนอนและแนวตั้ง
            //             blurRadius: 18, // รัศมีของเงา
            //             spreadRadius: 0, // รัศมีการกระจายของเงา
            //           ),
            //         ],
            //         // border: read != 'read'
            //         //     ? Border(
            //         //         bottom: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.1),
            //         //           width: 1,
            //         //         ),
            //         //         top: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.1),
            //         //           width: 0,
            //         //         ),
            //         //         left: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.1),
            //         //           width: 0,
            //         //         ),
            //         //         right: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.1),
            //         //           width: 0,
            //         //         ),
            //         //       )
            //         //     : Border(
            //         //         bottom: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.05),
            //         //           width: 1,
            //         //         ),
            //         //         top: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.05),
            //         //           width: 1,
            //         //         ),
            //         //         left: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.05),
            //         //           width: 1,
            //         //         ),
            //         //         right: BorderSide(
            //         //           color: Color(0xFF792AFF).withOpacity(0.05),
            //         //           width: 1,
            //         //         ),
            //         //       )
            //       ),
            //       child: Padding(
            //         padding: EdgeInsets.all(16.0),
            //         child: Column(
            //           children: [
            //             Row(
            //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //               children: [
            //                 Text(
            //                   '$typeNoti',
            //                   textAlign: TextAlign.center,
            //                   style: TextStyle(
            //                     color: configTheme().colorScheme.onSecondary,
            //                     fontSize: 12,
            //                     fontFamily:
            //                         TextStyleTheme.text_Regular.fontFamily,
            //                     fontWeight: FontWeight.w600,
            //                   ),
            //                 ),
            //                 read != 'read'
            //                     ? Container(
            //                         width: 6.0,
            //                         height: 6.0,
            //                         decoration: BoxDecoration(
            //                           shape: BoxShape.circle,
            //                           color: Color(
            //                               0xFFFF9300), // เปลี่ยนสีตามที่ต้องการ
            //                         ),
            //                       )
            //                     : Container()
            //               ],
            //             ),
            //             Row(
            //               children: [
            //                 Text(
            //                   '$title',
            //                   textAlign: TextAlign.center,
            //                   style: TextStyle(
            //                     color: Color(0xFF1A1818),
            //                     fontSize: 14,
            //                     fontFamily:
            //                         TextStyleTheme.text_Regular.fontFamily,
            //                     fontWeight: FontWeight.w700,
            //                   ),
            //                 ),
            //               ],
            //             ),
            //             SizedBox(
            //               width: Get.width,
            //               child: Row(
            //                 crossAxisAlignment: CrossAxisAlignment
            //                     .start, // ให้แสดงข้อความของแต่ละแถวที่จุดเริ่มต้น (start)
            //                 children: [
            //                   Expanded(
            //                     child: Text(
            //                       '$detail',
            //                       textAlign: TextAlign.start,
            //                       maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
            //                       overflow: TextOverflow
            //                           .ellipsis, // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
            //                       style: TextStyle(
            //                         color: Color(0xFF1A1818),
            //                         fontSize: 14,
            //                         fontFamily:
            //                             TextStyleTheme.text_Regular.fontFamily,
            //                         fontWeight: FontWeight.w400,
            //                       ),
            //                     ),
            //                   ),
            //                 ],
            //               ),
            //             ),
            //             Row(
            //               children: [
            //                 Text(
            //                   '$day',
            //                   textAlign: TextAlign.center,
            //                   style: TextStyle(
            //                     color: Color(0xFF1A1818).withOpacity(0.5),
            //                     fontSize: 12,
            //                     fontFamily:
            //                         TextStyleTheme.text_Regular.fontFamily,
            //                     fontWeight: FontWeight.w400,
            //                   ),
            //                 ),
            //               ],
            //             ),
            //             typeNotification == "Nextpayaut"
            //                 ? Padding(
            //                     padding: const EdgeInsets.only(top: 8.0),
            //                     child: Row(
            //                       mainAxisAlignment: MainAxisAlignment.start,
            //                       children: [
            //                         Container(
            //                           width: 130.w,
            //                           height: 36.h,
            //                           decoration: BoxDecoration(
            //                             color: Color(0xFF792AFF),
            //                             borderRadius: BorderRadius.circular(8),
            //                             border: Border(
            //                               left: BorderSide(
            //                                   color: Color(0xFF792AFF)),
            //                               top: BorderSide(
            //                                   color: Color(0xFF792AFF)),
            //                               right: BorderSide(
            //                                   color: Color(0xFF792AFF)),
            //                               bottom: BorderSide(
            //                                   width: 1.50.w,
            //                                   color: Color(0xFF792AFF)),
            //                             ),
            //                             boxShadow: [
            //                               BoxShadow(
            //                                 color: Color(0x72792AFF),
            //                                 blurRadius: 10,
            //                                 offset: Offset(2, 4),
            //                                 spreadRadius: 0,
            //                               )
            //                             ],
            //                           ),
            //                           child: Stack(
            //                             children: [
            //                               Container(
            //                                 margin: EdgeInsets.only(
            //                                     top: 6.h, right: 6.w),
            //                                 child: Opacity(
            //                                   opacity: 0,
            //                                   child: Container(
            //                                     width: 6,
            //                                     height: 6,
            //                                     decoration: ShapeDecoration(
            //                                       color: Color(0xFFFF9300),
            //                                       shape: OvalBorder(),
            //                                     ),
            //                                   ),
            //                                 ),
            //                               ),
            //                               InkWell(
            //                                 onTap: () {
            //                                   setState(() {
            //                                     tricker_count = 0;
            //                                   });
            //                                 },
            //                                 child: Row(
            //                                   crossAxisAlignment:
            //                                       CrossAxisAlignment.center,
            //                                   mainAxisAlignment:
            //                                       MainAxisAlignment.center,
            //                                   children: [
            //                                     SizedBox(
            //                                       height: 24.h,
            //                                       width: 24.w,
            //                                       child: Image.asset(
            //                                           'assets/notify/solar_scanner-bold-duotone.png'),
            //                                     ),
            //                                     SizedBox(
            //                                       width: 4.w,
            //                                     ),
            //                                     Container(
            //                                       alignment: Alignment.center,
            //                                       child: Text(
            //                                         'จ่ายค่างวด',
            //                                         textAlign: TextAlign.center,
            //                                         style: TextStyle(
            //                                           color: Colors.white,
            //                                           fontSize: 12,
            //                                           fontFamily: TextStyleTheme
            //                                               .text_Regular
            //                                               .fontFamily,
            //                                           fontWeight:
            //                                               FontWeight.w600,
            //                                         ),
            //                                       ),
            //                                     ),
            //                                   ],
            //                                 ),
            //                               ),
            //                             ],
            //                           ),
            //                         ),
            //                       ],
            //                     ),
            //                   )
            //                 : Container()
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            // )
          ],
        ),
      ); // เรียกใช้ Inkwell1 ถ้า read เป็น 1
    } else if (typeNotification == 'claimlike_' ||
        typeNotification == 'claimlike' ||
        typeNotification == "recommend_" ||
        typeNotification == "recommend_mr") {
      return Column(
        children: [
          InkWell(
            onTap: () async {
              await function();

              Get.to(() => pages);
            },
            child: Padding(
              padding: EdgeInsets.only(top: 16),
              child: Container(
                width: 327.w,
                decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [
                      Color(0xFFFFFFFF).withOpacity(0.9),
                      Color(0xFFFFFFFF)
                    ]),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x0A000000), // สีของเงา
                        offset: Offset(3, 6), // ตำแหน่งเงาแนวนอนและแนวตั้ง
                        blurRadius: 18, // รัศมีของเงา
                        spreadRadius: 0, // รัศมีการกระจายของเงา
                      ),
                    ],
                    border: Border(
                      bottom: BorderSide(
                        color: configTheme()
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.05),
                        width: 1,
                      ),
                      top: BorderSide(
                        color: configTheme()
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.05),
                        width: 1,
                      ),
                      left: BorderSide(
                        color: configTheme()
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.05),
                        width: 1,
                      ),
                      right: BorderSide(
                        color: configTheme()
                            .colorScheme
                            .onSecondary
                            .withOpacity(0.05),
                        width: 1,
                      ),
                    )),
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '$typeNoti',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: configTheme().colorScheme.onSecondary,
                              fontSize: 12,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          read != 'read'
                              ? Container(
                                  width: 6.0,
                                  height: 6.0,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: configTheme()
                                        .focusColor, // เปลี่ยนสีตามที่ต้องการ
                                  ),
                                )
                              : Container()
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            '$title',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818),
                              fontSize: 14.sp,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        width: Get.width,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          // ให้แสดงข้อความของแต่ละแถวที่จุดเริ่มต้น (start)
                          children: [
                            Expanded(
                              child: Text(
                                '$detail',
                                textAlign: TextAlign.start,
                                maxLines: 2,
                                // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                overflow: TextOverflow.ellipsis,
                                // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
                                style: TextStyle(
                                  color: Color(0xFF1A1818),
                                  fontSize: 14,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            '$day',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Color(0xFF1A1818).withOpacity(0.5),
                              fontSize: 12,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          // InkWell(
          //   onTap: () async {
          //     await function();
          //
          //     Get.to(() => pages);
          //   },
          //   child: Stack(
          //     children: [
          //       Padding(
          //         padding: EdgeInsets.only(top: 16),
          //         child: Container(
          //           width: 327.w,
          //           decoration: BoxDecoration(
          //               gradient: LinearGradient(colors: [
          //                 Color(0xFFFFFFFF).withOpacity(0.9),
          //                 Color(0xFFFFFFFF)
          //               ]),
          //               borderRadius: BorderRadius.circular(12),
          //               boxShadow: [
          //                 BoxShadow(
          //                   color: read != 'read'
          //                       ? Colors.transparent
          //                       : Color(0x0A000000), // สีของเงา
          //                   offset: Offset(3, 6), // ตำแหน่งเงาแนวนอนและแนวตั้ง
          //                   blurRadius: 18, // รัศมีของเงา
          //                   spreadRadius: 0, // รัศมีการกระจายของเงา
          //                 ),
          //               ],
          //               border: read != 'read'
          //                   ? Border(
          //                       bottom: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.1),
          //                         width: 1,
          //                       ),
          //                       top: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.1),
          //                         width: 0,
          //                       ),
          //                       left: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.1),
          //                         width: 0,
          //                       ),
          //                       right: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.1),
          //                         width: 0,
          //                       ),
          //                     )
          //                   : Border(
          //                       bottom: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.05),
          //                         width: 1,
          //                       ),
          //                       top: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.05),
          //                         width: 1,
          //                       ),
          //                       left: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.05),
          //                         width: 1,
          //                       ),
          //                       right: BorderSide(
          //                         color: Color(0xFF792AFF).withOpacity(0.05),
          //                         width: 1,
          //                       ),
          //                     )),
          //           child: Column(
          //             children: [
          //               Padding(
          //                 padding: EdgeInsets.all(16.0),
          //                 child: Column(
          //                   crossAxisAlignment: CrossAxisAlignment.start,
          //                   children: [
          //                     Row(
          //                       mainAxisAlignment:
          //                           MainAxisAlignment.spaceBetween,
          //                       children: [
          //                         Text(
          //                           '$typeNoti',
          //                           textAlign: TextAlign.center,
          //                           style: TextStyle(
          //                             color:
          //                                 configTheme().colorScheme.onSecondary,
          //                             fontSize: 14,
          //                             fontFamily: TextStyleTheme
          //                                 .text_Regular.fontFamily,
          //                             fontWeight: FontWeight.w600,
          //                           ),
          //                         ),
          //                         read != 'read'
          //                             ? Container(
          //                                 width: 6.0,
          //                                 height: 6.0,
          //                                 decoration: BoxDecoration(
          //                                   shape: BoxShape.circle,
          //                                   color: Color(
          //                                       0xFFFF9300), // เปลี่ยนสีตามที่ต้องการ
          //                                 ),
          //                               )
          //                             : Container()
          //                       ],
          //                     ),
          //                     Row(
          //                       children: [
          //                         Text(
          //                           '$title',
          //                           textAlign: TextAlign.center,
          //                           style: TextStyle(
          //                             color: Color(0xFF1A1818),
          //                             fontSize: 14.sp,
          //                             fontFamily: TextStyleTheme
          //                                 .text_Regular.fontFamily,
          //                             fontWeight: FontWeight.w700,
          //                           ),
          //                         ),
          //                       ],
          //                     ),
          //                     SizedBox(
          //                       width: Get.width,
          //                       child: Row(
          //                         crossAxisAlignment: CrossAxisAlignment.start,
          //                         // ให้แสดงข้อความของแต่ละแถวที่จุดเริ่มต้น (start)
          //                         children: [
          //                           Expanded(
          //                             child: Text(
          //                               '$detail',
          //                               textAlign: TextAlign.start,
          //                               maxLines: 2,
          //                               // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
          //                               overflow: TextOverflow.ellipsis,
          //                               // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
          //                               style: TextStyle(
          //                                 color: Color(0xFF1A1818),
          //                                 fontSize: 14,
          //                                 fontFamily: TextStyleTheme
          //                                     .text_Regular.fontFamily,
          //                                 fontWeight: FontWeight.w400,
          //                               ),
          //                             ),
          //                           ),
          //                         ],
          //                       ),
          //                     ),
          //                     Row(
          //                       children: [
          //                         Text(
          //                           '$day',
          //                           textAlign: TextAlign.center,
          //                           style: TextStyle(
          //                             color: Color(0xFF1A1818).withOpacity(0.5),
          //                             fontSize: 12,
          //                             fontFamily: TextStyleTheme
          //                                 .text_Regular.fontFamily,
          //                             fontWeight: FontWeight.w400,
          //                           ),
          //                         ),
          //                       ],
          //                     ),
          //                     SizedBox(
          //                       height: 8,
          //                     ),
          //                     Container(
          //                       width: 89.w,
          //                       height: 32.h,
          //                       decoration: BoxDecoration(
          //                         color: Color(0xFF792AFF),
          //                         borderRadius: BorderRadius.circular(8),
          //                         border: Border(
          //                           left: BorderSide(color: Color(0xFF792AFF)),
          //                           top: BorderSide(color: Color(0xFF792AFF)),
          //                           right: BorderSide(color: Color(0xFF792AFF)),
          //                           bottom: BorderSide(
          //                               width: 1.50.w,
          //                               color: Color(0xFF792AFF)),
          //                         ),
          //                         boxShadow: [
          //                           BoxShadow(
          //                             color: Color(0x72792AFF),
          //                             blurRadius: 10,
          //                             offset: Offset(2, 4),
          //                             spreadRadius: 0,
          //                           )
          //                         ],
          //                       ),
          //                       child: Stack(
          //                         children: [
          //                           Container(
          //                             alignment: Alignment.topRight,
          //                             margin:
          //                                 EdgeInsets.only(top: 6.h, right: 6.w),
          //                             child: Opacity(
          //                               opacity: 0,
          //                               child: Container(
          //                                 width: 6,
          //                                 height: 6,
          //                                 decoration: ShapeDecoration(
          //                                   color: Color(0xFFFF9300),
          //                                   shape: OvalBorder(),
          //                                 ),
          //                               ),
          //                             ),
          //                           ),
          //                           InkWell(
          //                             onTap: () {
          //                               setState(() {
          //                                 tricker_count = 0;
          //                               });
          //                             },
          //                             child: Container(
          //                               alignment: Alignment.center,
          //                               child: Text(
          //                                 'ร่วมทายรางวัล',
          //                                 textAlign: TextAlign.center,
          //                                 style: TextStyle(
          //                                   color: Colors.white,
          //                                   fontSize: 12,
          //                                   fontFamily: TextStyleTheme
          //                                       .text_Regular.fontFamily,
          //                                   fontWeight: FontWeight.w600,
          //                                 ),
          //                               ),
          //                             ),
          //                           ),
          //                         ],
          //                       ),
          //                     ),
          //                   ],
          //                 ),
          //               ),
          //             ],
          //           ),
          //         ),
          //       ),
          //       Padding(
          //         padding: const EdgeInsets.only(top: 134.0, left: 190),
          //         child: SizedBox(
          //             height: 70.h,
          //             width: 127.w,
          //             child: Image.asset('assets/notify/Vector2.jpg')),
          //       ),
          //     ],
          //   ),
          // ),
        ],
      ); // เรียกใช้ Inkwell1 ถ้า read เป็น 1
    } else if (typeNotification == 'notify' || typeNotification == 'news') {
      return InkWell(
        onTap: () async {
          await function();

          Get.to(() => pages);
        },
        child: Padding(
          padding: EdgeInsets.only(top: 16),
          child: Container(
            width: 327.w,
            height: 150.h,
            decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Color(0xFFFFFFFF).withOpacity(0.9),
                  Color(0xFFFFFFFF)
                ]),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: read != 'read'
                        ? Colors.transparent
                        : Color(0x0A000000), // สีของเงา
                    offset: Offset(3, 6), // ตำแหน่งเงาแนวนอนและแนวตั้ง
                    blurRadius: 18, // รัศมีของเงา
                    spreadRadius: 0, // รัศมีการกระจายของเงา
                  ),
                ],
                border: read != 'read'
                    ? Border(
                        bottom: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.1),
                          width: 1,
                        ),
                        top: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.1),
                          width: 0,
                        ),
                        left: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.1),
                          width: 0,
                        ),
                        right: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.1),
                          width: 0,
                        ),
                      )
                    : Border(
                        bottom: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.05),
                          width: 1,
                        ),
                        top: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.05),
                          width: 1,
                        ),
                        left: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.05),
                          width: 1,
                        ),
                        right: BorderSide(
                          color: Color(0xFF792AFF).withOpacity(0.05),
                          width: 1,
                        ),
                      )),
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Container(
                width: Get.width,
                height: Get.height,
                child: Row(
                  children: [
                    Container(
                      width: 187.w,
                      height: 120.h,
                      child: Column(
                        children: [
                          Text(
                            '$title',
                            textAlign: TextAlign.start,
                            maxLines: 2, // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontFamily:
                                  TextStyleTheme.text_Regular.fontFamily,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          read != 'read'
                              ? Container(
                                  width: 6.0,
                                  height: 6.0,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: configTheme()
                                        .focusColor, // เปลี่ยนสีตามที่ต้องการ
                                  ),
                                )
                              : Container(),
                          SizedBox(
                            width: Get.width,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              // ให้แสดงข้อความของแต่ละแถวที่จุดเริ่มต้น (start)
                              children: [
                                Expanded(
                                  child: Text(
                                    '$detail',
                                    textAlign: TextAlign.start,
                                    maxLines: 2,
                                    // กำหนดให้แสดงได้สูงสุด 2 บรรทัด
                                    overflow: TextOverflow.ellipsis,
                                    // ให้ข้อความสุดท้ายของแถวที่สองแสดงเป็นจุดไข่ปลา
                                    style: TextStyle(
                                      color: Color(0xFF1A1818),
                                      fontSize: 14,
                                      fontFamily: TextStyleTheme
                                          .text_Regular.fontFamily,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '$day',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Color(0xFF1A1818).withOpacity(0.5),
                                  fontSize: 12,
                                  fontFamily:
                                      TextStyleTheme.text_Regular.fontFamily,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      child: Image.asset('assets/notify/Rectangle 2227.jpg'),
                      width: 120.w,
                      height: 120.h,
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ); // เรียกใช้ Inkwell1 ถ้า read เป็น 1
    } else {
      return SizedBox(); // หรือ Widget อื่น ๆ ที่เหมาะสมกับสถานการณ์
    }
  }

  Widget _buildServicePage(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.height,
      child: Padding(
        padding: EdgeInsets.only(top: 100.h, left: 20, right: 20),
        child: GetBuilder<NotificationController>(builder: (notiCtl) {
          // ตรวจสอบว่ามีรายการใน notificationList ที่มี typeNotification เป็น '1' หรือไม่
          bool hasTypeNotification1 =
              notiCtl.notificationList!.any((notification) =>
                  notification.typeNotification == "" ||
                  notification.typeNotification == "-" ||
                  notification.typeNotification == null ||
                  notification.typeNotification == "Nextpayaut" ||

                  ///ค่างวด
                  notification.typeNotification == "claimrewar" ||

                  ///MR
                  notification.typeNotification == "requestIns" ||

                  //TODO RPLC, RAFCO
                  notification.typeNotification == "localNoti" ||
                  notification.typeNotification == "evaluation" ||
                  notification.typeNotification == "bill");

          print('hasTypeNotification1 service: $hasTypeNotification1');

          // ถ้าไม่มีรายการที่มี typeNotification เป็น '1' แสดง _buildNotiEmptyPage(context)
          if (!hasTypeNotification1) {
            return _buildNotiEmptyPage(context);
          }

          // มีรายการที่มี typeNotification เป็น '1' ให้แสดงรายการปกติ
          return ListView.builder(
            itemCount: notiCtl.notificationList!.length,
            // itemCount: notiCtl.notificationList!.length,
            scrollDirection: Axis.vertical,
            itemBuilder: (BuildContext context, index) {
              var type_noti_name = notiCtl.sortTypeNotification(
                  notiCtl.notificationList![index].typeNotification);
              var ctt_code = '';

              if (notiCtl.notificationList![index].typeNotification == "" ||
                  notiCtl.notificationList![index].typeNotification == "-" ||
                  notiCtl.notificationList![index].typeNotification == null ||
                  notiCtl.notificationList![index].typeNotification ==
                      "Nextpayaut" ||
                  notiCtl.notificationList![index].typeNotification ==
                      "claimrewar" ||
                  notiCtl.notificationList![index].typeNotification ==
                      "requestIns" ||
                  //TODO RPLC, RAFCO
                  notiCtl.notificationList![index].typeNotification ==
                      "localNoti" ||
                  notiCtl.notificationList![index].typeNotification ==
                      "evaluation" ||
                  notiCtl.notificationList![index].typeNotification == "bill") {
                // print(notiCtl.notificationList![0].toJson());
                // print(type_noti_name);
                if (notiCtl.notificationList![index].typeNotification ==
                    "Nextpayaut") {
                  print("Nextpayaut");
                  ctt_code = notiCtl.extractContractNumber(
                      notiCtl.notificationList![index].detail);
                }
                return _alertBox(
                    context,
                    notiCtl.notificationList![index].title,
                    type_noti_name,
                    notiCtl.notificationList![index].detail,
                    notiCtl.notificationList![index].createTime,
                    notiCtl.notificationList![index].ctt_code,
                    index,
                    detail_type_service(
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].title,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].createTime.toString(),
                      ctt_code ?? "",
                      notiCtl.notificationList![index].noti_time,
                      index,
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].statusnotification,
                      notiCtl.notificationList![index].typeNotification,
                      type_noti_name,
                    ),
                    notiCtl.notificationList![index].statusnotification,
                    notiCtl.notificationList![index].typeNotification, () {
                  notiCtl.updateReaded(
                      index, notiCtl.notificationList![index].ctt_code, 'read');
                });
              } else {
                return Container();
              }
            },
          );
        }),
      ),
    );
  }

  Widget _buildPointsPage(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.height,
      child: Padding(
        padding: EdgeInsets.only(top: 100.h, left: 20, right: 20),
        child: GetBuilder<NotificationController>(builder: (notiCtl) {
          // ตรวจสอบว่ามีรายการใน notificationList ที่มี typeNotification เป็น '1' หรือไม่
          bool hasTypeNotification1 = notiCtl.notificationList!.any(
              (notification) =>
                  notification.typeNotification == 'claimlike_' ||
                  notification.typeNotification == 'claimlike' ||
                  notification.typeNotification == "recommend_" ||
                  notification.typeNotification == "recommend_mr");

          // ถ้าไม่มีรายการที่มี typeNotification เป็น '1' แสดง _buildNotiEmptyPage(context)
          if (!hasTypeNotification1) {
            return _buildNotiEmptyPage(context);
          }

          // มีรายการที่มี typeNotification เป็น '1' ให้แสดงรายการปกติ
          return ListView.builder(
            itemCount: notiCtl.notificationList!.length,
            scrollDirection: Axis.vertical,
            itemBuilder: (BuildContext context, int index) {
              var type_noti_name = notiCtl.sortTypeNotification(
                  notiCtl.notificationList![index].typeNotification);

              if (notiCtl.notificationList![index].typeNotification ==
                      'claimlike_' ||
                  notiCtl
                          .notificationList![index].typeNotification ==
                      'claimlike' ||
                  notiCtl.notificationList![index].typeNotification ==
                      "recommend_" ||
                  notiCtl.notificationList![index].typeNotification ==
                      "recommend_mr") {
                return _alertBox(
                    context,
                    notiCtl.notificationList![index].title,
                    type_noti_name,
                    notiCtl.notificationList![index].detail,
                    notiCtl.notificationList![index].createTime,
                    notiCtl.notificationList![index].ctt_code,
                    index,
                    detail_type_point(
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].title,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].createTime,
                      notiCtl.notificationList![index].ctt_code,
                      index,
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].statusnotification,
                      type_noti_name,
                    ),
                    notiCtl.notificationList![index].statusnotification,
                    notiCtl.notificationList![index].typeNotification, () {
                  notiCtl.updateReaded(
                      index, notiCtl.notificationList![index].ctt_code, 'read');
                });
              } else {
                // ถ้าไม่ใช่ typeNotification เป็น '1' ให้ส่งคืน Container() หรือวิดเจ็ตที่ต้องการสร้างเพื่อให้ไม่มีการแสดงผล
                return Container();
              }
            },
          );
        }),
      ),
    );
  }

  Widget _buildNewsPage(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.height,
      child: Padding(
        padding: EdgeInsets.only(top: 100.h, left: 20, right: 20),
        child: GetBuilder<NotificationController>(builder: (notiCtl) {
          // ตรวจสอบว่ามีรายการใน notificationList ที่มี typeNotification เป็น '1' หรือไม่
          bool hasTypeNotification1 = notiCtl.notificationList!.any(
              (notification) =>
                  notification.typeNotification == 'notify' ||
                  notification.typeNotification == 'news');

          // ถ้าไม่มีรายการที่มี typeNotification เป็น '1' แสดง _buildNotiEmptyPage(context)
          if (!hasTypeNotification1) {
            return _buildNotiEmptyPage(context);
          }

          // มีรายการที่มี typeNotification เป็น '1' ให้แสดงรายการปกติ
          return ListView.builder(
            itemCount: notiCtl.notificationList!.length,
            scrollDirection: Axis.vertical,
            itemBuilder: (BuildContext context, int index) {
              var type_noti_name = notiCtl.sortTypeNotification(
                  notiCtl.notificationList![index].typeNotification);

              if (notiCtl.notificationList![index].typeNotification ==
                      'notify' ||
                  notiCtl.notificationList![index].typeNotification == 'news') {
                return _alertBox(
                    context,
                    notiCtl.notificationList![index].title,
                    type_noti_name,
                    notiCtl.notificationList![index].detail,
                    notiCtl.notificationList![index].createTime,
                    notiCtl.notificationList![index].ctt_code,
                    index,
                    detail_type_news(
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].title,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].detail,
                      notiCtl.notificationList![index].createTime,
                      notiCtl.notificationList![index].ctt_code,
                      index,
                      notiCtl.notificationList![index].idNotification,
                      notiCtl.notificationList![index].statusnotification,
                      type_noti_name,
                    ),
                    notiCtl.notificationList![index].statusnotification,
                    notiCtl.notificationList![index].typeNotification, () {
                  notiCtl.updateReaded(
                      index, notiCtl.notificationList![index].ctt_code, 'read');
                });
              } else {
                return Container();
              }
            },
          );
        }),
      ),
    );
  }

  Widget DeleteReaded(BuildContext context, function) {
    return InkWell(
      onTap: () {
        if (Get.find<HomeController>().isGuest!.value) {
          showDialog(
              context: context,
              useSafeArea: true,
              builder: (_) => const RegisterPage());
        } else {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            builder: (BuildContext context) {
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft:
                            Radius.circular(12), // กำหนดมุมด้านบนซ้ายเป็นวงกลม
                        topRight:
                            Radius.circular(12), // กำหนดมุมด้านบนขวาเป็นวงกลม
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: AnimatedContainer(
                        duration: Duration(seconds: 2),
                        width: MediaQuery.of(context).size.width,
                        height: 273.h,
                        child: Center(
                          child: Column(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                child: SvgPicture.string(
                                    '<svg width="44" height="5" viewBox="0 0 44 5" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="44" height="5" rx="2.5" fill="#1A1818" fill-opacity="0.2"/></svg>'),
                              ),
                              SizedBox(
                                height: 16.h,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Text(
                                            notificationDelete.tr,
                                            textAlign: TextAlign.start,
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 16.sp,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w700,
                                            ),
                                          ),
                                          Text(
                                            notificationDeleteMSG.tr,
                                            textAlign: TextAlign.start,
                                            style: TextStyle(
                                              color: Colors.black
                                                  .withOpacity(0.75),
                                              fontSize: 14.sp,
                                              fontFamily: TextStyleTheme
                                                  .text_Regular.fontFamily,
                                              fontWeight: FontWeight.w400,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        height: 39.h,
                                        width: 73.w,
                                        child: Image.asset(
                                            'assets/notify/Vector.jpg'),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                              SizedBox(height: 20),
                              InkWell(
                                onTap: () {
                                  setState(() {
                                    function();
                                  });
                                  Navigator.of(context).pop();
                                  isDeleted != isDeleted;
                                },
                                child: Container(
                                  width: 327.w,
                                  height: 52.h,
                                  decoration: BoxDecoration(
                                    color: appConfigService
                                                .countryConfigCollection
                                                .toString() ==
                                            'aam'
                                        ? Color(0xFF792AFF)
                                        : appConfigService
                                                    .countryConfigCollection
                                                    .toString() ==
                                                'rplc'
                                            ? Color(0xFF6A7165)
                                            : Color(0xFFEA1B23),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Center(
                                    child: Text(
                                      notificationDeleteRead.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xFFFFFFFF),
                                        fontSize: 14.sp,
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 10),
                              InkWell(
                                onTap: () {
                                  Navigator.of(context).pop();
                                },
                                child: Container(
                                  width: 327.w,
                                  height: 52.h,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Center(
                                    child: Text(
                                      notificationDeleteCancel.tr,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: const Color(0xFF1A1818),
                                        fontSize: 14.sp,
                                        fontFamily: TextStyleTheme
                                            .text_Regular.fontFamily,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }
      },
      child: Container(
        width: 24,
        height: 24,
        child: SvgPicture.string(AppSvgImage.delete_noti),
      ),
    );
  }
}
