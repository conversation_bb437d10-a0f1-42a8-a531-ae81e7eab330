class ResponseContracDigi {
  ResponseContracDigi(
      {this.guarantee_id,
        this.current_contract,
        this.money_approve,
        this.debt_loan,
        this.remain_loan
      });

  String? guarantee_id;
  int? current_contract;
  int? money_approve;
  int? debt_loan;
  int? remain_loan;

  factory ResponseContracDigi.fromJson(json) => ResponseContracDigi(
      guarantee_id: json["guarantee_id"] ?? "",
      current_contract: json["current_contract"] ?? 0,
      money_approve: json["money_approve"] ?? 0,
      debt_loan: json["debt_loan"] ?? 0,
      remain_loan: json["remain_loan"] ?? 0
  );

  Map<String, dynamic> toJson() => {
    "guarantee_id": guarantee_id,
    "current_contract": current_contract,
    "money_approve": money_approve,
    "debt_loan": debt_loan,
    "remain_loan": remain_loan
  };
}
