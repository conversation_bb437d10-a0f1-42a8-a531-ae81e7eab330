class Subscription {
  Subscription({
    this.phoneNumber,
    this.firstName,
    this.lastName,
    this.idCard,
    this.address,
    this.subDistrict,
    this.district,
    this.province,
    this.postalCode,
    this.branch,
    this.birthday,
    this.referrerCode,
  });

  String? phoneNumber;
  String? firstName;
  String? lastName;
  String? idCard;
  String? address;
  String? subDistrict;
  String? district;
  String? province;
  String? postalCode;
  String? branch;
  String? birthday;
  String? referrerCode;

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      phoneNumber: json['phoneNumber'],
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      idCard: json['idCard'] ?? '',
      address: json['address'] ?? '',
      subDistrict: json['subDistrict'] ?? '',
      district: json['district'] ?? '',
      province: json['province'] ?? '',
      postalCode: json['postalCode'] ?? '',
      branch: json['branch'] ?? '',
      birthday: json['birthday'] ?? '',
      referrerCode: json['referrerCode'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'firstName': firstName,
      'lastName': lastName,
      'idCard': idCard,
      'address': address,
      'subDistrict': subDistrict,
      'district': district,
      'province': province,
      'postalCode': postalCode,
      'branch': branch,
      'birthday': birthday,
      'referrerCode': referrerCode,
    };
  }
}
