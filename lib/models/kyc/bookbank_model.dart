import 'package:get/get.dart';

class BookbankModel {
  final String? bookbank_status;
  final String? bookbank_reviewAns;
  final String? bookbank_custName;
  final String? bookbank_name;
  final String? bookbank_number;
  final String? bookbank_branch;
  final String? bookbank_image;
  final String? bookbank_note;
  final bool? bookbank_showList;

  BookbankModel({
    this.bookbank_status,
    this.bookbank_reviewAns,
    this.bookbank_custName,
    this.bookbank_name,
    this.bookbank_number,
    this.bookbank_branch,
    this.bookbank_image,
    this.bookbank_note,
    this.bookbank_showList,
  });

  factory BookbankModel.fromJson(Map<String, dynamic> json) {
    return BookbankModel(
      bookbank_status: json['bookbank_status'] ?? "",
      bookbank_reviewAns: json['bookbank_reviewAns'] ?? "",
      bookbank_custName: json['bookbank_custName'] ?? "",
      bookbank_name: json['bookbank_name'] ?? "",
      bookbank_number: json['bookbank_number'] ?? "",
      bookbank_branch: json['bookbank_branch'] ?? "",
      bookbank_image: json['bookbank_image'] ?? "",
      bookbank_note: json['bookbank_note'] ?? "",
      bookbank_showList: json['bookbank_showList'] ?? false.obs,
    );
  }
  // factory BookbankModel.fromJson(Map<String, dynamic> json) {
  //   return BookbankModel(
  //     bookbank_status: json['bookbank_status'] as String?,
  //     bookbank_reviewAns: json['bookbank_reviewAns'] as String?,
  //     bookbank_custName: json['bookbank_custName'] as String?,
  //     bookbank_name: json['bookbank_name'] as String?,
  //     bookbank_number: json['bookbank_number'] as String?,
  //     bookbank_branch: json['bookbank_branch'] as String?,
  //     bookbank_image: json['bookbank_image'] as String?,
  //     bookbank_note: json['bookbank_note'] as String?,
  //     bookbank_showList: json['bookbank_showList'] as bool?,
  //   );
  // }

  Map<String, dynamic> toJson() {
    return {
      'bookbank_status': bookbank_status,
      'bookbank_reviewAns': bookbank_reviewAns,
      'bookbank_custName': bookbank_custName,
      'bookbank_name': bookbank_name,
      'bookbank_number': bookbank_number,
      'bookbank_branch': bookbank_branch,
      'bookbank_image': bookbank_image,
      'bookbank_note': bookbank_note,
      'bookbank_showList': bookbank_showList,
    };
  }
}
