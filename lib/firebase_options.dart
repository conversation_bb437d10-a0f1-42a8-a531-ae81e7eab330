import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_config.dart';
import 'controller/AppConfigService.dart';

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform  {
    print('init firebase');
    // ลงทะเบียน AppConfigService ก่อนการใช้งาน
    final appConfigService = Get.find<AppConfigService>();
    final environment = appConfigService.environment;
    if (kIsWeb) {
      if (environment == Environment.aam_dev || environment == Environment.aam_prod) {
        return web_aam_dev;
      } else if (environment == Environment.rplc_dev || environment == Environment.rplc_prod) {
        return web_rplc_dev;
      } else if(environment == Environment.rafco_dev || environment == Environment.rafco_prod){
        return web_rafco_dev;
      }else{
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for web - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      }
      // throw UnsupportedError(
      //   'DefaultFirebaseOptions have not been configured for web - '
      //   'you can reconfigure this by running the FlutterFire CLI again.',
      // );
    }

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        if (environment == Environment.aam_dev) {
          return android_aam_dev;
        } else if (environment == Environment.rplc_dev) {
          return android_rplc_dev;
        } else if(environment == Environment.rafco_dev){
          return android_rafco_dev;
        }else if(environment == Environment.aam_prod){
          return android_aam_prod;
        }else if(environment == Environment.rplc_prod){
          return android_rplc_prod;
        }else if(environment == Environment.rafco_prod){
          return android_rafco_prod;
        } else {
          throw UnsupportedError(
            'DefaultFirebaseOptions have not been configured for android - '
            'you can reconfigure this by running the FlutterFire CLI again.',
          );
        }
      case TargetPlatform.iOS:
        if (environment == Environment.aam_dev) {
          return ios_aam_dev;
        } else if (environment == Environment.rplc_dev) {
          return ios_rplc_dev;
        } else if(environment == Environment.rafco_dev){
          return ios_rafco_dev;
        }else if(environment == Environment.aam_prod){
          return ios_aam_prod;
        }else if(environment == Environment.rplc_prod){
          return ios_rplc_prod;
        }else if(environment == Environment.rafco_prod){
          return ios_rafco_prod;
        } else {
          throw UnsupportedError(
            'DefaultFirebaseOptions have not been configured for iOS - '
            'you can reconfigure this by running the FlutterFire CLI again.',
          );
        }
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android_aam_dev = FirebaseOptions(
      apiKey: 'AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM',
      appId: '1:465736603910:android:179cb3abaa32db385e6687',
      messagingSenderId: '465736603910',
      projectId: 'mappaam-44857',
      databaseURL: 'https://mappaam-44857.firebaseio.com',
      storageBucket: 'mappaam-44857.appspot.com');

  static const FirebaseOptions android_aam_prod = FirebaseOptions(
      apiKey: 'AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM',
      appId: '1:465736603910:android:179cb3abaa32db385e6687',
      messagingSenderId: '465736603910',
      projectId: 'mappaam-44857',
      databaseURL: 'https://mappaam-44857.firebaseio.com',
      storageBucket: 'mappaam-44857.appspot.com');

  static const FirebaseOptions ios_aam_dev = FirebaseOptions(
      apiKey: 'AIzaSyAHKoiuG-Fy0vg3JdCc2a5Xf3GnNKNanKk',
      appId: '1:465736603910:ios:99abe226febeedae5e6687',
      messagingSenderId: '465736603910',
      projectId: 'mappaam-44857',
      databaseURL: 'https://mappaam-44857.firebaseio.com',
      storageBucket: 'mappaam-44857.appspot.com',
      iosClientId:
          '465736603910-ejooup85c5hk5gpkpkb538ju9fndqvis.apps.googleusercontent.com',
      iosBundleId: 'com.aamfinancegroup.aambetaapp');

  static const FirebaseOptions ios_aam_prod = FirebaseOptions(
      apiKey: 'AIzaSyAHKoiuG-Fy0vg3JdCc2a5Xf3GnNKNanKk',
      appId: '1:465736603910:ios:0f497809a1989d155e6687',
      messagingSenderId: '465736603910',
      projectId: 'mappaam-44857',
      databaseURL: 'https://mappaam-44857.firebaseio.com',
      storageBucket: 'mappaam-44857.appspot.com',
      iosClientId:
          '465736603910-ejooup85c5hk5gpkpkb538ju9fndqvis.apps.googleusercontent.com',
      iosBundleId: 'com.aamfinancegroup.aamapp');

  static const FirebaseOptions web_aam_dev = FirebaseOptions(
      apiKey: 'AIzaSyA1hUQBhKTzqjTgbWwYcSvcPKMXY-PaeQ0',
      appId: '1:465736603910:web:534edc519de3f0045e6687',
      messagingSenderId: '465736603910',
      projectId: 'mappaam-44857',
      authDomain: 'mappaam-44857.firebaseapp.com',
      databaseURL: 'https://mappaam-44857.firebaseio.com',
      storageBucket: 'mappaam-44857.appspot.com',
      measurementId: 'G-DC8QCNHN6G');

  static const FirebaseOptions android_rplc_dev = FirebaseOptions(
    apiKey: 'AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w',
    appId: '1:153559207355:android:5da8300ccd868a3188489e',
    messagingSenderId: '153559207355',
    projectId: 'mapprplc',
    databaseURL: 'https://mapprplc.firebaseio.com',
    storageBucket: 'mapprplc.appspot.com'
  );

  static const FirebaseOptions android_rplc_prod = FirebaseOptions(
      apiKey: 'AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w',
      appId: '1:153559207355:android:5da8300ccd868a3188489e',
      messagingSenderId: '153559207355',
      projectId: 'mapprplc',
      databaseURL: 'https://mapprplc.firebaseio.com',
      storageBucket: 'mapprplc.appspot.com'
  );

  static const FirebaseOptions ios_rplc_dev = FirebaseOptions(
    apiKey: 'AIzaSyBAOVpZSRcvnt-RFa5PxrGaZKEDJa5jF9M',
    appId: '1:153559207355:ios:a77d7a4ba436cce788489e',
    messagingSenderId: '153559207355',
    projectId: 'mapprplc',
    databaseURL: 'https://mapprplc.firebaseio.com',
    storageBucket: 'mapprplc.appspot.com',
    iosClientId: '153559207355-2489jv9e5sj9f30658m8tvoas25noenk.apps.googleusercontent.com',
    iosBundleId: 'com.ruampattanaleasing.rplcapp'
  );

  static const FirebaseOptions ios_rplc_prod = FirebaseOptions(
      apiKey: 'AIzaSyBAOVpZSRcvnt-RFa5PxrGaZKEDJa5jF9M',
      appId: '1:153559207355:ios:a77d7a4ba436cce788489e',
      messagingSenderId: '153559207355',
      projectId: 'mapprplc',
      databaseURL: 'https://mapprplc.firebaseio.com',
      storageBucket: 'mapprplc.appspot.com',
      iosClientId: '153559207355-2489jv9e5sj9f30658m8tvoas25noenk.apps.googleusercontent.com',
      iosBundleId: 'com.ruampattanaleasing.rplcapp'
  );

  static const FirebaseOptions web_rplc_dev = FirebaseOptions(
      apiKey: "AIzaSyDXyrhQ4mVBbEYST6Ykk7M4FE6yqCKyw9w",
      authDomain: "mapprplc.firebaseapp.com",
      databaseURL: "https://mapprplc.firebaseio.com",
      projectId: "mapprplc",
      storageBucket: "mapprplc.appspot.com",
      messagingSenderId: "153559207355",
      appId: "1:153559207355:web:5ebe1c11ea10e32488489e",
      measurementId: "G-7JEJHKHKMT"
  );

  static const FirebaseOptions android_rafco_dev = FirebaseOptions(
    apiKey: 'AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA',
    appId: '1:825860150327:android:10ca20637dbd2a19909bb2',
    messagingSenderId: '825860150327',
    projectId: 'mapprafco',
    databaseURL: 'https://mapprafco.firebaseio.com',
    storageBucket: 'mapprafco.appspot.com'
  );

  static const FirebaseOptions android_rafco_prod = FirebaseOptions(
    apiKey: 'AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA',
    appId: '1:825860150327:android:10ca20637dbd2a19909bb2',
    messagingSenderId: '825860150327',
    projectId: 'mapprafco',
    databaseURL: 'https://mapprafco.firebaseio.com',
    storageBucket: 'mapprafco.appspot.com'
  );

  static const FirebaseOptions ios_rafco_dev = FirebaseOptions(
    apiKey: 'AIzaSyBye9WzopkcD6cCJZFbZQuNo6gjuaeacvk',
    appId: '1:825860150327:ios:b388978e1a53d5a4909bb2',
    messagingSenderId: '825860150327',
    projectId: 'mapprafco',
    databaseURL: 'https://mapprafco.firebaseio.com',
    storageBucket: 'mapprafco.appspot.com',
    iosClientId: '825860150327-1ue6a3ioor65dd53b835u2bg5nvqakh8.apps.googleusercontent.com',
    iosBundleId: 'com.rptn.rafco'
  );

  static const FirebaseOptions ios_rafco_prod = FirebaseOptions(
    apiKey: 'AIzaSyBye9WzopkcD6cCJZFbZQuNo6gjuaeacvk',
    appId: '1:825860150327:ios:b388978e1a53d5a4909bb2',
    messagingSenderId: '825860150327',
    projectId: 'mapprafco',
    databaseURL: 'https://mapprafco.firebaseio.com',
    storageBucket: 'mapprafco.appspot.com',
    iosClientId: '825860150327-1ue6a3ioor65dd53b835u2bg5nvqakh8.apps.googleusercontent.com',
    iosBundleId: 'com.rptn.rafco'
  );

  static const FirebaseOptions web_rafco_dev = FirebaseOptions(
      apiKey: "AIzaSyBWJDkzZABSBxZpiUgP7e-tdOY57vWBEyM",
      authDomain: "mapprafco.firebaseapp.com",
      databaseURL: "https://mapprafco.firebaseio.com",
      projectId: "mapprafco",
      storageBucket: "mapprafco.appspot.com",
      messagingSenderId: "825860150327",
      appId: "1:825860150327:web:963d61da0405d04b909bb2",
      measurementId: "G-ZBBL3Q9NCR"
  );
}
