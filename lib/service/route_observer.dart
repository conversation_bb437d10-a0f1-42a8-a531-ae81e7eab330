import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../view/componance/widgets/common_widgets/custom_bottom_sheet.dart';
import '../view/screen/feedback/widgets/feedback_bottom_sheet.dart';
import 'firebase/analytics_service.dart';

class AppRouteObserver extends GetObserver {
  var routeCount = 0;
  GetStorage box = GetStorage();

  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    // เรียกเมื่อเปลี่ยนหน้าใหม่ (push)
    debugPrint('Pushed to: ${route.settings.name}');
    routeCount++;
    debugPrint('routeCount: $routeCount');

    //TODO save Firebase Analytics log here
    AnalyticsService.trackScreenUser(
        screen_name: route.settings.name.toString());

    //TODO Spectify option
    if (route.settings.name == '/ProfilePage') {
      print("## feedback");
      // Get.context! // ใช้ context จาก Get
      // showPopup();
      final isPopupShown = box.read("isSettingPopupShown") ?? false;
      final isFeedbackProfile = box.read("feedback_profile") ?? false;

      print(isFeedbackProfile);

      // if (isPopupShown && !isFeedbackProfile) {
      //   Future.delayed(const Duration(seconds: 1), () {
      //     Get.bottomSheet(
      //       const CustomBottomSheet(
      //           height: 650, child: FeedbackBottomSheetContent()),
      //       isScrollControlled: true,
      //     );
      //   });
      // }
    }
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    // เรียกเมื่อกลับหน้าเก่า (pop)
    debugPrint('Popped from: ${route.settings.name}');
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    // เรียกเมื่อเปลี่ยนหน้า (replace)
    debugPrint(
        'Replaced ${oldRoute?.settings.name} with ${newRoute?.settings.name}');
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);
    // เรียกเมื่อเส้นทางถูกลบ (remove)
    debugPrint('Removed: ${route.settings.name}');
  }
}
