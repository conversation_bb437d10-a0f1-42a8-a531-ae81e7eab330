import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class AppService {
  static getOS() {
    String os = Platform.operatingSystem;

    return os;
  }

  static hideKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  static String dateThaiDate(textName) {
    if (textName != null) {
      var arr;
      if (textName.contains("T")) {
        arr = textName.split("T");
      } else {
        arr = textName.split(" ");
      }

      var dateArr = arr[0].split('-');
      var months_th_mini = [
        "",
        "ม.ค.",
        "ก.พ.",
        "มี.ค.",
        "เม.ย.",
        "พ.ค.",
        "มิ.ย.",
        "ก.ค.",
        "ส.ค.",
        "ก.ย.",
        "ต.ค.",
        "พ.ย.",
        "ธ.ค.",
      ];
      var year = int.parse(dateArr[0]) + 543;
      var month = months_th_mini[int.parse(dateArr[1])];
      var day = int.parse(dateArr[2]);
      return '$day $month $year';
    } else {
      return '';
    }
  }

  static String dateLocalized(DateTime date, String locale) {
    var monthNames = {
      'th': [
        "",
        "ม.ค.",
        "ก.พ.",
        "มี.ค.",
        "เม.ย.",
        "พ.ค.",
        "มิ.ย.",
        "ก.ค.",
        "ส.ค.",
        "ก.ย.",
        "ต.ค.",
        "พ.ย.",
        "ธ.ค."
      ],
      'lo': [
        "",
        "ມ.ກ.",
        "ກ.ພ.",
        "ມີ.ນ.",
        "ເມ.ສ.",
        "ພ.ພ.",
        "ມິ.ຖ.",
        "ກ.ຟ.",
        "ສິ.ຫ.",
        "ກ.ຍ.",
        "ຕ.ລ.",
        "ພ.ຈ.",
        "ທ.ວ."
      ],
      'km': [
        "",
        "ម.ក.",
        "ក.ស.",
        "ម.ន.",
        "ម.ស.",
        "ព.ស.",
        "ម.ម.",
        "ក.ដ.",
        "សី.ហ.",
        "ក.យ.",
        "ត.ឡ.",
        "ព.ឈ.",
        "ធ.វ."
      ],
      'en': [
        "",
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec"
      ],
    };

    int year = date.year;
    int month = date.month;
    int day = date.day;

    // สำหรับภาษาไทย ปีพ.ศ. ต้องบวก 543
    if (locale == 'th') {
      year += 543;
    }

    // ตรวจสอบ locale ว่ามีใน Map หรือไม่ ถ้าไม่ให้ใช้ภาษาอังกฤษเป็นค่าเริ่มต้น
    var monthsInLocale = monthNames[locale] ?? monthNames['en'];
    var monthName = monthsInLocale![month];

    return '$day $monthName $year';
  }

  static phoneToPhoneCode(phone) {
    return '+66' + phone.substring(1);
  }

  static dateEng2Tha(textName) async {
    if (textName != '0000-00-00') {
      List<String> monthList = [
        'มกราคม',
        'กุมภาพันธ์',
        'มีนาคม',
        'เมษายน',
        'พฤษภาคม',
        'มิถุนายน',
        'กรกฎาคม',
        'สิงหาคม',
        'กันยายน',
        'ตุลาคม',
        'พฤศจิกายน',
        'ธันวาคม',
      ];

      var dateArray = textName.split("-");

      int day = int.parse(dateArray[2]);
      int month = int.parse(dateArray[1]);
      int year = int.parse(dateArray[0]);

      return day.toString() +
          " " +
          monthList[month - 1] +
          " " +
          (year + 543).toString();
    } else {
      return '';
    }
  }

  static getImage(imageSource) async {
    final ImagePicker _picker = ImagePicker();

    var image = _picker.pickImage(source: imageSource);

    return image;
  }

  static formatCurrencyWithDecimal(String amount) {
    try {
      // print("amount!!!");
      // print(amount);
      if(amount.contains(',')){
        amount = amount.replaceAll(',', '');
      }
      final double parsedAmount = double.parse(amount);
      final NumberFormat decimalFormatter = NumberFormat.decimalPattern();
      decimalFormatter.minimumFractionDigits = 2; // อย่างน้อย 2 จุดทศนิยม
      decimalFormatter.maximumFractionDigits = 2; // มากสุด 2 จุดทศนิยม
      return decimalFormatter.format(parsedAmount);
    } catch (e) {
      return 'Invalid number'; // Handle conversion error
    }
  }

  static formatCurrencyWithoutDecimal(String amount) {
    try {
      // Remove commas if present
      if (amount.contains(',')) {
        amount = amount.replaceAll(',', '');
      }
      final double parsedAmount = double.parse(amount);
      final NumberFormat decimalFormatter = NumberFormat.decimalPattern();
      decimalFormatter.minimumFractionDigits = 0; // ไม่มีจุดทศนิยมขั้นต่ำ
      decimalFormatter.maximumFractionDigits = 0; // ไม่มีจุดทศนิยมมากที่สุด
      return decimalFormatter.format(parsedAmount);
    } catch (e) {
      return amount; // Handle conversion error
    }
  }

  static Future<String>formatDuration(Duration duration) async {
    // แปลงวินาทีเป็นนาที
    String minutes = duration.inMinutes.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นวินาที
    int secondsRemaining = duration.inSeconds % 60;
    String seconds = secondsRemaining.toString().padLeft(2, '0');

    // แปลงวินาทีที่เหลือเป็นเศษส่วนของวินาที
    int milliseconds = duration.inMilliseconds % 1000;
    String millisecondsString = milliseconds.toString().padLeft(2, '0');

    return '$minutes:$seconds:$millisecondsString';
  }

  static extractDateAndTime(String dateTimeString) {
    // แปลง dateTimeString เป็น DateTime
    DateTime dateTime = DateTime.parse(dateTimeString);

    // แยกวันที่
    String formattedDate =
        '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';

    // แยกเวลา
    String formattedTime =
        extractTime(dateTimeString); // เรียกใช้ฟังก์ชันจากคำตอบก่อนหน้า

    // รวมวันที่และเวลา
    String formattedDateTime = '$formattedDate, $formattedTime';
    return formattedDateTime;
  }

  static extractTime(String dateTimeString) {
    // แปลง dateTimeString เป็น DateTime
    DateTime dateTime = DateTime.parse(dateTimeString);

    // แยกชั่วโมง นาที และวินาที
    int hour = dateTime.hour;
    int minute = dateTime.minute;

    // ฟอร์แมตเวลาเป็น HH:mm
    String formattedTime =
        '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    return formattedTime;
  }
}
