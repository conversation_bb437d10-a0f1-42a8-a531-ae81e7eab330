import 'package:global_configuration/global_configuration.dart';

class Endpoints {
  Endpoints._();

  static String register = GlobalConfiguration().getValue('register');
  static String loginUsername = GlobalConfiguration().getValue('loginUsername');
  static String loginWithPhone =
      GlobalConfiguration().getValue('loginWithPhone');
  static String saveNotification =
      GlobalConfiguration().getValue('saveNotification');
  static String getNotification =
      GlobalConfiguration().getValue('getNotification');
  static String getNews = GlobalConfiguration().getValue('getNews');
  static String updateStatusNoti =
      GlobalConfiguration().getValue('updateStatusNoti');
  static String getDataCust = GlobalConfiguration().getValue('getDataCust');
  static String getContractDetail =
      GlobalConfiguration().getValue('getContractDetail');
  static String getContractList =
      GlobalConfiguration().getValue('getContractList');
  static String getMRData = GlobalConfiguration().getValue('getMRData');
  static String uploadS3_Center =
      GlobalConfiguration().getValue('uploadS3_Center');
  static String thai_id_card_ocrUrl =
      GlobalConfiguration().getValue('scanimage');
  static String getProvince = GlobalConfiguration().getValue('getProvince');
  static String getDistrict = GlobalConfiguration().getValue('getDistrict');
  static String getSubDistrict =
      GlobalConfiguration().getValue('getSubDistrict');
  static String getBalanceByUID =
      GlobalConfiguration().getValue('getBalanceByUID');
  static String getBalanceByPhoneNumber =
      GlobalConfiguration().getValue('getBalanceByPhoneNumber');
  static String checkAAMPAY = GlobalConfiguration().getValue('checkAAMPAY');
  static String getCreditByGuaranteeID =
      GlobalConfiguration().getValue('getCreditByGuaranteeID');
  static String getCreditByGuaranteeIDDigital =
      GlobalConfiguration().getValue('getCreditByGuaranteeIDDigital');
  static String qrCodePayUrl = GlobalConfiguration().getValue('qrCodePayUrl');
  static String getKycSumsub = GlobalConfiguration().getValue('getKycSumsub');
  static String getKycBookbank =
      GlobalConfiguration().getValue('getKycBookbank');
  static String getContractStatus =
      GlobalConfiguration().getValue('getContractStatus');
  static String getDataMember = GlobalConfiguration().getValue("getDataMember");
  static String requestLocal_OTP =
      GlobalConfiguration().getValue("requestLocalOTP_Mongo");
  static String sendOTPLaos = GlobalConfiguration().getValue("sendOTPLaos");
  static String sendOTPRafco = GlobalConfiguration().getValue("sendOTPRafco");
  static String sendNotiTelegramAI =
      GlobalConfiguration().getValue("sendNotiTelegramAI");
  static String lambdaLinkSendLineMappRAFCO =
      GlobalConfiguration().getValue("lambdaLinkSendLineMappRAFCO");
  static String requestOTPbot = GlobalConfiguration().getValue("requestOTPbot");
  static String verifyOTPCF = GlobalConfiguration().getValue("verifyOTPCF");
  static String verifyOTP_RPLC =
      GlobalConfiguration().getValue("verifyOTP_RPLC");
  static String getRPLCAddress =
      GlobalConfiguration().getValue("getRPLCAddress");
  // static String getRPLCAddress = GlobalConfiguration().getValue("https://agilesoftgroup.com/RPLCp3-UAT/dropdownLoanOnlineProvince");
  static String getRAFCOAddress = GlobalConfiguration().getValue("getRAFCOAddress");
  static String dropdownLoanOnlineProvince = GlobalConfiguration().getValue("dropdownLoanOnlineProvince");
  static String dropdownLoanOnlineDistrict = GlobalConfiguration().getValue("dropdownLoanOnlineDistrict");
  static String dropdownLoanOnlineSubDistrict = GlobalConfiguration().getValue("dropdownLoanOnlineSubDistrict");
  static String saveRequestLoan = GlobalConfiguration().getValue("saveRequestLoan");
  static String updateProfile = GlobalConfiguration().getValue("updateProfile");
  static String updateStatusNotification =
      GlobalConfiguration().getValue("updateStatusNoti");
  static String deleteStatusNotification =
      GlobalConfiguration().getValue("deleteStatusNoti");
  static String arcardPreview = GlobalConfiguration().getValue("arcardPreview");
  static String createAAMPAYContract =
      GlobalConfiguration().getValue("createAAMPAYContract");
  static String registerMR = GlobalConfiguration().getValue("registerMR");
  static String assumeBookBankData =
      GlobalConfiguration().getValue("assumeBookBankData");
  static String checkUpdateAddress =
      GlobalConfiguration().getValue("checkUpdateAddressDigital");
  static String getCustCode = GlobalConfiguration().getValue("getCustCode");
  static String updateAddressDigital =
      GlobalConfiguration().getValue("updateAddressDigital");
  static String getReferFriendData =
      GlobalConfiguration().getValue("getReferFriendData");
  static String getMRReferralHistory =
      GlobalConfiguration().getValue("getMRReferralHistory");
  static String referFriend = GlobalConfiguration().getValue("referFriend");
  static String updateAppAgreement = GlobalConfiguration().getValue("updateAppAgreement");
  static String updateAICPAgreement = GlobalConfiguration().getValue("updateAICPAgreement");
  static String updateAppPrivacyPolicy = GlobalConfiguration().getValue("updateAppPrivacyPolicy");
  static String getCountUnreadChat = GlobalConfiguration().getValue("getCountUnreadChat");
  static String delCountUnreadChat = GlobalConfiguration().getValue("delCountUnreadChat");
  // static String createReferralCode = GlobalConfiguration().getValue('https://api2.likepoint.io/member/get-ref-code/');
 // static String saveFeedback = GlobalConfiguration().getValue("saveFeedback");
  static String saveDeleteFeedback =
      GlobalConfiguration().getValue("saveDeleteFeedback");
  static String saveDisableAccount =
      GlobalConfiguration().getValue("saveDisableAccount");
  static String checkDeleteAccount =
      GlobalConfiguration().getValue("checkDeleteAccount");
  static String recoverAccount =
      GlobalConfiguration().getValue("recoverAccount");
  static String updateFCMToken = GlobalConfiguration().getValue("updateFCMToken");
  static String saveReferMRto4no = GlobalConfiguration().getValue("saveReferMRto4no");
  static String referFriendV_2 = GlobalConfiguration().getValue("referFriend_V2");
  static String searchHistoryMR = GlobalConfiguration().getValue("searchHistoryMR");
  static String searchHistoryReferal = GlobalConfiguration().getValue("searchHistoryReferal");
  static String getBranchData = GlobalConfiguration().getValue("getBranchData");
  static String searchBranchData = GlobalConfiguration().getValue("searchBranchData");
  static String getQRPayment = GlobalConfiguration().getValue("getQRPayment");
  static String uploadSlipBillPayment = GlobalConfiguration().getValue("uploadSlipBillPayment");
  //Social Login
  static String checkFaceBookUsers = GlobalConfiguration().getValue("checkFaceBookUsers");
  static String updateFaceBookUsers = GlobalConfiguration().getValue("updateFaceBookUsers");
  static String loginWithFacebook = GlobalConfiguration().getValue("loginWithFacebook");
  static String saveUserFeedbackReaction = GlobalConfiguration().getValue("saveFeedback");
  static String saveMultipleFeedbackReaction = GlobalConfiguration().getValue("saveFeedbackMultiType");
  //OCR
  static String ocrUrl = GlobalConfiguration().getValue("scanimage");
  // Loan status
  static String acceptRejectedLoanStatus = GlobalConfiguration().getValue("acceptRejectedLoanStatus");
  // get book bank
  static String getBookBankData = GlobalConfiguration().getValue("getBookBankData");
  static String updateDigitalAgreement = GlobalConfiguration().getValue("updateDigitalAgreement");
  static String updateBookbank = GlobalConfiguration().getValue("updateBookbank");
  // update referal code
  static String updateReferalCode = GlobalConfiguration().getValue("updateReferalCode");
  static String getReferralDownload = GlobalConfiguration().getValue("getReferralDownload");
  static String checkRefCode = GlobalConfiguration().getValue("checkRefCode");

}

