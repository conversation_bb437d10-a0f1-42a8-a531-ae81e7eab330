<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools" package="com.aamfinancegroup.aambeta">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_INTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.USE_FINGERPRINT"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<!--    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <!-- Samsung -->
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ"/>
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE"/>
    <!-- HTC -->
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS"/>
    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT"/>
    <!-- Sony -->
    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE"/>
    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE"/>
    <!-- Apex -->
    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT"/>
    <!-- Solid -->
    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE"/>
    <!-- Huawei -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" />


    <application
        android:label="@string/app_name"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:allowBackup="true"
        android:supportsRtl="false"
        tools:replace="android:supportsRtl"
        android:requestLegacyExternalStorage="true"
        >

        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>

<!--        <activity android:name="com.facebook.FacebookActivity"-->
<!--            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"-->
<!--            android:theme="@android:style/Theme.Translucent.NoTitleBar" />-->
        <!--        <activity android:name="com.facebook.FacebookActivity"-->
<!--            android:configChanges=-->
<!--                "keyboard|keyboardHidden|screenLayout|screenSize|orientation"-->
<!--            android:label="@string/app_name" />-->
<!--        <activity-->
<!--            android:name="com.facebook.CustomTabActivity"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.BROWSABLE" />-->
<!--                <data android:scheme="@string/fb_login_protocol_scheme" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

        <meta-data android:name="com.google.android.gms.version" android:value="@integer/google_play_services_version" />
        <meta-data android:name="io.flutter.embedding.android.SplashScreenDrawable" android:resource="@drawable/launch_background" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize"
            android:showWhenLocked="true"
            android:turnScreenOn="true"
            >
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme" />
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
<!--                <data android:scheme="@string/fb_login_protocol_scheme" />-->
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->

        <!--        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="de29c649ffcb3b2e4ee093db980f452e"/>-->

<!--        <activity-->
<!--            android:name="com.facebook.FacebookActivity"-->
<!--            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"-->
<!--            android:label="@string/app_name" />-->
<!--        <provider-->
<!--            android:name="com.facebook.FacebookContentProvider"-->
<!--            android:authorities="com.facebook.app.FacebookContentProvider[facebook_app_id]"-->
<!--            android:exported="false" />-->
<!--        <activity-->
<!--            android:name="com.facebook.CustomTabActivity"-->
<!--            android:exported="true">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.BROWSABLE" />-->
<!--                <data android:scheme="@string/fb_login_protocol_scheme" />-->
<!--                <action android:name="FLUTTER_NOTIFICATION_CLICK" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--            </intent-filter>-->
<!--        </activity>-->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="@string/google_maps_api_key"/>
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
    </queries>
<!--    <queries>-->
<!--        <intent>-->
<!--            <action android:name="android.intent.action.PROCESS_TEXT"/>-->
<!--            <data android:mimeType="text/plain"/>-->
<!--        </intent>-->
<!--        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />-->
<!--    </queries>-->
</manifest>
