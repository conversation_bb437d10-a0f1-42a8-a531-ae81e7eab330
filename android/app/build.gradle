plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '4'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '4.0.0'
}

//add new
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}
//

android {
    namespace "com.aamfinancegroup.aambeta"
    compileSdkVersion 35
    ndkVersion "25.1.8937393"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    aaptOptions {
        noCompress "tflite"
        noCompress "lite"
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.aamfinancegroup.aambeta"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            minifyEnabled false
            shrinkResources false
            signingConfig signingConfigs.release
        }
    }

    //   signingConfigs {
    //     debug {
    //          signingConfig signingConfigs.release
    //     }

    // }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }

    }
    flavorDimensions "env"

    productFlavors {
        aam_dev {
            dimension "env"
            resValue "string", "app_name", "AAM(BETA)"
            resValue "string", "keytool_sha1","C9:E6:E7:3D:4F:12:D2:7D:57:A9:A8:09:C2:21:75:92:AB:59:AD:95"
            resValue "string", "google_maps_api_key", "\"AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM\""
//            resValue "string", "facebook_app_id","1913575202110061"
//            resValue "string", "fb_login_protocol_scheme","fb1913575202110061"
            applicationId "com.aamfinancegroup.aambeta"
        }

        aam_prod {
            dimension "env"
            resValue "string", "app_name", "AAM"
            resValue "string", "keytool_sha1","C9:E6:E7:3D:4F:12:D2:7D:57:A9:A8:09:C2:21:75:92:AB:59:AD:95"
            resValue "string", "google_maps_api_key", "\"AIzaSyCBCxWAr6qPfU78eCzhBIetUToNoxlNOhM\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb1913575202110061\""
            applicationId "com.aamfinancegroup.aam"
        }

        rafco_dev {
            dimension "env"
            resValue "string", "app_name", "RAFCO(BETA)"
            resValue "string", "keytool_sha1","39:E4:16:8B:7C:CF:C1:2D:5B:4A:A1:B1:DB:E4:8F:F4:82:2A:BF:B1"
            resValue "string", "google_maps_api_key", "\"AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.rptn.rafcobeta"
        }

        rafco_prod {
            dimension "env"
            resValue "string", "app_name", "RAFCO"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            resValue "string", "google_maps_api_key", "\"AIzaSyC-RD0kTkFNbpL7d3yjChhoPYHsbRonuHA\""
//            resValue "string", "facebook_app_id","\"1913575202110061\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.rptn.rafco"
        }

        rplc_dev {
            dimension "env"
            resValue "string", "app_name", "RPLC(BETA)"
            resValue "string", "keytool_sha1","BE:B5:84:52:9B:56:AF:6E:4C:A5:24:54:E1:3E:99:49:57:67:27:F4"
            resValue "string", "google_maps_api_key", "\"AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w\""
//            resValue "string", "facebook_app_id","\"713437833144457\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.ruampattanaleasing.rplc_appbeta"
        }

        rplc_prod {
            dimension "env"
            resValue "string", "app_name", "RPLC"
            resValue "string", "keytool_sha1","9E:F7:CD:D2:2E:88:09:DC:C8:D4:CD:5B:AD:ED:B6:63:91:66:79:59"
            resValue "string", "google_maps_api_key", "\"AIzaSyDV7DZQAHVMO1KtuEOpM95DRsn8SBeXq1w\""
//            resValue "string", "facebook_app_id","\"713437833144457\""
//            resValue "string", "fb_login_protocol_scheme","\"fb713437833144457\""
            applicationId "com.ruampattanaleasing.rplc_app"
        }
    }

    //   signingConfigs {
    //     debug {
    //          signingConfig signingConfigs.release
    //     }

    // }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }

    }


}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "com.android.support:multidex:1.0.3"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.2.2'
    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation platform('com.google.firebase:firebase-bom:30.0.1')
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.google.android.gms:play-services-auth:20.6.0'

//    implementation 'com.facebook.android:facebook-login:15.0.1'

//    implementation 'com.facebook.android:facebook-login:latest.release'

}
