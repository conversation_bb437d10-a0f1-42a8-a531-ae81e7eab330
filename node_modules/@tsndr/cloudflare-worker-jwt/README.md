# Cloudflare Worker JWT

A lightweight JWT implementation with ZERO dependencies for Cloudflare Workers.


## Contents

- [Install](#install)
- [Examples](#examples)
- [Usage](#usage)
    - [Sign](#sign)
    - [Verify](#verify)
    - [Decode](#decode)


## Install

```
npm i @tsndr/cloudflare-worker-jwt
```


## Examples

### Basic Example

```typescript
async () => {
    import jwt from '@tsndr/cloudflare-worker-jwt'

    // Creating a token
    const token = await jwt.sign({ name: '<PERSON>', email: '<EMAIL>' }, 'secret')

    // Verifing token
    const isValid = await jwt.verify(token, 'secret')

    // Check for validity
    if (!isValid)
        return

    // Decoding token
    const { payload } = jwt.decode(token)
}
```

### Restrict Timeframe

```typescript
async () => {
    import jwt from '@tsndr/cloudflare-worker-jwt'

    // Creating a token
    const token = await jwt.sign({
        name: '<PERSON>',
        email: '<EMAIL>',
        nbf: Math.floor(Date.now() / 1000) + (60 * 60),      // Not before: Now + 1h
        exp: Math.floor(Date.now() / 1000) + (2 * (60 * 60)) // Expires: Now + 2h
    }, 'secret')

    // Verifing token
    const isValid = await jwt.verify(token, 'secret') // false

    // Check for validity
    if (!isValid)
        return

    // Decoding token
    const { payload } = jwt.decode(token) // { name: 'John Doe', email: '<EMAIL>', ... }
}
```

## Usage

- [Sign](#sign)
- [Verify](#verify)
- [Decode](#decode)

<hr>

### Sign
#### `jwt.sign(payload, secret, [options])`

Signs a payload and returns the token.

#### Arguments

Argument                 | Type               | Status   | Default     | Description
------------------------ | ------------------ | -------- | ----------- | -----------
`payload`                | `object`                            | required | -           | The payload object. To use `nbf` (Not Before) and/or `exp` (Expiration Time) add `nbf` and/or `exp` to the payload.
`secret`                 | `string`, `JsonWebKey`, `CryptoKey` | required | -           | A string which is used to sign the payload.
`options`                | `string`, `object`                  | optional | `HS256`     | Either the `algorithm` string or an object.
`options.algorithm`      | `string`                            | optional | `HS256`     | See [Available Algorithms](#available-algorithms)
`options.keyid`          | `string`                            | optional | `undefined` | The `keyid` or `kid` to be set in the header of the resulting JWT.

#### `return`
Returns token as a `string`.

<hr>

### Verify
#### `jwt.verify(token, secret, [options])`

Verifies the integrity of the token and returns a boolean value.

Argument                 | Type               | Status   | Default | Description
------------------------ | ------------------ | -------- | ------- | -----------
`token`                  | `string`                            | required | -       | The token string generated by `jwt.sign()`.
`secret`                 | `string`, `JsonWebKey`, `CryptoKey` | required | -       | The string which was used to sign the payload.
`options`                | `string`, `object`                  | optional | `HS256` | Either the `algorithm` string or an object.
`options.algorithm`      | `string`                            | optional | `HS256` | See [Available Algorithms](#available-algorithms)
`options.clockTolerance` | `number`                            | optional | `0`     | Clock tolerance in seconds, to help with slighly out of sync systems.
`options.throwError`     | `boolean`                           | optional | `false` | By default this we will only throw implementation errors, only set this to `true` if you want verification errors to be thrown as well.


#### `throws`
If `options.throwError` is `true` and the token is invalid, an error will be thrown.

#### `return`
Returns `true` if signature, `nbf` (if set) and `exp` (if set) are valid, otherwise returns `false`. 

<hr>

### Decode
#### `jwt.decode(token)`

Returns the payload **without** verifying the integrity of the token. Please use `jwt.verify()` first to keep your application secure!

Argument    | Type     | Status   | Default | Description
----------- | -------- | -------- | ------- | -----------
`token`     | `string` | required | -       | The token string generated by `jwt.sign()`.

#### `return`
Returns an `object` containing `header` and `payload`:
```javascript
{
    header: {
        alg: 'HS256',
        typ: 'JWT'
    },
    payload: {
        name: 'John Doe',
        email: '<EMAIL>'
    }
}
```

### Available Algorithms
 - ES256
 - ES384
 - ES512
 - HS256
 - HS384
 - HS512
 - RS256
 - RS384
 - RS512