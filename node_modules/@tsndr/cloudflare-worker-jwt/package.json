{"name": "@tsndr/cloudflare-worker-jwt", "version": "2.5.4", "description": "A lightweight JWT implementation with ZERO dependencies for Cloudflare Worker", "type": "module", "exports": "./index.js", "types": "index.d.ts", "engine": {"node": ">=18"}, "scripts": {"build": "tsc & esbuild --bundle --target=esnext --platform=neutral --outfile=index.js src/index.ts & wait", "test": "vitest"}, "repository": {"type": "git", "url": "git+https://github.com/tsndr/cloudflare-worker-jwt.git"}, "keywords": ["jwt", "token", "cloudflare", "worker", "cloudflare-worker"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/tsndr/cloudflare-worker-jwt/issues"}, "homepage": "https://github.com/tsndr/cloudflare-worker-jwt#readme", "devDependencies": {"@cloudflare/workers-types": "^4.20240925.0", "@edge-runtime/vm": "^4.0.3", "esbuild": "^0.24.0", "typescript": "^5.6.2", "vitest": "^2.1.1"}}